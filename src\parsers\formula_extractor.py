"""
数学公式提取器

负责从LaTeX文档中提取和分类数学公式。
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..core.exceptions import LaTeXParseError
from ..utils.logger import get_logger
from ..utils.error_handler import handle_errors

logger = get_logger(__name__)


class FormulaType(Enum):
    """公式类型枚举"""
    INLINE = "inline"           # 内联公式
    DISPLAY = "display"         # 显示公式
    EQUATION = "equation"       # 带编号的方程
    ALIGN = "align"            # 对齐环境
    EQNARRAY = "eqnarray"      # 方程组
    MATRIX = "matrix"          # 矩阵
    CASES = "cases"            # 分段函数


@dataclass
class Formula:
    """公式数据结构"""
    type: FormulaType           # 公式类型
    content: str               # 公式内容
    raw_content: str           # 原始内容（包括环境标记）
    start_pos: int             # 开始位置
    end_pos: int               # 结束位置
    label: Optional[str] = None # 标签
    number: Optional[str] = None # 编号
    environment: Optional[str] = None  # 环境名称
    options: Optional[str] = None      # 环境选项


class FormulaExtractor:
    """
    数学公式提取器
    
    从LaTeX文档中提取各种类型的数学公式。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化公式提取器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 编译正则表达式
        self._compile_patterns()
        
        # 数学环境映射
        self.math_environments = {
            'equation': FormulaType.EQUATION,
            'equation*': FormulaType.DISPLAY,
            'align': FormulaType.ALIGN,
            'align*': FormulaType.ALIGN,
            'eqnarray': FormulaType.EQNARRAY,
            'eqnarray*': FormulaType.EQNARRAY,
            'matrix': FormulaType.MATRIX,
            'pmatrix': FormulaType.MATRIX,
            'bmatrix': FormulaType.MATRIX,
            'Bmatrix': FormulaType.MATRIX,
            'vmatrix': FormulaType.MATRIX,
            'Vmatrix': FormulaType.MATRIX,
            'cases': FormulaType.CASES,
            'dcases': FormulaType.CASES,
        }
    
    def _compile_patterns(self) -> None:
        """编译正则表达式模式"""
        
        # 内联数学公式 $...$
        self.inline_math_pattern = re.compile(r'\$([^$\n]+)\$')
        
        # 显示数学公式 $$...$$
        self.display_math_pattern = re.compile(r'\$\$([^$]+?)\$\$', re.DOTALL)
        
        # 数学环境
        self.math_env_pattern = re.compile(
            r'\\begin\{(equation\*?|align\*?|eqnarray\*?|(?:[bBvV]?matrix)|(?:d?cases))\}'
            r'(.*?)'
            r'\\end\{\1\}',
            re.DOTALL
        )
        
        # 标签模式
        self.label_pattern = re.compile(r'\\label\{([^}]+)\}')
        
        # 编号模式
        self.tag_pattern = re.compile(r'\\tag\{([^}]+)\}')
        
        # 括号内的数学公式 \(...\)
        self.paren_math_pattern = re.compile(r'\\\(([^)]+?)\\\)', re.DOTALL)
        
        # 方括号内的数学公式 \[...\]
        self.bracket_math_pattern = re.compile(r'\\\[([^]]+?)\\\]', re.DOTALL)
    
    @handle_errors(context="提取数学公式")
    def extract_formulas(self, content: str) -> List[Dict[str, Any]]:
        """
        提取数学公式
        
        Args:
            content: 文档内容
            
        Returns:
            公式列表
        """
        logger.info("开始提取数学公式")
        
        formulas = []
        
        # 提取各种类型的公式
        formulas.extend(self._extract_inline_formulas(content))
        formulas.extend(self._extract_display_formulas(content))
        formulas.extend(self._extract_environment_formulas(content))
        formulas.extend(self._extract_paren_formulas(content))
        formulas.extend(self._extract_bracket_formulas(content))
        
        # 按位置排序
        formulas.sort(key=lambda f: f.start_pos)
        
        # 去重（处理重叠的匹配）
        formulas = self._remove_overlapping_formulas(formulas)
        
        logger.info(f"提取完成，共找到 {len(formulas)} 个公式")
        
        return [self._formula_to_dict(formula) for formula in formulas]
    
    def _extract_inline_formulas(self, content: str) -> List[Formula]:
        """提取内联公式"""
        formulas = []
        
        matches = self.inline_math_pattern.finditer(content)
        for match in matches:
            formula_content = match.group(1).strip()
            
            formula = Formula(
                type=FormulaType.INLINE,
                content=formula_content,
                raw_content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end()
            )
            
            formulas.append(formula)
        
        return formulas
    
    def _extract_display_formulas(self, content: str) -> List[Formula]:
        """提取显示公式"""
        formulas = []
        
        matches = self.display_math_pattern.finditer(content)
        for match in matches:
            formula_content = match.group(1).strip()
            
            # 检查是否包含标签
            label = self._extract_label(formula_content)
            if label:
                # 移除标签从内容中
                formula_content = self.label_pattern.sub('', formula_content).strip()
            
            formula = Formula(
                type=FormulaType.DISPLAY,
                content=formula_content,
                raw_content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                label=label
            )
            
            formulas.append(formula)
        
        return formulas
    
    def _extract_environment_formulas(self, content: str) -> List[Formula]:
        """提取环境公式"""
        formulas = []
        
        matches = self.math_env_pattern.finditer(content)
        for match in matches:
            env_name = match.group(1)
            formula_content = match.group(2).strip()
            
            # 确定公式类型
            formula_type = self.math_environments.get(env_name, FormulaType.DISPLAY)
            
            # 提取标签
            label = self._extract_label(formula_content)
            if label:
                formula_content = self.label_pattern.sub('', formula_content).strip()
            
            # 提取自定义编号
            tag = self._extract_tag(formula_content)
            if tag:
                formula_content = self.tag_pattern.sub('', formula_content).strip()
            
            formula = Formula(
                type=formula_type,
                content=formula_content,
                raw_content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                label=label,
                number=tag,
                environment=env_name
            )
            
            formulas.append(formula)
        
        return formulas
    
    def _extract_paren_formulas(self, content: str) -> List[Formula]:
        """提取括号内的公式"""
        formulas = []
        
        matches = self.paren_math_pattern.finditer(content)
        for match in matches:
            formula_content = match.group(1).strip()
            
            formula = Formula(
                type=FormulaType.INLINE,
                content=formula_content,
                raw_content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end()
            )
            
            formulas.append(formula)
        
        return formulas
    
    def _extract_bracket_formulas(self, content: str) -> List[Formula]:
        """提取方括号内的公式"""
        formulas = []
        
        matches = self.bracket_math_pattern.finditer(content)
        for match in matches:
            formula_content = match.group(1).strip()
            
            # 检查是否包含标签
            label = self._extract_label(formula_content)
            if label:
                formula_content = self.label_pattern.sub('', formula_content).strip()
            
            formula = Formula(
                type=FormulaType.DISPLAY,
                content=formula_content,
                raw_content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                label=label
            )
            
            formulas.append(formula)
        
        return formulas
    
    def _extract_label(self, content: str) -> Optional[str]:
        """提取标签"""
        match = self.label_pattern.search(content)
        return match.group(1) if match else None
    
    def _extract_tag(self, content: str) -> Optional[str]:
        """提取自定义编号"""
        match = self.tag_pattern.search(content)
        return match.group(1) if match else None
    
    def _remove_overlapping_formulas(self, formulas: List[Formula]) -> List[Formula]:
        """移除重叠的公式"""
        if not formulas:
            return formulas
        
        # 按开始位置排序
        formulas.sort(key=lambda f: f.start_pos)
        
        result = []
        last_end = -1
        
        for formula in formulas:
            # 如果当前公式不与前一个重叠，则添加
            if formula.start_pos >= last_end:
                result.append(formula)
                last_end = formula.end_pos
            else:
                # 如果重叠，选择更长的公式
                if result and formula.end_pos > result[-1].end_pos:
                    result[-1] = formula
                    last_end = formula.end_pos
        
        return result
    
    def _formula_to_dict(self, formula: Formula) -> Dict[str, Any]:
        """将公式对象转换为字典"""
        return {
            'type': formula.type.value,
            'content': formula.content,
            'raw_content': formula.raw_content,
            'start_pos': formula.start_pos,
            'end_pos': formula.end_pos,
            'label': formula.label,
            'number': formula.number,
            'environment': formula.environment,
            'options': formula.options
        }
    
    def classify_formula_complexity(self, formula_content: str) -> str:
        """
        分类公式复杂度
        
        Args:
            formula_content: 公式内容
            
        Returns:
            复杂度级别 (simple/medium/complex)
        """
        # 简单指标计算
        length = len(formula_content)
        
        # 复杂结构计数
        complex_patterns = [
            r'\\frac',      # 分数
            r'\\sqrt',      # 根号
            r'\\sum',       # 求和
            r'\\int',       # 积分
            r'\\prod',      # 乘积
            r'\\lim',       # 极限
            r'\\matrix',    # 矩阵
            r'\\begin',     # 环境
        ]
        
        complex_count = sum(len(re.findall(pattern, formula_content)) 
                          for pattern in complex_patterns)
        
        # 嵌套层次
        brace_depth = self._calculate_brace_depth(formula_content)
        
        # 分类逻辑
        if length < 20 and complex_count == 0 and brace_depth <= 2:
            return "simple"
        elif length < 100 and complex_count <= 3 and brace_depth <= 4:
            return "medium"
        else:
            return "complex"
    
    def _calculate_brace_depth(self, content: str) -> int:
        """计算括号嵌套深度"""
        max_depth = 0
        current_depth = 0
        
        for char in content:
            if char == '{':
                current_depth += 1
                max_depth = max(max_depth, current_depth)
            elif char == '}':
                current_depth = max(0, current_depth - 1)
        
        return max_depth
