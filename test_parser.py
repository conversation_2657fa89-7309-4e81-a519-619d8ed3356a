#!/usr/bin/env python3
"""
简单的测试脚本，用于验证LaTeX解析器功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 直接导入需要的模块，避免循环导入
from parsers.latex_parser import LaTeXParser
from utils.config_manager import ConfigManager
from utils.logger import setup_logger

def test_latex_parser():
    """测试LaTeX解析器"""
    
    # 设置日志
    setup_logger({'level': 'DEBUG', 'console_output': True})
    
    # 创建配置管理器
    config_manager = ConfigManager()
    config = config_manager.get_config()
    
    # 创建解析器
    parser = LaTeXParser(config.latex_parser)
    
    # 测试解析示例文件
    try:
        print("正在解析示例LaTeX文件...")
        document = parser.parse_file("examples/input/sample.tex")
        
        print(f"\n解析结果:")
        print(f"文档标题: {document.title}")
        print(f"作者: {document.author}")
        print(f"文档类: {document.document_class}")
        print(f"包数量: {len(document.packages)}")
        print(f"章节数量: {len(document.sections)}")
        print(f"公式数量: {len(document.formulas)}")
        print(f"表格数量: {len(document.tables)}")
        print(f"图形数量: {len(document.figures)}")
        
        # 显示前几个章节
        print(f"\n前3个章节:")
        for i, section in enumerate(document.sections[:3]):
            print(f"  {i+1}. {section['title']} (级别: {section['level']})")
        
        # 显示前几个公式
        print(f"\n前5个公式:")
        for i, formula in enumerate(document.formulas[:5]):
            print(f"  {i+1}. 类型: {formula['type']}, 内容: {formula['content'][:50]}...")
        
        print("\n✅ LaTeX解析器测试成功!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_formula_converter():
    """测试公式转换器"""
    
    print("\n" + "="*50)
    print("测试公式转换器")
    print("="*50)
    
    try:
        from converters.formula_converter import FormulaConverter
        from converters.formula_classifier import FormulaClassifier
        
        # 创建转换器
        converter = FormulaConverter()
        classifier = FormulaClassifier()
        
        # 测试公式
        test_formulas = [
            r"x^2 + y^2 = z^2",
            r"\frac{a}{b} + \frac{c}{d}",
            r"\int_0^{\infty} e^{-x} dx",
            r"\sum_{i=1}^{n} x_i",
            r"\sqrt{a^2 + b^2}",
            r"\alpha + \beta = \gamma"
        ]
        
        print("测试公式分类:")
        for i, formula in enumerate(test_formulas):
            complexity = classifier.classify_complexity(formula)
            formula_type = classifier.classify_formula_type(formula)
            print(f"  {i+1}. {formula}")
            print(f"     复杂度: {complexity}, 类型: {formula_type}")
        
        print("\n测试公式转换:")
        for i, formula in enumerate(test_formulas[:3]):  # 只测试前3个
            print(f"  转换公式: {formula}")
            result = converter.convert_formula(formula, "inline")
            if result.success:
                print(f"    ✅ 转换成功")
                print(f"    方法: {result.conversion_method}")
            else:
                print(f"    ❌ 转换失败: {result.error_message}")
        
        print("\n✅ 公式转换器测试完成!")
        
    except Exception as e:
        print(f"❌ 公式转换器测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始测试LaTeX到Word转换系统")
    print("="*50)
    
    test_latex_parser()
    test_formula_converter()
    
    print("\n" + "="*50)
    print("测试完成!")
