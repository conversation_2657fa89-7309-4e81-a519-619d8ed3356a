#!/usr/bin/env python3
"""
主程序入口

LaTeX到Word转换系统的主程序入口。
"""

import sys
import argparse
from pathlib import Path
from typing import Optional

from .core.converter import LatexToWordConverter
from .utils.config_manager import ConfigManager
from .utils.logger import setup_logger, get_logger

logger = get_logger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="LaTeX到Word转换系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python -m src.main input.tex output.docx
  python -m src.main input.tex output.docx --template academic
  python -m src.main --preview input.tex
        """
    )
    
    parser.add_argument(
        'input_file',
        help='输入的LaTeX文件路径'
    )
    
    parser.add_argument(
        'output_file',
        nargs='?',
        help='输出的Word文件路径'
    )
    
    parser.add_argument(
        '--template',
        default='default',
        help='使用的样式模板 (default: default)'
    )
    
    parser.add_argument(
        '--config',
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--preview',
        action='store_true',
        help='只生成预览，不执行转换'
    )
    
    parser.add_argument(
        '--validate',
        action='store_true',
        help='只验证输入文件，不执行转换'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='详细输出'
    )
    
    parser.add_argument(
        '--quiet',
        action='store_true',
        help='静默模式'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.quiet:
        log_level = 'ERROR'
    elif args.verbose:
        log_level = 'DEBUG'
    else:
        log_level = 'INFO'
    
    setup_logger({
        'level': log_level,
        'console_output': True
    })
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        if args.config:
            config_manager.load_config(args.config)
        
        # 创建转换器
        converter = LatexToWordConverter(config_manager)
        
        # 验证输入文件
        input_path = Path(args.input_file)
        if not input_path.exists():
            logger.error(f"输入文件不存在: {input_path}")
            return 1
        
        logger.info(f"处理文件: {input_path}")
        
        # 验证模式
        if args.validate:
            logger.info("验证输入文件...")
            validation_result = converter.validate_input(input_path)
            
            if validation_result['valid']:
                logger.info("✅ 文件验证通过")
                if validation_result['warnings']:
                    logger.warning("警告:")
                    for warning in validation_result['warnings']:
                        logger.warning(f"  - {warning}")
                return 0
            else:
                logger.error("❌ 文件验证失败:")
                for error in validation_result['errors']:
                    logger.error(f"  - {error}")
                return 1
        
        # 预览模式
        if args.preview:
            logger.info("生成转换预览...")
            preview = converter.preview_conversion(input_path)
            
            if 'error' in preview:
                logger.error(f"预览生成失败: {preview['error']}")
                return 1
            
            # 显示预览信息
            print_preview(preview)
            return 0
        
        # 转换模式
        if not args.output_file:
            logger.error("转换模式需要指定输出文件")
            return 1
        
        output_path = Path(args.output_file)
        
        logger.info("开始转换...")
        result = converter.convert(
            input_path,
            output_path,
            options={'template': args.template}
        )
        
        if result['success']:
            logger.info("✅ 转换成功!")
            logger.info(f"输出文件: {result['output_file']}")
            
            # 显示统计信息
            if not args.quiet:
                print_conversion_result(result)
            
            return 0
        else:
            logger.error(f"❌ 转换失败: {result.get('error', '未知错误')}")
            return 1
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        return 1
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


def print_preview(preview: dict) -> None:
    """打印预览信息"""
    print("\n" + "="*50)
    print("转换预览")
    print("="*50)
    
    doc_info = preview.get('document_info', {})
    print(f"标题: {doc_info.get('title', '无')}")
    print(f"作者: {doc_info.get('author', '无')}")
    print(f"文档类: {doc_info.get('document_class', '无')}")
    print(f"章节数: {doc_info.get('sections', 0)}")
    print(f"公式数: {doc_info.get('formulas', 0)}")
    print(f"表格数: {doc_info.get('tables', 0)}")
    print(f"图形数: {doc_info.get('figures', 0)}")
    
    # 显示章节结构
    structure = preview.get('structure_preview', [])
    if structure:
        print(f"\n章节结构:")
        for section in structure:
            indent = "  " * (section['level'] - 1)
            print(f"{indent}- {section['title']} (级别 {section['level']})")
    
    # 显示公式分析
    formula_analysis = preview.get('formula_analysis', [])
    if formula_analysis:
        print(f"\n公式分析 (前{len(formula_analysis)}个):")
        for i, analysis in enumerate(formula_analysis):
            print(f"  {i+1}. {analysis['content']}")
            print(f"     类型: {analysis['type']}, 复杂度: {analysis['complexity']}")
            print(f"     转换方法: {analysis['conversion_method']}")
    
    # 显示复杂度分布
    complexity_dist = preview.get('complexity_distribution', {})
    if complexity_dist:
        print(f"\n公式复杂度分布:")
        for complexity, count in complexity_dist.items():
            print(f"  {complexity}: {count}")
    
    print(f"\n估计处理时间: {preview.get('estimated_processing_time', 0):.2f} 秒")


def print_conversion_result(result: dict) -> None:
    """打印转换结果"""
    print("\n" + "="*50)
    print("转换结果")
    print("="*50)
    
    doc_info = result.get('document_info', {})
    print(f"文档信息:")
    print(f"  标题: {doc_info.get('title', '无')}")
    print(f"  章节数: {doc_info.get('sections', 0)}")
    print(f"  公式数: {doc_info.get('formulas', 0)}")
    print(f"  表格数: {doc_info.get('tables', 0)}")
    print(f"  图形数: {doc_info.get('figures', 0)}")
    
    formula_info = result.get('formula_conversion', {})
    print(f"\n公式转换:")
    print(f"  总数: {formula_info.get('total', 0)}")
    print(f"  成功: {formula_info.get('successful', 0)}")
    print(f"  失败: {formula_info.get('failed', 0)}")
    
    if formula_info.get('total', 0) > 0:
        success_rate = formula_info.get('successful', 0) / formula_info.get('total', 1) * 100
        print(f"  成功率: {success_rate:.1f}%")
    
    print(f"\n处理时间: {result.get('processing_time', 0):.2f} 秒")


def test_conversion():
    """测试转换功能"""
    print("LaTeX到Word转换系统测试")
    print("="*40)
    
    try:
        # 创建转换器
        converter = LatexToWordConverter()
        
        # 测试文件
        input_file = "examples/input/sample.tex"
        output_file = "examples/output/test_output.docx"
        
        if not Path(input_file).exists():
            print(f"❌ 测试文件不存在: {input_file}")
            return
        
        print(f"测试转换: {input_file} -> {output_file}")
        
        # 执行转换
        result = converter.convert(input_file, output_file)
        
        if result['success']:
            print("✅ 测试转换成功!")
            print_conversion_result(result)
        else:
            print(f"❌ 测试转换失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    sys.exit(main())
