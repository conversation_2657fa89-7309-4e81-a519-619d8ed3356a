from __future__ import annotations
INTERPRETERS = {
    'ash': {'shell', 'ash'},
    'awk': {'awk'},
    'bash': {'shell', 'bash'},
    'bats': {'shell', 'bash', 'bats'},
    'cbsd': {'shell', 'cbsd'},
    'csh': {'shell', 'csh'},
    'dash': {'shell', 'dash'},
    'expect': {'expect'},
    'ksh': {'shell', 'ksh'},
    'node': {'javascript'},
    'nodejs': {'javascript'},
    'perl': {'perl'},
    'php': {'php'},
    'php7': {'php', 'php7'},
    'php8': {'php', 'php8'},
    'python': {'python'},
    'python2': {'python', 'python2'},
    'python3': {'python', 'python3'},
    'ruby': {'ruby'},
    'sh': {'shell', 'sh'},
    'tcsh': {'shell', 'tcsh'},
    'zsh': {'shell', 'zsh'},
}
