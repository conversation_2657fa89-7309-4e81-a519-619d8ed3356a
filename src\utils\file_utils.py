"""
文件工具模块

提供文件操作相关的工具函数。
"""

import os
import shutil
import chardet
from pathlib import Path
from typing import Optional, Union, List, Dict, Any

from ..core.exceptions import FileProcessingError
from .logger import get_logger

logger = get_logger(__name__)


class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def detect_encoding(file_path: Union[str, Path]) -> str:
        """
        检测文件编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件编码
            
        Raises:
            FileProcessingError: 文件处理失败
        """
        file_path = Path(file_path)
        
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                result = chardet.detect(raw_data)
                encoding = result.get('encoding', 'utf-8')
                confidence = result.get('confidence', 0)
                
                logger.debug(f"文件 {file_path} 编码检测结果: {encoding} (置信度: {confidence:.2f})")
                
                # 如果置信度太低，使用默认编码
                if confidence < 0.7:
                    logger.warning(f"文件编码检测置信度较低，使用默认编码 utf-8")
                    return 'utf-8'
                
                return encoding
                
        except Exception as e:
            raise FileProcessingError(
                f"文件编码检测失败: {e}",
                file_path=str(file_path),
                operation="detect_encoding"
            )
    
    @staticmethod
    def read_text_file(
        file_path: Union[str, Path],
        encoding: Optional[str] = None
    ) -> str:
        """
        读取文本文件
        
        Args:
            file_path: 文件路径
            encoding: 文件编码，如果为None则自动检测
            
        Returns:
            文件内容
            
        Raises:
            FileProcessingError: 文件处理失败
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileProcessingError(
                f"文件不存在: {file_path}",
                file_path=str(file_path),
                operation="read_text_file"
            )
        
        try:
            # 如果没有指定编码，自动检测
            if encoding is None:
                encoding = FileUtils.detect_encoding(file_path)
            
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
                
            logger.debug(f"成功读取文件: {file_path} (编码: {encoding})")
            return content
            
        except Exception as e:
            raise FileProcessingError(
                f"文件读取失败: {e}",
                file_path=str(file_path),
                operation="read_text_file"
            )
    
    @staticmethod
    def write_text_file(
        file_path: Union[str, Path],
        content: str,
        encoding: str = 'utf-8',
        create_dirs: bool = True
    ) -> None:
        """
        写入文本文件
        
        Args:
            file_path: 文件路径
            content: 文件内容
            encoding: 文件编码
            create_dirs: 是否创建目录
            
        Raises:
            FileProcessingError: 文件处理失败
        """
        file_path = Path(file_path)
        
        try:
            # 创建目录
            if create_dirs:
                file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
                
            logger.debug(f"成功写入文件: {file_path} (编码: {encoding})")
            
        except Exception as e:
            raise FileProcessingError(
                f"文件写入失败: {e}",
                file_path=str(file_path),
                operation="write_text_file"
            )
    
    @staticmethod
    def copy_file(
        src_path: Union[str, Path],
        dst_path: Union[str, Path],
        create_dirs: bool = True
    ) -> None:
        """
        复制文件
        
        Args:
            src_path: 源文件路径
            dst_path: 目标文件路径
            create_dirs: 是否创建目录
            
        Raises:
            FileProcessingError: 文件处理失败
        """
        src_path = Path(src_path)
        dst_path = Path(dst_path)
        
        if not src_path.exists():
            raise FileProcessingError(
                f"源文件不存在: {src_path}",
                file_path=str(src_path),
                operation="copy_file"
            )
        
        try:
            # 创建目录
            if create_dirs:
                dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(src_path, dst_path)
            logger.debug(f"成功复制文件: {src_path} -> {dst_path}")
            
        except Exception as e:
            raise FileProcessingError(
                f"文件复制失败: {e}",
                file_path=str(src_path),
                operation="copy_file"
            )
    
    @staticmethod
    def ensure_directory(dir_path: Union[str, Path]) -> Path:
        """
        确保目录存在
        
        Args:
            dir_path: 目录路径
            
        Returns:
            目录路径对象
            
        Raises:
            FileProcessingError: 目录创建失败
        """
        dir_path = Path(dir_path)
        
        try:
            dir_path.mkdir(parents=True, exist_ok=True)
            return dir_path
            
        except Exception as e:
            raise FileProcessingError(
                f"目录创建失败: {e}",
                file_path=str(dir_path),
                operation="ensure_directory"
            )
    
    @staticmethod
    def get_file_info(file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
            
        Raises:
            FileProcessingError: 文件处理失败
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileProcessingError(
                f"文件不存在: {file_path}",
                file_path=str(file_path),
                operation="get_file_info"
            )
        
        try:
            stat = file_path.stat()
            
            return {
                "path": str(file_path),
                "name": file_path.name,
                "stem": file_path.stem,
                "suffix": file_path.suffix,
                "size": stat.st_size,
                "created": stat.st_ctime,
                "modified": stat.st_mtime,
                "is_file": file_path.is_file(),
                "is_dir": file_path.is_dir(),
            }
            
        except Exception as e:
            raise FileProcessingError(
                f"获取文件信息失败: {e}",
                file_path=str(file_path),
                operation="get_file_info"
            )
    
    @staticmethod
    def find_files(
        directory: Union[str, Path],
        pattern: str = "*",
        recursive: bool = True
    ) -> List[Path]:
        """
        查找文件
        
        Args:
            directory: 搜索目录
            pattern: 文件模式
            recursive: 是否递归搜索
            
        Returns:
            文件路径列表
            
        Raises:
            FileProcessingError: 文件搜索失败
        """
        directory = Path(directory)
        
        if not directory.exists():
            raise FileProcessingError(
                f"目录不存在: {directory}",
                file_path=str(directory),
                operation="find_files"
            )
        
        try:
            if recursive:
                files = list(directory.rglob(pattern))
            else:
                files = list(directory.glob(pattern))
            
            # 只返回文件，不包括目录
            files = [f for f in files if f.is_file()]
            
            logger.debug(f"在目录 {directory} 中找到 {len(files)} 个文件")
            return files
            
        except Exception as e:
            raise FileProcessingError(
                f"文件搜索失败: {e}",
                file_path=str(directory),
                operation="find_files"
            )
