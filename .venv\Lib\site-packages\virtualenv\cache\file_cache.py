from __future__ import annotations

from typing import TYPE_CHECKING, Callable

from virtualenv.cache import Cache

if TYPE_CHECKING:
    from pathlib import Path

    from virtualenv.app_data.base import ContentStore


class FileCache(Cache):
    def __init__(self, store_factory: Callable[[Path], ContentStore], clearer: Callable[[], None] | None) -> None:
        self.store_factory = store_factory
        self.clearer = clearer

    def get(self, key: Path) -> dict | None:
        """Get a value from the file cache."""
        result, store = None, self.store_factory(key)
        with store.locked():
            if store.exists():
                result = store.read()
        return result

    def set(self, key: Path, value: dict) -> None:
        """Set a value in the file cache."""
        store = self.store_factory(key)
        with store.locked():
            store.write(value)

    def remove(self, key: Path) -> None:
        """Remove a value from the file cache."""
        store = self.store_factory(key)
        with store.locked():
            if store.exists():
                store.remove()

    def clear(self) -> None:
        """Clear the entire file cache."""
        if self.clearer is not None:
            self.clearer()


__all__ = [
    "FileCache",
]
