#!/usr/bin/env python3
"""
简单测试脚本
"""

import re
import os
from pathlib import Path

def test_basic_latex_parsing():
    """测试基本的LaTeX解析功能"""
    print("测试基本LaTeX解析功能")
    print("="*40)
    
    # 读取示例文件
    sample_file = Path("examples/input/sample.tex")
    if not sample_file.exists():
        print("❌ 示例文件不存在")
        return
    
    with open(sample_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"✅ 成功读取文件，长度: {len(content)} 字符")
    
    # 基本解析测试
    # 查找文档类
    doc_class_pattern = re.compile(r'\\documentclass(?:\[([^\]]*)\])?\{([^}]+)\}')
    doc_class_match = doc_class_pattern.search(content)
    if doc_class_match:
        print(f"✅ 文档类: {doc_class_match.group(2)}")
    
    # 查找标题
    title_pattern = re.compile(r'\\title\{([^}]+)\}')
    title_match = title_pattern.search(content)
    if title_match:
        print(f"✅ 标题: {title_match.group(1)}")
    
    # 查找作者
    author_pattern = re.compile(r'\\author\{([^}]+)\}')
    author_match = author_pattern.search(content)
    if author_match:
        print(f"✅ 作者: {author_match.group(1)}")
    
    # 查找章节
    section_pattern = re.compile(r'\\((?:sub)*section)\{([^}]+)\}')
    sections = section_pattern.findall(content)
    print(f"✅ 找到 {len(sections)} 个章节")
    for i, (level, title) in enumerate(sections[:5]):
        print(f"   {i+1}. {level}: {title}")
    
    # 查找数学公式
    inline_math = re.findall(r'\$([^$]+)\$', content)
    display_math = re.findall(r'\$\$([^$]+)\$\$', content, re.DOTALL)
    equation_env = re.findall(r'\\begin\{equation\}(.*?)\\end\{equation\}', content, re.DOTALL)
    
    total_formulas = len(inline_math) + len(display_math) + len(equation_env)
    print(f"✅ 找到 {total_formulas} 个数学公式")
    print(f"   内联公式: {len(inline_math)}")
    print(f"   显示公式: {len(display_math)}")
    print(f"   方程环境: {len(equation_env)}")
    
    # 显示前几个公式
    print("\n前5个内联公式:")
    for i, formula in enumerate(inline_math[:5]):
        print(f"   {i+1}. {formula}")
    
    print("\n前3个方程:")
    for i, formula in enumerate(equation_env[:3]):
        clean_formula = formula.strip().replace('\n', ' ')
        print(f"   {i+1}. {clean_formula[:100]}...")

def test_formula_complexity():
    """测试公式复杂度分析"""
    print("\n" + "="*40)
    print("测试公式复杂度分析")
    print("="*40)
    
    test_formulas = [
        ("简单公式", r"x + y = z"),
        ("二次方程", r"ax^2 + bx + c = 0"),
        ("分数", r"\frac{a}{b} + \frac{c}{d}"),
        ("根号", r"\sqrt{a^2 + b^2}"),
        ("积分", r"\int_0^{\infty} e^{-x} dx"),
        ("求和", r"\sum_{i=1}^{n} x_i"),
        ("复杂公式", r"\frac{\partial^2 u}{\partial t^2} = c^2 \nabla^2 u"),
        ("矩阵", r"\begin{matrix} a & b \\ c & d \end{matrix}"),
    ]
    
    for name, formula in test_formulas:
        complexity = analyze_formula_complexity(formula)
        print(f"✅ {name}: {complexity}")
        print(f"   公式: {formula}")

def analyze_formula_complexity(formula):
    """简单的公式复杂度分析"""
    score = 0
    
    # 长度因子
    score += len(formula) * 0.1
    
    # 复杂结构
    if r'\frac' in formula:
        score += 2
    if r'\sqrt' in formula:
        score += 1.5
    if r'\int' in formula:
        score += 3
    if r'\sum' in formula:
        score += 2.5
    if r'\begin{' in formula:
        score += 4
    if '^' in formula:
        score += 0.5
    if '_' in formula:
        score += 0.5
    
    # 希腊字母
    greek_letters = ['alpha', 'beta', 'gamma', 'delta', 'epsilon', 'theta', 'lambda', 'mu', 'pi', 'sigma', 'omega']
    for letter in greek_letters:
        if f'\\{letter}' in formula:
            score += 0.2
    
    # 分类
    if score < 2:
        return "简单"
    elif score < 5:
        return "中等"
    elif score < 10:
        return "复杂"
    else:
        return "非常复杂"

def test_config_loading():
    """测试配置加载"""
    print("\n" + "="*40)
    print("测试配置加载")
    print("="*40)
    
    config_files = [
        "config/default_config.yaml",
        "config/formula_mapping.yaml",
        "config/style_templates.yaml"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ 配置文件存在: {config_file}")
            try:
                import yaml
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                print(f"   配置项数量: {len(config_data) if isinstance(config_data, dict) else 'N/A'}")
            except Exception as e:
                print(f"   ⚠️ 配置文件读取失败: {e}")
        else:
            print(f"❌ 配置文件不存在: {config_file}")

def test_directory_structure():
    """测试目录结构"""
    print("\n" + "="*40)
    print("测试项目目录结构")
    print("="*40)
    
    required_dirs = [
        "src",
        "src/core",
        "src/parsers", 
        "src/converters",
        "src/generators",
        "src/integrations",
        "src/utils",
        "config",
        "tests",
        "examples",
        "examples/input",
        "examples/output"
    ]
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ 目录存在: {dir_path}")
        else:
            print(f"❌ 目录不存在: {dir_path}")

if __name__ == "__main__":
    print("LaTeX到Word转换系统 - 简单测试")
    print("="*50)
    
    test_directory_structure()
    test_config_loading()
    test_basic_latex_parsing()
    test_formula_complexity()
    
    print("\n" + "="*50)
    print("测试完成!")
    print("\n下一步:")
    print("1. 安装依赖: pip install -r requirements.txt")
    print("2. 运行完整测试")
    print("3. 开发Word文档生成模块")
    print("4. 集成MathType")
