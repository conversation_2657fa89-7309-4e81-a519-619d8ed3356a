# LaTeX到Word转换系统默认配置文件

# 输入输出设置
input_encoding: "utf-8"
output_encoding: "utf-8"

# LaTeX解析器设置
latex_parser:
  strict_mode: false                    # 严格模式，是否严格解析LaTeX语法
  ignore_unknown_commands: true         # 忽略未知命令
  preserve_comments: false              # 保留注释
  max_recursion_depth: 100             # 最大递归深度
  
# 公式转换设置
formula_conversion:
  inline_formula_style: "inline"       # 内联公式样式
  display_formula_style: "display"     # 显示公式样式
  preserve_equation_numbers: true      # 保留公式编号
  convert_symbols: true                # 转换特殊符号
  use_unicode: true                    # 使用Unicode字符
  mathtype_format: "omml"              # MathType格式 (omml/mathml)
  
# MathType集成设置
mathtype:
  installation_path: "D:\\Softwares\\mathtype"  # MathType安装路径
  timeout: 30                          # 超时时间（秒）
  retry_count: 3                       # 重试次数
  use_com_interface: true              # 使用COM接口
  temp_directory: "temp/mathtype"      # 临时文件目录
  
# Word文档生成设置
word_generation:
  # 页面设置
  page_size: "A4"                      # 页面大小
  margins:                             # 页边距（厘米）
    top: 2.5
    bottom: 2.5
    left: 2.5
    right: 2.5
  
  # 字体设置
  font_family: "Times New Roman"       # 默认字体
  font_size: 12                        # 默认字号
  line_spacing: 1.5                    # 行间距
  
  # 标题样式
  heading_styles:
    h1:
      font_size: 16
      bold: true
      space_before: 12
      space_after: 6
    h2:
      font_size: 14
      bold: true
      space_before: 10
      space_after: 5
    h3:
      font_size: 12
      bold: true
      space_before: 8
      space_after: 4
  
  # 段落样式
  paragraph_styles:
    normal:
      font_size: 12
      space_after: 6
    quote:
      font_size: 11
      italic: true
      left_indent: 1.0
  
  # 公式样式
  formula_styles:
    inline:
      font_size: 12
    display:
      font_size: 12
      space_before: 6
      space_after: 6
      alignment: "center"

# 日志设置
logging:
  level: "INFO"                        # 日志级别 (DEBUG/INFO/WARNING/ERROR)
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/converter.log"      # 日志文件路径
  max_file_size: "10MB"                # 最大文件大小
  backup_count: 5                      # 备份文件数量
  console_output: true                 # 控制台输出

# 性能设置
performance:
  max_memory_usage: "1GB"              # 最大内存使用
  chunk_size: 1000                     # 处理块大小
  parallel_processing: false          # 并行处理
  max_workers: 4                       # 最大工作线程数

# 错误处理设置
error_handling:
  continue_on_error: true              # 遇到错误时继续处理
  max_errors: 10                       # 最大错误数
  error_log_file: "logs/errors.log"   # 错误日志文件
  
# 调试设置
debug:
  save_intermediate_files: false      # 保存中间文件
  verbose_output: false               # 详细输出
  profile_performance: false          # 性能分析
