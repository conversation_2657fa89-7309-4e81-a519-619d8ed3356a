"""
MathType格式化器

负责将解析后的公式结构转换为MathType格式。
"""

from typing import Dict, List, Any, Optional
from xml.etree.ElementTree import Element, SubElement, tostring
import xml.etree.ElementTree as ET

from ..core.exceptions import FormulaConversionError
from ..utils.logger import get_logger
from ..utils.error_handler import handle_errors

logger = get_logger(__name__)


class MathTypeFormatter:
    """
    MathType格式化器
    
    将解析后的公式结构转换为MathType OMML格式。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化MathType格式化器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.format_type = self.config.get('mathtype_format', 'omml')
        
        # OMML命名空间
        self.omml_ns = {
            'm': 'http://schemas.openxmlformats.org/officeDocument/2006/math'
        }
        
        # 初始化符号映射
        self._init_symbol_mappings()
    
    def _init_symbol_mappings(self) -> None:
        """初始化符号映射"""
        
        # 希腊字母映射
        self.greek_mappings = {
            'alpha': 'α', 'beta': 'β', 'gamma': 'γ', 'delta': 'δ',
            'epsilon': 'ε', 'varepsilon': 'ε', 'zeta': 'ζ', 'eta': 'η',
            'theta': 'θ', 'vartheta': 'ϑ', 'iota': 'ι', 'kappa': 'κ',
            'lambda': 'λ', 'mu': 'μ', 'nu': 'ν', 'xi': 'ξ',
            'pi': 'π', 'varpi': 'ϖ', 'rho': 'ρ', 'varrho': 'ϱ',
            'sigma': 'σ', 'varsigma': 'ς', 'tau': 'τ', 'upsilon': 'υ',
            'phi': 'φ', 'varphi': 'ϕ', 'chi': 'χ', 'psi': 'ψ', 'omega': 'ω',
            'Gamma': 'Γ', 'Delta': 'Δ', 'Theta': 'Θ', 'Lambda': 'Λ',
            'Xi': 'Ξ', 'Pi': 'Π', 'Sigma': 'Σ', 'Upsilon': 'Υ',
            'Phi': 'Φ', 'Psi': 'Ψ', 'Omega': 'Ω'
        }
        
        # 运算符映射
        self.operator_mappings = {
            'pm': '±', 'mp': '∓', 'times': '×', 'div': '÷',
            'cdot': '·', 'ast': '∗', 'star': '⋆', 'circ': '∘',
            'bullet': '∙', 'leq': '≤', 'geq': '≥', 'neq': '≠',
            'approx': '≈', 'equiv': '≡', 'sim': '∼', 'simeq': '≃',
            'cong': '≅', 'propto': '∝', 'parallel': '∥', 'perp': '⊥'
        }
        
        # 集合符号映射
        self.set_mappings = {
            'in': '∈', 'notin': '∉', 'subset': '⊂', 'supset': '⊃',
            'subseteq': '⊆', 'supseteq': '⊇', 'cup': '∪', 'cap': '∩',
            'emptyset': '∅', 'varnothing': '∅'
        }
        
        # 箭头映射
        self.arrow_mappings = {
            'leftarrow': '←', 'rightarrow': '→', 'leftrightarrow': '↔',
            'Leftarrow': '⇐', 'Rightarrow': '⇒', 'Leftrightarrow': '⇔',
            'uparrow': '↑', 'downarrow': '↓', 'updownarrow': '↕'
        }
    
    @handle_errors(context="格式化MathType公式")
    def format_formula(self, parsed_formula: Dict[str, Any]) -> str:
        """
        格式化公式为MathType格式
        
        Args:
            parsed_formula: 解析后的公式对象
            
        Returns:
            MathType格式的公式字符串
        """
        logger.debug("开始格式化公式为MathType格式")
        
        if self.format_type == 'omml':
            return self._format_to_omml(parsed_formula)
        elif self.format_type == 'mathml':
            return self._format_to_mathml(parsed_formula)
        else:
            raise FormulaConversionError(
                f"不支持的MathType格式: {self.format_type}",
                conversion_stage="format_selection"
            )
    
    def _format_to_omml(self, parsed_formula: Dict[str, Any]) -> str:
        """格式化为OMML格式"""
        
        # 创建根元素
        root = Element('m:oMath', attrib={'xmlns:m': self.omml_ns['m']})
        
        # 处理公式元素
        elements = parsed_formula.get('elements', [])
        for element in elements:
            omml_element = self._convert_element_to_omml(element)
            if omml_element is not None:
                root.append(omml_element)
        
        # 转换为字符串
        return ET.tostring(root, encoding='unicode', method='xml')
    
    def _format_to_mathml(self, parsed_formula: Dict[str, Any]) -> str:
        """格式化为MathML格式"""
        
        # 创建根元素
        root = Element('math', attrib={'xmlns': 'http://www.w3.org/1998/Math/MathML'})
        
        # 根据公式类型选择容器
        formula_type = parsed_formula.get('formula_type', 'inline')
        if formula_type == 'display':
            container = SubElement(root, 'mstyle', attrib={'displaystyle': 'true'})
        else:
            container = root
        
        # 处理公式元素
        elements = parsed_formula.get('elements', [])
        for element in elements:
            mathml_element = self._convert_element_to_mathml(element)
            if mathml_element is not None:
                container.append(mathml_element)
        
        # 转换为字符串
        return ET.tostring(root, encoding='unicode', method='xml')
    
    def _convert_element_to_omml(self, element: Dict[str, Any]) -> Optional[Element]:
        """将公式元素转换为OMML元素"""
        
        element_type = element.get('type')
        
        if element_type == 'fraction':
            return self._create_omml_fraction(element)
        elif element_type == 'superscript':
            return self._create_omml_superscript(element)
        elif element_type == 'subscript':
            return self._create_omml_subscript(element)
        elif element_type == 'sqrt':
            return self._create_omml_sqrt(element)
        elif element_type == 'integral':
            return self._create_omml_integral(element)
        elif element_type == 'sum':
            return self._create_omml_sum(element)
        elif element_type == 'matrix':
            return self._create_omml_matrix(element)
        elif element_type == 'symbol':
            return self._create_omml_symbol(element)
        elif element_type == 'function':
            return self._create_omml_function(element)
        elif element_type == 'text':
            return self._create_omml_text(element)
        else:
            # 默认处理为文本
            return self._create_omml_text(element)
    
    def _create_omml_fraction(self, element: Dict[str, Any]) -> Element:
        """创建OMML分数元素"""
        frac = Element('m:f')
        
        # 分子
        num = SubElement(frac, 'm:num')
        num_run = SubElement(num, 'm:r')
        num_text = SubElement(num_run, 'm:t')
        num_text.text = element['attributes'].get('numerator', '')
        
        # 分母
        den = SubElement(frac, 'm:den')
        den_run = SubElement(den, 'm:r')
        den_text = SubElement(den_run, 'm:t')
        den_text.text = element['attributes'].get('denominator', '')
        
        return frac
    
    def _create_omml_superscript(self, element: Dict[str, Any]) -> Element:
        """创建OMML上标元素"""
        sup = Element('m:sSup')
        
        # 基数（这里简化处理）
        base = SubElement(sup, 'm:e')
        base_run = SubElement(base, 'm:r')
        base_text = SubElement(base_run, 'm:t')
        base_text.text = ''  # 需要从上下文获取基数
        
        # 上标
        sup_elem = SubElement(sup, 'm:sup')
        sup_run = SubElement(sup_elem, 'm:r')
        sup_text = SubElement(sup_run, 'm:t')
        sup_text.text = element['attributes'].get('superscript_content', '')
        
        return sup
    
    def _create_omml_subscript(self, element: Dict[str, Any]) -> Element:
        """创建OMML下标元素"""
        sub = Element('m:sSub')
        
        # 基数
        base = SubElement(sub, 'm:e')
        base_run = SubElement(base, 'm:r')
        base_text = SubElement(base_run, 'm:t')
        base_text.text = ''  # 需要从上下文获取基数
        
        # 下标
        sub_elem = SubElement(sub, 'm:sub')
        sub_run = SubElement(sub_elem, 'm:r')
        sub_text = SubElement(sub_run, 'm:t')
        sub_text.text = element['attributes'].get('subscript_content', '')
        
        return sub
    
    def _create_omml_sqrt(self, element: Dict[str, Any]) -> Element:
        """创建OMML根号元素"""
        rad = Element('m:rad')
        
        # 根指数
        index = element['attributes'].get('index', '2')
        if index != '2':
            deg = SubElement(rad, 'm:deg')
            deg_run = SubElement(deg, 'm:r')
            deg_text = SubElement(deg_run, 'm:t')
            deg_text.text = index
        
        # 被开方数
        radicand = SubElement(rad, 'm:e')
        rad_run = SubElement(radicand, 'm:r')
        rad_text = SubElement(rad_run, 'm:t')
        rad_text.text = element['attributes'].get('radicand', '')
        
        return rad
    
    def _create_omml_integral(self, element: Dict[str, Any]) -> Element:
        """创建OMML积分元素"""
        integral = Element('m:nary')
        
        # 积分符号
        nary_pr = SubElement(integral, 'm:naryPr')
        chr_elem = SubElement(nary_pr, 'm:chr')
        chr_elem.set('m:val', '∫')  # 积分符号
        
        # 下限
        lower_limit = element['attributes'].get('lower_limit')
        if lower_limit:
            sub = SubElement(integral, 'm:sub')
            sub_run = SubElement(sub, 'm:r')
            sub_text = SubElement(sub_run, 'm:t')
            sub_text.text = lower_limit
        
        # 上限
        upper_limit = element['attributes'].get('upper_limit')
        if upper_limit:
            sup = SubElement(integral, 'm:sup')
            sup_run = SubElement(sup, 'm:r')
            sup_text = SubElement(sup_run, 'm:t')
            sup_text.text = upper_limit
        
        # 被积函数
        e = SubElement(integral, 'm:e')
        e_run = SubElement(e, 'm:r')
        e_text = SubElement(e_run, 'm:t')
        e_text.text = ''  # 需要从上下文获取
        
        return integral
    
    def _create_omml_sum(self, element: Dict[str, Any]) -> Element:
        """创建OMML求和元素"""
        sum_elem = Element('m:nary')
        
        # 求和符号
        nary_pr = SubElement(sum_elem, 'm:naryPr')
        chr_elem = SubElement(nary_pr, 'm:chr')
        
        sum_type = element['attributes'].get('sum_type', 'sum')
        if sum_type == 'sum':
            chr_elem.set('m:val', '∑')
        elif sum_type == 'prod':
            chr_elem.set('m:val', '∏')
        
        # 下限
        lower_limit = element['attributes'].get('lower_limit')
        if lower_limit:
            sub = SubElement(sum_elem, 'm:sub')
            sub_run = SubElement(sub, 'm:r')
            sub_text = SubElement(sub_run, 'm:t')
            sub_text.text = lower_limit
        
        # 上限
        upper_limit = element['attributes'].get('upper_limit')
        if upper_limit:
            sup = SubElement(sum_elem, 'm:sup')
            sup_run = SubElement(sup, 'm:r')
            sup_text = SubElement(sup_run, 'm:t')
            sup_text.text = upper_limit
        
        # 求和表达式
        e = SubElement(sum_elem, 'm:e')
        e_run = SubElement(e, 'm:r')
        e_text = SubElement(e_run, 'm:t')
        e_text.text = ''  # 需要从上下文获取
        
        return sum_elem
    
    def _create_omml_matrix(self, element: Dict[str, Any]) -> Element:
        """创建OMML矩阵元素"""
        matrix = Element('m:m')
        
        # 矩阵属性
        matrix_pr = SubElement(matrix, 'm:mPr')
        
        # 解析矩阵内容（简化版本）
        matrix_content = element['attributes'].get('matrix_content', '')
        rows = matrix_content.split('\\\\')
        
        for row_content in rows:
            if row_content.strip():
                row = SubElement(matrix, 'm:mr')
                cols = row_content.split('&')
                
                for col_content in cols:
                    col = SubElement(row, 'm:e')
                    col_run = SubElement(col, 'm:r')
                    col_text = SubElement(col_run, 'm:t')
                    col_text.text = col_content.strip()
        
        return matrix
    
    def _create_omml_symbol(self, element: Dict[str, Any]) -> Element:
        """创建OMML符号元素"""
        run = Element('m:r')
        text = SubElement(run, 'm:t')
        
        symbol_name = element['attributes'].get('symbol_name', '')
        
        # 查找符号映射
        if symbol_name in self.greek_mappings:
            text.text = self.greek_mappings[symbol_name]
        elif symbol_name in self.operator_mappings:
            text.text = self.operator_mappings[symbol_name]
        elif symbol_name in self.set_mappings:
            text.text = self.set_mappings[symbol_name]
        elif symbol_name in self.arrow_mappings:
            text.text = self.arrow_mappings[symbol_name]
        else:
            text.text = symbol_name
        
        return run
    
    def _create_omml_function(self, element: Dict[str, Any]) -> Element:
        """创建OMML函数元素"""
        run = Element('m:r')
        text = SubElement(run, 'm:t')
        
        function_name = element['attributes'].get('function_name', '')
        text.text = function_name
        
        return run
    
    def _create_omml_text(self, element: Dict[str, Any]) -> Element:
        """创建OMML文本元素"""
        run = Element('m:r')
        text = SubElement(run, 'm:t')
        
        content = element.get('content', '')
        if 'text_content' in element.get('attributes', {}):
            content = element['attributes']['text_content']
        
        text.text = content
        
        return run
    
    def _convert_element_to_mathml(self, element: Dict[str, Any]) -> Optional[Element]:
        """将公式元素转换为MathML元素"""
        # MathML转换的简化实现
        # 在实际项目中，这里需要完整的MathML转换逻辑
        
        element_type = element.get('type')
        
        if element_type == 'symbol':
            mi = Element('mi')
            symbol_name = element['attributes'].get('symbol_name', '')
            if symbol_name in self.greek_mappings:
                mi.text = self.greek_mappings[symbol_name]
            else:
                mi.text = symbol_name
            return mi
        
        elif element_type == 'text':
            mtext = Element('mtext')
            mtext.text = element.get('content', '')
            return mtext
        
        else:
            # 默认处理
            mi = Element('mi')
            mi.text = element.get('content', '')
            return mi
