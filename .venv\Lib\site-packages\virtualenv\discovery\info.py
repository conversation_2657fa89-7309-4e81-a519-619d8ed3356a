from __future__ import annotations

import logging
import os
import sys
import tempfile

_FS_CASE_SENSITIVE = None
LOGGER = logging.getLogger(__name__)
IS_WIN = sys.platform == "win32"


def fs_is_case_sensitive():
    """Check if the file system is case-sensitive."""
    global _FS_CASE_SENSITIVE  # noqa: PLW0603

    if _FS_CASE_SENSITIVE is None:
        with tempfile.NamedTemporaryFile(prefix="TmP") as tmp_file:
            _FS_CASE_SENSITIVE = not os.path.exists(tmp_file.name.lower())
            LOGGER.debug("filesystem is %scase-sensitive", "" if _FS_CASE_SENSITIVE else "not ")
    return _FS_CASE_SENSITIVE


def fs_path_id(path: str) -> str:
    """Get a case-normalized path identifier."""
    return path.casefold() if fs_is_case_sensitive() else path


__all__ = (
    "IS_WIN",
    "fs_is_case_sensitive",
    "fs_path_id",
)
