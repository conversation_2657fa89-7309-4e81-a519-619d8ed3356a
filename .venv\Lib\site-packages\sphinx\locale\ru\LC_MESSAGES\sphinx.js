Documentation.addTranslations({
    "locale": "ru",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "%(filename)s &#8212; %(docstitle)s",
        "&#169; %(copyright_prefix)s %(copyright)s.": "",
        ", in ": ", \u0432",
        "About these documents": "\u041e\u0431 \u044d\u0442\u0438\u0445 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0445",
        "Automatically generated list of changes in version %(version)s": "\u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438 \u0441\u043e\u0437\u0434\u0430\u043d\u043d\u044b\u0439 \u0441\u043f\u0438\u0441\u043e\u043a \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0439 \u0432 \u0432\u0435\u0440\u0441\u0438\u0438 %(version)s",
        "C API changes": "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0432 API C",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0432 \u0432\u0435\u0440\u0441\u0438\u0438 %(version)s &#8212; %(docstitle)s",
        "Collapse sidebar": "\u0421\u0432\u0435\u0440\u043d\u0443\u0442\u044c \u0431\u043e\u043a\u043e\u0432\u0443\u044e \u043f\u0430\u043d\u0435\u043b\u044c",
        "Complete Table of Contents": "\u041f\u043e\u043b\u043d\u043e\u0435 \u043e\u0433\u043b\u0430\u0432\u043b\u0435\u043d\u0438\u0435",
        "Contents": "\u0421\u043e\u0434\u0435\u0440\u0436\u0430\u043d\u0438\u0435",
        "Copyright": "\u0410\u0432\u0442\u043e\u0440\u0441\u043a\u0438\u0435 \u043f\u0440\u0430\u0432\u0430",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "\u0421\u043e\u0437\u0434\u0430\u043d\u043e \u0441 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u0435\u043c <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.",
        "Expand sidebar": "\u0420\u0430\u0437\u0432\u0435\u0440\u043d\u0443\u0442\u044c \u0431\u043e\u043a\u043e\u0432\u0443\u044e \u043f\u0430\u043d\u0435\u043b\u044c",
        "Full index on one page": "\u041f\u043e\u043b\u043d\u044b\u0439 \u0430\u043b\u0444\u0430\u0432\u0438\u0442\u043d\u044b\u0439 \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u044c \u043d\u0430 \u043e\u0434\u043d\u043e\u0439 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0435",
        "General Index": "\u0410\u043b\u0444\u0430\u0432\u0438\u0442\u043d\u044b\u0439 \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u044c",
        "Global Module Index": "\u0410\u043b\u0444\u0430\u0432\u0438\u0442\u043d\u044b\u0439 \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u044c \u043c\u043e\u0434\u0443\u043b\u0435\u0439",
        "Go": "\u0418\u0441\u043a\u0430\u0442\u044c",
        "Hide Search Matches": "\u0421\u043d\u044f\u0442\u044c \u0432\u044b\u0434\u0435\u043b\u0435\u043d\u0438\u0435",
        "Index": "\u0410\u043b\u0444\u0430\u0432\u0438\u0442\u043d\u044b\u0439 \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u044c",
        "Index &#x2013; %(key)s": "",
        "Index pages by letter": "\u0423\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u0438 \u043f\u043e \u0431\u0443\u043a\u0432\u0430\u043c \u0430\u043b\u0444\u0430\u0432\u0438\u0442\u0430",
        "Indices and tables:": "\u0422\u0430\u0431\u043b\u0438\u0446\u044b \u0438 \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u0438:",
        "Last updated on %(last_updated)s.": "\u041e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u043e: %(last_updated)s.",
        "Library changes": "\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u0432 \u0431\u0438\u0431\u043b\u0438\u043e\u0442\u0435\u043a\u0435",
        "Navigation": "\u041d\u0430\u0432\u0438\u0433\u0430\u0446\u0438\u044f",
        "Next topic": "\u0421\u043b\u0435\u0434\u0443\u044e\u0449\u0438\u0439 \u0440\u0430\u0437\u0434\u0435\u043b",
        "Other changes": "\u0414\u0440\u0443\u0433\u0438\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f",
        "Overview": "\u041e\u0431\u0437\u043e\u0440",
        "Please activate JavaScript to enable the search\n    functionality.": "\u0414\u043b\u044f \u0440\u0430\u0431\u043e\u0442\u044b \u043f\u043e\u0438\u0441\u043a\u0430 \u0432\u043a\u043b\u044e\u0447\u0438\u0442\u0435 JavaScript \u0432 \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0435.",
        "Preparing search...": "\u041f\u043e\u0434\u0433\u043e\u0442\u043e\u0432\u043a\u0430 \u043f\u043e\u0438\u0441\u043a\u0430\u2026",
        "Previous topic": "\u041f\u0440\u0435\u0434\u044b\u0434\u0443\u0449\u0438\u0439 \u0440\u0430\u0437\u0434\u0435\u043b",
        "Quick search": "\u0411\u044b\u0441\u0442\u0440\u044b\u0439 \u043f\u043e\u0438\u0441\u043a",
        "Search": "\u041f\u043e\u0438\u0441\u043a",
        "Search Page": "\u041f\u043e\u0438\u0441\u043a",
        "Search Results": "\u0420\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442\u044b \u043f\u043e\u0438\u0441\u043a\u0430",
        "Search finished, found one page matching the search query.": [
            "",
            "",
            "",
            ""
        ],
        "Search within %(docstitle)s": "\u041f\u043e\u0438\u0441\u043a \u0432 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0435 \u00ab%(docstitle)s\u00bb",
        "Searching": "\u0418\u0434\u0451\u0442 \u043f\u043e\u0438\u0441\u043a",
        "Searching for multiple words only shows matches that contain\n    all words.": "\u041f\u043e\u0438\u0441\u043a \u043f\u043e \u043d\u0435\u0441\u043a\u043e\u043b\u044c\u043a\u0438\u043c \u0441\u043b\u043e\u0432\u0430\u043c \u043e\u0442\u043e\u0431\u0440\u0430\u0436\u0430\u0435\u0442 \u0442\u043e\u043b\u044c\u043a\u043e \u0440\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442\u044b,\n    \u0441\u043e\u0434\u0435\u0440\u0436\u0430\u0449\u0438\u0435 \u0432\u0441\u0435 \u0441\u043b\u043e\u0432\u0430.",
        "Show Source": "\u0418\u0441\u0445\u043e\u0434\u043d\u044b\u0439 \u0442\u0435\u043a\u0441\u0442",
        "Table of Contents": "\u041e\u0433\u043b\u0430\u0432\u043b\u0435\u043d\u0438\u0435",
        "This Page": "\u042d\u0442\u0430 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0430",
        "Welcome! This is": "\u0414\u043e\u0431\u0440\u043e \u043f\u043e\u0436\u0430\u043b\u043e\u0432\u0430\u0442\u044c! \u042d\u0442\u043e",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "\u041f\u043e \u0432\u0430\u0448\u0435\u043c\u0443 \u043f\u043e\u0438\u0441\u043a\u0443 \u043d\u0435 \u043d\u0430\u0439\u0434\u0435\u043d\u043e \u043d\u0438 \u043e\u0434\u043d\u043e\u0433\u043e \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430. \u041f\u0440\u043e\u0432\u0435\u0440\u044c\u0442\u0435, \u0447\u0442\u043e \u0432\u0441\u0435 \u0441\u043b\u043e\u0432\u0430 \u043d\u0430\u043f\u0438\u0441\u0430\u043d\u044b \u0431\u0435\u0437 \u043e\u0448\u0438\u0431\u043e\u043a, \u0438 \u0447\u0442\u043e \u0432\u044b \u0432\u044b\u0431\u0440\u0430\u043b\u0438 \u0434\u043e\u0441\u0442\u0430\u0442\u043e\u0447\u043d\u043e \u043a\u0430\u0442\u0435\u0433\u043e\u0440\u0438\u0439.",
        "all functions, classes, terms": "\u0432\u0441\u0435 \u0444\u0443\u043d\u043a\u0446\u0438\u0438, \u043a\u043b\u0430\u0441\u0441\u044b, \u043f\u0435\u0440\u0435\u043c\u0435\u043d\u043d\u044b\u0435 \u0438 \u043a\u043e\u043d\u0441\u0442\u0430\u043d\u0442\u044b",
        "can be huge": "\u043c\u043e\u0436\u0435\u0442 \u0431\u044b\u0442\u044c \u043e\u0447\u0435\u043d\u044c \u0431\u043e\u043b\u044c\u0448\u0438\u043c",
        "last updated": "\u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0435 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435",
        "lists all sections and subsections": "\u0441\u043f\u0438\u0441\u043e\u043a \u0432\u0441\u0435\u0445 \u0440\u0430\u0437\u0434\u0435\u043b\u043e\u0432 \u0438 \u043f\u043e\u0434\u0440\u0430\u0437\u0434\u0435\u043b\u043e\u0432",
        "next chapter": "\u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0430\u044f \u0433\u043b\u0430\u0432\u0430",
        "previous chapter": "\u043f\u0440\u0435\u0434\u044b\u0434\u0443\u0449\u0430\u044f \u0433\u043b\u0430\u0432\u0430",
        "quick access to all modules": "\u0441\u0432\u043e\u0434\u043d\u044b\u0439 \u0441\u043f\u0438\u0441\u043e\u043a \u0432\u0441\u0435\u0445 \u043c\u043e\u0434\u0443\u043b\u0435\u0439",
        "search": "\u0438\u0441\u043a\u0430\u0442\u044c",
        "search this documentation": "\u043f\u043e\u0438\u0441\u043a \u0432 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0446\u0438\u0438",
        "the documentation for": "\u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0446\u0438\u044f"
    },
    "plural_expr": "(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3)"
});