"""
LaTeX公式解析器

专门用于解析LaTeX数学公式的语法结构。
"""

import re
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum

from ..core.exceptions import FormulaConversionError
from ..utils.logger import get_logger
from ..utils.error_handler import handle_errors

logger = get_logger(__name__)


class FormulaElementType(Enum):
    """公式元素类型"""
    SYMBOL = "symbol"           # 符号
    FUNCTION = "function"       # 函数
    OPERATOR = "operator"       # 运算符
    FRACTION = "fraction"       # 分数
    SUPERSCRIPT = "superscript" # 上标
    SUBSCRIPT = "subscript"     # 下标
    SQRT = "sqrt"              # 根号
    INTEGRAL = "integral"       # 积分
    SUM = "sum"                # 求和
    LIMIT = "limit"            # 极限
    MATRIX = "matrix"          # 矩阵
    BRACKET = "bracket"        # 括号
    ENVIRONMENT = "environment" # 环境
    TEXT = "text"              # 文本


@dataclass
class FormulaElement:
    """公式元素数据结构"""
    type: FormulaElementType
    content: str
    start_pos: int
    end_pos: int
    attributes: Dict[str, Any] = field(default_factory=dict)
    children: List['FormulaElement'] = field(default_factory=list)


@dataclass
class ParsedFormula:
    """解析后的公式数据结构"""
    original_content: str
    formula_type: str
    elements: List[FormulaElement]
    complexity: str = "medium"
    metadata: Dict[str, Any] = field(default_factory=dict)


class LaTeXFormulaParser:
    """
    LaTeX公式解析器
    
    解析LaTeX数学公式的语法结构，识别各种数学元素。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化公式解析器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 编译正则表达式
        self._compile_patterns()
        
        # 数学命令映射
        self._init_command_mappings()
    
    def _compile_patterns(self) -> None:
        """编译正则表达式模式"""
        
        # 分数
        self.fraction_pattern = re.compile(r'\\frac\{([^}]*)\}\{([^}]*)\}')
        
        # 根号
        self.sqrt_pattern = re.compile(r'\\sqrt(?:\[([^\]]*)\])?\{([^}]*)\}')
        
        # 上标和下标
        self.superscript_pattern = re.compile(r'\^(\{[^}]*\}|[^{}\s])')
        self.subscript_pattern = re.compile(r'_(\{[^}]*\}|[^{}\s])')
        
        # 积分
        self.integral_pattern = re.compile(
            r'\\(int|iint|iiint|oint)(?:_(\{[^}]*\}|[^{}\s]))?(?:\^(\{[^}]*\}|[^{}\s]))?'
        )
        
        # 求和
        self.sum_pattern = re.compile(
            r'\\(sum|prod|coprod)(?:_(\{[^}]*\}|[^{}\s]))?(?:\^(\{[^}]*\}|[^{}\s]))?'
        )
        
        # 极限
        self.limit_pattern = re.compile(
            r'\\lim(?:_(\{[^}]*\}|[^{}\s]))?'
        )
        
        # 矩阵环境
        self.matrix_pattern = re.compile(
            r'\\begin\{([bBvV]?matrix)\}(.*?)\\end\{\1\}',
            re.DOTALL
        )
        
        # 括号
        self.bracket_pattern = re.compile(
            r'\\(left|right|big|Big|bigg|Bigg)([()[\]{}|]|\\[|])'
        )
        
        # 函数
        self.function_pattern = re.compile(
            r'\\(sin|cos|tan|log|ln|exp|max|min|det|gcd)(?![a-zA-Z])'
        )
        
        # 希腊字母和符号
        self.symbol_pattern = re.compile(
            r'\\([a-zA-Z]+)(?![a-zA-Z])'
        )
        
        # 运算符
        self.operator_pattern = re.compile(
            r'[+\-*/=<>≤≥≠±∓×÷·∘∗⋆∙]'
        )
        
        # 文本
        self.text_pattern = re.compile(r'\\text\{([^}]*)\}')
    
    def _init_command_mappings(self) -> None:
        """初始化命令映射"""
        
        # 希腊字母
        self.greek_letters = {
            'alpha', 'beta', 'gamma', 'delta', 'epsilon', 'varepsilon',
            'zeta', 'eta', 'theta', 'vartheta', 'iota', 'kappa',
            'lambda', 'mu', 'nu', 'xi', 'pi', 'varpi', 'rho', 'varrho',
            'sigma', 'varsigma', 'tau', 'upsilon', 'phi', 'varphi',
            'chi', 'psi', 'omega', 'Gamma', 'Delta', 'Theta', 'Lambda',
            'Xi', 'Pi', 'Sigma', 'Upsilon', 'Phi', 'Psi', 'Omega'
        }
        
        # 数学函数
        self.math_functions = {
            'sin', 'cos', 'tan', 'cot', 'sec', 'csc',
            'arcsin', 'arccos', 'arctan', 'sinh', 'cosh', 'tanh',
            'log', 'ln', 'lg', 'exp', 'max', 'min', 'sup', 'inf',
            'lim', 'det', 'gcd', 'lcm'
        }
        
        # 运算符
        self.operators = {
            'pm', 'mp', 'times', 'div', 'cdot', 'ast', 'star',
            'circ', 'bullet', 'leq', 'geq', 'neq', 'approx',
            'equiv', 'sim', 'simeq', 'cong', 'propto'
        }
        
        # 关系符号
        self.relations = {
            'in', 'notin', 'subset', 'supset', 'subseteq', 'supseteq',
            'cup', 'cap', 'emptyset', 'varnothing'
        }
    
    @handle_errors(context="解析LaTeX公式")
    def parse(self, content: str, formula_type: str = "inline") -> ParsedFormula:
        """
        解析LaTeX公式
        
        Args:
            content: LaTeX公式内容
            formula_type: 公式类型
            
        Returns:
            解析后的公式对象
        """
        logger.debug(f"开始解析公式: {content[:50]}...")
        
        # 预处理
        processed_content = self._preprocess_content(content)
        
        # 解析元素
        elements = self._parse_elements(processed_content)
        
        # 构建层次结构
        structured_elements = self._build_structure(elements, processed_content)
        
        # 计算复杂度
        complexity = self._calculate_complexity(structured_elements)
        
        # 创建解析结果
        parsed_formula = ParsedFormula(
            original_content=content,
            formula_type=formula_type,
            elements=structured_elements,
            complexity=complexity,
            metadata={
                'element_count': len(structured_elements),
                'has_fractions': any(e.type == FormulaElementType.FRACTION for e in structured_elements),
                'has_integrals': any(e.type == FormulaElementType.INTEGRAL for e in structured_elements),
                'has_matrices': any(e.type == FormulaElementType.MATRIX for e in structured_elements),
            }
        )
        
        logger.debug(f"公式解析完成，复杂度: {complexity}，元素数: {len(structured_elements)}")
        
        return parsed_formula
    
    def _preprocess_content(self, content: str) -> str:
        """预处理公式内容"""
        # 移除多余的空白
        content = re.sub(r'\s+', ' ', content).strip()
        
        # 标准化括号
        content = content.replace('{', ' { ').replace('}', ' } ')
        content = re.sub(r'\s+', ' ', content)
        
        return content
    
    def _parse_elements(self, content: str) -> List[FormulaElement]:
        """解析公式元素"""
        elements = []
        
        # 按优先级解析不同类型的元素
        elements.extend(self._parse_fractions(content))
        elements.extend(self._parse_sqrt(content))
        elements.extend(self._parse_integrals(content))
        elements.extend(self._parse_sums(content))
        elements.extend(self._parse_limits(content))
        elements.extend(self._parse_matrices(content))
        elements.extend(self._parse_brackets(content))
        elements.extend(self._parse_functions(content))
        elements.extend(self._parse_symbols(content))
        elements.extend(self._parse_superscripts(content))
        elements.extend(self._parse_subscripts(content))
        elements.extend(self._parse_text(content))
        
        # 按位置排序
        elements.sort(key=lambda e: e.start_pos)
        
        return elements
    
    def _parse_fractions(self, content: str) -> List[FormulaElement]:
        """解析分数"""
        elements = []
        
        matches = self.fraction_pattern.finditer(content)
        for match in matches:
            numerator, denominator = match.groups()
            
            element = FormulaElement(
                type=FormulaElementType.FRACTION,
                content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={
                    'numerator': numerator.strip(),
                    'denominator': denominator.strip()
                }
            )
            
            elements.append(element)
        
        return elements
    
    def _parse_sqrt(self, content: str) -> List[FormulaElement]:
        """解析根号"""
        elements = []
        
        matches = self.sqrt_pattern.finditer(content)
        for match in matches:
            index, radicand = match.groups()
            
            element = FormulaElement(
                type=FormulaElementType.SQRT,
                content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={
                    'index': index.strip() if index else '2',
                    'radicand': radicand.strip()
                }
            )
            
            elements.append(element)
        
        return elements
    
    def _parse_integrals(self, content: str) -> List[FormulaElement]:
        """解析积分"""
        elements = []
        
        matches = self.integral_pattern.finditer(content)
        for match in matches:
            integral_type, lower_limit, upper_limit = match.groups()
            
            element = FormulaElement(
                type=FormulaElementType.INTEGRAL,
                content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={
                    'integral_type': integral_type,
                    'lower_limit': self._clean_braces(lower_limit) if lower_limit else None,
                    'upper_limit': self._clean_braces(upper_limit) if upper_limit else None
                }
            )
            
            elements.append(element)
        
        return elements
    
    def _parse_sums(self, content: str) -> List[FormulaElement]:
        """解析求和"""
        elements = []
        
        matches = self.sum_pattern.finditer(content)
        for match in matches:
            sum_type, lower_limit, upper_limit = match.groups()
            
            element = FormulaElement(
                type=FormulaElementType.SUM,
                content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={
                    'sum_type': sum_type,
                    'lower_limit': self._clean_braces(lower_limit) if lower_limit else None,
                    'upper_limit': self._clean_braces(upper_limit) if upper_limit else None
                }
            )
            
            elements.append(element)
        
        return elements
    
    def _parse_limits(self, content: str) -> List[FormulaElement]:
        """解析极限"""
        elements = []
        
        matches = self.limit_pattern.finditer(content)
        for match in matches:
            limit_expr = match.group(1)
            
            element = FormulaElement(
                type=FormulaElementType.LIMIT,
                content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={
                    'limit_expr': self._clean_braces(limit_expr) if limit_expr else None
                }
            )
            
            elements.append(element)
        
        return elements
    
    def _parse_matrices(self, content: str) -> List[FormulaElement]:
        """解析矩阵"""
        elements = []
        
        matches = self.matrix_pattern.finditer(content)
        for match in matches:
            matrix_type, matrix_content = match.groups()
            
            element = FormulaElement(
                type=FormulaElementType.MATRIX,
                content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={
                    'matrix_type': matrix_type,
                    'matrix_content': matrix_content.strip()
                }
            )
            
            elements.append(element)
        
        return elements
    
    def _parse_brackets(self, content: str) -> List[FormulaElement]:
        """解析括号"""
        elements = []
        
        matches = self.bracket_pattern.finditer(content)
        for match in matches:
            size_modifier, bracket_char = match.groups()
            
            element = FormulaElement(
                type=FormulaElementType.BRACKET,
                content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={
                    'size_modifier': size_modifier,
                    'bracket_char': bracket_char
                }
            )
            
            elements.append(element)
        
        return elements
    
    def _parse_functions(self, content: str) -> List[FormulaElement]:
        """解析函数"""
        elements = []
        
        matches = self.function_pattern.finditer(content)
        for match in matches:
            function_name = match.group(1)
            
            element = FormulaElement(
                type=FormulaElementType.FUNCTION,
                content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={'function_name': function_name}
            )
            
            elements.append(element)
        
        return elements
    
    def _parse_symbols(self, content: str) -> List[FormulaElement]:
        """解析符号"""
        elements = []
        
        matches = self.symbol_pattern.finditer(content)
        for match in matches:
            symbol_name = match.group(1)
            
            # 跳过已经处理的命令
            if symbol_name in self.math_functions:
                continue
            
            element = FormulaElement(
                type=FormulaElementType.SYMBOL,
                content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={
                    'symbol_name': symbol_name,
                    'symbol_category': self._classify_symbol(symbol_name)
                }
            )
            
            elements.append(element)
        
        return elements
    
    def _parse_superscripts(self, content: str) -> List[FormulaElement]:
        """解析上标"""
        elements = []
        
        matches = self.superscript_pattern.finditer(content)
        for match in matches:
            superscript_content = match.group(1)
            
            element = FormulaElement(
                type=FormulaElementType.SUPERSCRIPT,
                content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={
                    'superscript_content': self._clean_braces(superscript_content)
                }
            )
            
            elements.append(element)
        
        return elements
    
    def _parse_subscripts(self, content: str) -> List[FormulaElement]:
        """解析下标"""
        elements = []
        
        matches = self.subscript_pattern.finditer(content)
        for match in matches:
            subscript_content = match.group(1)
            
            element = FormulaElement(
                type=FormulaElementType.SUBSCRIPT,
                content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={
                    'subscript_content': self._clean_braces(subscript_content)
                }
            )
            
            elements.append(element)
        
        return elements
    
    def _parse_text(self, content: str) -> List[FormulaElement]:
        """解析文本"""
        elements = []
        
        matches = self.text_pattern.finditer(content)
        for match in matches:
            text_content = match.group(1)
            
            element = FormulaElement(
                type=FormulaElementType.TEXT,
                content=match.group(0),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={'text_content': text_content}
            )
            
            elements.append(element)
        
        return elements
    
    def _clean_braces(self, content: str) -> str:
        """清理括号"""
        if content and content.startswith('{') and content.endswith('}'):
            return content[1:-1]
        return content
    
    def _classify_symbol(self, symbol_name: str) -> str:
        """分类符号"""
        if symbol_name in self.greek_letters:
            return "greek_letter"
        elif symbol_name in self.operators:
            return "operator"
        elif symbol_name in self.relations:
            return "relation"
        else:
            return "unknown"
    
    def _build_structure(self, elements: List[FormulaElement], content: str) -> List[FormulaElement]:
        """构建层次结构"""
        # 简化版本：直接返回元素列表
        # 在更复杂的实现中，这里会构建语法树
        return elements
    
    def _calculate_complexity(self, elements: List[FormulaElement]) -> str:
        """计算公式复杂度"""
        if len(elements) <= 3:
            return "simple"
        elif len(elements) <= 10:
            return "medium"
        else:
            return "complex"
