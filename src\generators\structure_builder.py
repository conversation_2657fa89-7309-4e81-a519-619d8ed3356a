"""
文档结构构建器

负责构建Word文档的结构层次。
"""

from typing import Dict, List, Any, Optional
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager

logger = get_logger(__name__)


class StructureBuilder:
    """
    文档结构构建器
    
    负责构建Word文档的层次结构。
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化结构构建器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or ConfigManager()
        self.config = self.config_manager.get_config()
    
    def build_document_outline(
        self,
        document: Document,
        sections: List[Dict[str, Any]]
    ) -> None:
        """
        构建文档大纲
        
        Args:
            document: Word文档对象
            sections: 章节列表
        """
        logger.debug("开始构建文档大纲")
        
        for section in sections:
            self._add_section_to_document(document, section)
    
    def _add_section_to_document(
        self,
        document: Document,
        section: Dict[str, Any]
    ) -> None:
        """添加章节到文档"""
        
        # 添加章节标题
        title = section.get('title', '')
        level = section.get('level', 1)
        
        # 确保级别在有效范围内
        word_level = max(1, min(level, 9))
        
        heading = document.add_heading(title, level=word_level)
        
        # 添加章节内容
        content = section.get('content', '')
        if content.strip():
            # 分段处理内容
            paragraphs = self._split_content_into_paragraphs(content)
            for para in paragraphs:
                if para.strip():
                    document.add_paragraph(para)
        
        # 递归处理子章节
        subsections = section.get('subsections', [])
        for subsection in subsections:
            self._add_section_to_document(document, subsection)
    
    def _split_content_into_paragraphs(self, content: str) -> List[str]:
        """将内容分割为段落"""
        # 按双换行符分割段落
        paragraphs = content.split('\n\n')
        
        # 清理每个段落
        cleaned_paragraphs = []
        for para in paragraphs:
            # 移除单个换行符，保持段落连续
            cleaned_para = para.replace('\n', ' ').strip()
            if cleaned_para:
                cleaned_paragraphs.append(cleaned_para)
        
        return cleaned_paragraphs
    
    def create_table_of_contents(
        self,
        document: Document,
        sections: List[Dict[str, Any]]
    ) -> None:
        """
        创建目录
        
        Args:
            document: Word文档对象
            sections: 章节列表
        """
        logger.debug("创建目录")
        
        # 添加目录标题
        toc_heading = document.add_heading("目录", level=1)
        toc_heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 生成目录项
        toc_items = self._generate_toc_items(sections)
        
        for item in toc_items:
            toc_paragraph = document.add_paragraph()
            
            # 添加缩进
            indent_level = item['level'] - 1
            toc_paragraph.paragraph_format.left_indent = indent_level * 0.5  # 每级缩进0.5英寸
            
            # 添加编号和标题
            toc_text = f"{item['numbering']} {item['title']}"
            toc_paragraph.add_run(toc_text)
        
        # 添加分页符
        document.add_page_break()
    
    def _generate_toc_items(
        self,
        sections: List[Dict[str, Any]],
        parent_numbering: str = ""
    ) -> List[Dict[str, Any]]:
        """生成目录项"""
        toc_items = []
        
        for i, section in enumerate(sections):
            # 生成编号
            if parent_numbering:
                numbering = f"{parent_numbering}.{i + 1}"
            else:
                numbering = str(i + 1)
            
            # 创建目录项
            toc_item = {
                'level': section.get('level', 1),
                'title': section.get('title', ''),
                'numbering': numbering
            }
            
            toc_items.append(toc_item)
            
            # 递归处理子章节
            subsections = section.get('subsections', [])
            if subsections:
                sub_items = self._generate_toc_items(subsections, numbering)
                toc_items.extend(sub_items)
        
        return toc_items
    
    def add_page_numbers(self, document: Document) -> None:
        """添加页码"""
        logger.debug("添加页码")
        
        # 获取第一个节
        section = document.sections[0]
        
        # 创建页脚
        footer = section.footer
        footer_paragraph = footer.paragraphs[0]
        footer_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加页码字段
        # 注意：python-docx对页码字段的支持有限
        # 这里添加一个简单的页码文本
        footer_paragraph.add_run("第 ")
        
        # 添加页码字段（需要手动在Word中更新）
        footer_paragraph.add_run("页")
    
    def add_headers_footers(
        self,
        document: Document,
        header_text: Optional[str] = None,
        footer_text: Optional[str] = None
    ) -> None:
        """
        添加页眉页脚
        
        Args:
            document: Word文档对象
            header_text: 页眉文本
            footer_text: 页脚文本
        """
        section = document.sections[0]
        
        # 添加页眉
        if header_text:
            header = section.header
            header_paragraph = header.paragraphs[0]
            header_paragraph.text = header_text
            header_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加页脚
        if footer_text:
            footer = section.footer
            footer_paragraph = footer.paragraphs[0]
            footer_paragraph.text = footer_text
            footer_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    def create_section_breaks(
        self,
        document: Document,
        sections: List[Dict[str, Any]]
    ) -> None:
        """
        创建章节分隔
        
        Args:
            document: Word文档对象
            sections: 章节列表
        """
        # 在主要章节之间添加分页符
        for i, section in enumerate(sections):
            if i > 0 and section.get('level', 1) <= 2:  # 只在主要章节前分页
                document.add_page_break()
    
    def organize_content_blocks(
        self,
        content: str,
        formulas: List[Dict[str, Any]],
        tables: List[Dict[str, Any]],
        figures: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        组织内容块
        
        Args:
            content: 文本内容
            formulas: 公式列表
            tables: 表格列表
            figures: 图形列表
            
        Returns:
            组织后的内容块列表
        """
        content_blocks = []
        
        # 创建所有元素的位置映射
        elements = []
        
        # 添加公式
        for formula in formulas:
            elements.append({
                'type': 'formula',
                'start_pos': formula.get('start_pos', 0),
                'end_pos': formula.get('end_pos', 0),
                'data': formula
            })
        
        # 添加表格
        for table in tables:
            elements.append({
                'type': 'table',
                'start_pos': table.get('start_pos', 0),
                'end_pos': table.get('end_pos', 0),
                'data': table
            })
        
        # 添加图形
        for figure in figures:
            elements.append({
                'type': 'figure',
                'start_pos': figure.get('start_pos', 0),
                'end_pos': figure.get('end_pos', 0),
                'data': figure
            })
        
        # 按位置排序
        elements.sort(key=lambda x: x['start_pos'])
        
        # 分割文本并插入元素
        current_pos = 0
        
        for element in elements:
            # 添加元素前的文本
            if element['start_pos'] > current_pos:
                text_content = content[current_pos:element['start_pos']].strip()
                if text_content:
                    content_blocks.append({
                        'type': 'text',
                        'content': text_content
                    })
            
            # 添加元素
            content_blocks.append(element)
            
            current_pos = element['end_pos']
        
        # 添加剩余文本
        if current_pos < len(content):
            remaining_text = content[current_pos:].strip()
            if remaining_text:
                content_blocks.append({
                    'type': 'text',
                    'content': remaining_text
                })
        
        return content_blocks
