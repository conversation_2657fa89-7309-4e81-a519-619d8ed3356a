"""
Word document generator for tex2word package.

This module provides functionality to generate Word documents from
converted LaTeX content, maintaining formatting and structure.
"""

import os
import re
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

try:
    from docx import Document
    from docx.shared import Inches, Pt, RGBColor
    from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_BREAK
    from docx.enum.style import WD_STYLE_TYPE
    from docx.oxml import parse_xml
    from docx.oxml.ns import nsdecls, qn
    from docx.text.paragraph import Paragraph
    from docx.text.run import Run
except ImportError:
    raise ImportError(
        "python-docx package is required. Install it with: pip install python-docx"
    )

from ..core.parser import ParsedElement
from ..core.exceptions import ConversionError
from ..converters.math_converter import MathMLElement


class DocxGenerator:
    """Generator for creating Word documents from converted LaTeX content."""
    
    # Default styles configuration
    DEFAULT_STYLES = {
        'title': {
            'font_name': 'Times New Roman',
            'font_size': 16,
            'bold': True,
            'alignment': WD_ALIGN_PARAGRAPH.CENTER
        },
        'author': {
            'font_name': 'Times New Roman',
            'font_size': 12,
            'alignment': WD_ALIGN_PARAGRAPH.CENTER
        },
        'section': {
            'font_name': 'Times New Roman',
            'font_size': 14,
            'bold': True,
            'space_before': Pt(12),
            'space_after': Pt(6)
        },
        'subsection': {
            'font_name': 'Times New Roman',
            'font_size': 12,
            'bold': True,
            'space_before': Pt(10),
            'space_after': Pt(4)
        },
        'normal': {
            'font_name': 'Times New Roman',
            'font_size': 11,
            'line_spacing': 1.15,
            'space_after': Pt(6)
        },
        'equation': {
            'alignment': WD_ALIGN_PARAGRAPH.CENTER,
            'space_before': Pt(6),
            'space_after': Pt(6)
        }
    }
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the DOCX generator.
        
        Args:
            config: Configuration dictionary for customizing output
        """
        self.config = config or {}
        self.document = Document()
        self.styles = self._merge_styles(self.DEFAULT_STYLES, 
                                       self.config.get('styles', {}))
        self.equation_counter = 0
        self.section_counters = [0] * 7  # For different section levels
        
        # Setup document styles
        self._setup_document_styles()
    
    def _merge_styles(self, default_styles: Dict, custom_styles: Dict) -> Dict:
        """Merge default styles with custom styles."""
        merged = default_styles.copy()
        for style_name, style_config in custom_styles.items():
            if style_name in merged:
                merged[style_name].update(style_config)
            else:
                merged[style_name] = style_config
        return merged
    
    def _setup_document_styles(self) -> None:
        """Setup custom styles in the document."""
        styles = self.document.styles
        
        # Create custom styles if they don't exist
        for style_name, style_config in self.styles.items():
            try:
                style = styles[style_name]
            except KeyError:
                # Create new style
                if style_name in ['title', 'author']:
                    style = styles.add_style(style_name, WD_STYLE_TYPE.PARAGRAPH)
                elif 'section' in style_name:
                    style = styles.add_style(style_name, WD_STYLE_TYPE.PARAGRAPH)
                else:
                    continue  # Use existing styles for others
            
            # Apply style configuration
            self._apply_style_config(style, style_config)
    
    def _apply_style_config(self, style, config: Dict) -> None:
        """Apply configuration to a style."""
        paragraph_format = style.paragraph_format
        font = style.font
        
        # Font settings
        if 'font_name' in config:
            font.name = config['font_name']
        if 'font_size' in config:
            font.size = Pt(config['font_size'])
        if 'bold' in config:
            font.bold = config['bold']
        if 'italic' in config:
            font.italic = config['italic']
        
        # Paragraph settings
        if 'alignment' in config:
            # Handle both enum values and string values
            alignment = config['alignment']
            if isinstance(alignment, str):
                alignment_map = {
                    'left': WD_ALIGN_PARAGRAPH.LEFT,
                    'center': WD_ALIGN_PARAGRAPH.CENTER,
                    'right': WD_ALIGN_PARAGRAPH.RIGHT,
                    'justify': WD_ALIGN_PARAGRAPH.JUSTIFY
                }
                alignment = alignment_map.get(alignment.lower(), WD_ALIGN_PARAGRAPH.LEFT)
            paragraph_format.alignment = alignment
        if 'space_before' in config:
            space_before = config['space_before']
            if isinstance(space_before, str):
                # Convert string values like '12pt' to Pt objects
                if space_before.endswith('pt'):
                    space_before = Pt(float(space_before[:-2]))
                else:
                    space_before = Pt(float(space_before))
            paragraph_format.space_before = space_before
        if 'space_after' in config:
            space_after = config['space_after']
            if isinstance(space_after, str):
                # Convert string values like '12pt' to Pt objects
                if space_after.endswith('pt'):
                    space_after = Pt(float(space_after[:-2]))
                else:
                    space_after = Pt(float(space_after))
            paragraph_format.space_after = space_after
        if 'line_spacing' in config:
            paragraph_format.line_spacing = config['line_spacing']
    
    def generate_document(self, elements: List[ParsedElement], 
                         output_path: str) -> None:
        """
        Generate Word document from parsed elements.
        
        Args:
            elements: List of parsed LaTeX elements
            output_path: Path to save the generated document
        """
        try:
            # Process document metadata first
            self._process_document_metadata(elements)
            
            # Process content elements
            for element in elements:
                self._process_element(element)
            
            # Save the document
            self.document.save(output_path)
            
        except Exception as e:
            raise ConversionError(
                f"Failed to generate Word document: {str(e)}",
                element_type="document"
            ) from e
    
    def _process_document_metadata(self, elements: List[ParsedElement]) -> None:
        """Process document metadata like title, author, date."""
        # Look for title, author, and date in elements
        title_found = False
        
        for element in elements:
            if element.element_type == 'command':
                cmd_name = element.attributes.get('command', '')
                
                if cmd_name == 'title' and not title_found:
                    self._add_title(element.content)
                    title_found = True
                elif cmd_name == 'author':
                    self._add_author(element.content)
                elif cmd_name == 'date':
                    self._add_date(element.content)
    
    def _add_title(self, title_text: str) -> None:
        """Add title to the document."""
        title_paragraph = self.document.add_paragraph()
        title_paragraph.style = self.document.styles['title']
        title_paragraph.add_run(title_text)
    
    def _add_author(self, author_text: str) -> None:
        """Add author to the document."""
        author_paragraph = self.document.add_paragraph()
        author_paragraph.style = self.document.styles['author']
        author_paragraph.add_run(author_text)
    
    def _add_date(self, date_text: str) -> None:
        """Add date to the document."""
        date_paragraph = self.document.add_paragraph()
        date_paragraph.style = self.document.styles['author']  # Same style as author
        date_paragraph.add_run(date_text)
    
    def _process_element(self, element: ParsedElement) -> None:
        """
        Process a single parsed element.
        
        Args:
            element: ParsedElement to process
        """
        if element.element_type == 'section':
            self._add_section(element)
        elif element.element_type == 'paragraph':
            self._add_paragraph(element)
        elif element.element_type == 'math':
            self._add_math_element(element)
        elif element.element_type == 'math_environment':
            self._add_math_environment(element)
        elif element.element_type == 'list':
            self._add_list(element)
        elif element.element_type == 'table':
            self._add_table(element)
        elif element.element_type == 'figure':
            self._add_figure(element)
        else:
            # Handle other element types or add as regular paragraph
            self._add_generic_element(element)
    
    def _add_section(self, element: ParsedElement) -> None:
        """Add a section heading."""
        level = element.attributes.get('level', 2)
        title = element.content
        
        # Update section counter
        if level < len(self.section_counters):
            self.section_counters[level] += 1
            # Reset lower level counters
            for i in range(level + 1, len(self.section_counters)):
                self.section_counters[i] = 0
        
        # Create section number if not starred
        if not element.attributes.get('starred', False):
            section_number = '.'.join(str(self.section_counters[i]) 
                                    for i in range(2, level + 1) 
                                    if self.section_counters[i] > 0)
            title = f"{section_number} {title}"
        
        # Add section paragraph
        section_paragraph = self.document.add_paragraph()
        
        # Apply appropriate style based on level
        if level == 2:  # section
            section_paragraph.style = self.document.styles.get('section', 'Heading 1')
        elif level == 3:  # subsection
            section_paragraph.style = self.document.styles.get('subsection', 'Heading 2')
        else:
            # Use built-in heading styles for other levels
            heading_level = min(level - 1, 9)  # Word supports up to Heading 9
            section_paragraph.style = f'Heading {heading_level}'
        
        section_paragraph.add_run(title)
    
    def _add_paragraph(self, element: ParsedElement) -> None:
        """Add a regular paragraph."""
        paragraph = self.document.add_paragraph()
        paragraph.style = self.document.styles.get('normal', 'Normal')
        
        # Process paragraph content (may contain inline math)
        self._process_paragraph_content(paragraph, element.content)
    
    def _process_paragraph_content(self, paragraph: Paragraph, content: str) -> None:
        """Process paragraph content, handling inline math and formatting."""
        # Simple implementation - can be enhanced to handle more complex formatting
        run = paragraph.add_run(content)
    
    def _add_math_element(self, element: ParsedElement) -> None:
        """Add a mathematical element."""
        math_type = element.attributes.get('math_type', 'inline')
        
        if math_type == 'inline':
            # Add inline math to current paragraph or create new one
            paragraph = self.document.add_paragraph()
            self._add_inline_math(paragraph, element.content)
        else:
            # Add display math as separate paragraph
            self._add_display_math(element.content)
    
    def _add_math_environment(self, element: ParsedElement) -> None:
        """Add a mathematical environment."""
        env_name = element.attributes.get('environment', '')
        numbered = element.attributes.get('numbered', False)
        
        # Add the math content
        self._add_display_math(element.content, numbered=numbered)
    
    def _add_inline_math(self, paragraph: Paragraph, math_content: str) -> None:
        """Add inline math to a paragraph."""
        # For now, add as regular text with special formatting
        # This can be enhanced to use actual MathML when python-docx supports it better
        run = paragraph.add_run(f"${math_content}$")
        run.italic = True
    
    def _add_display_math(self, math_content: str, numbered: bool = False) -> None:
        """Add display math as a centered paragraph."""
        math_paragraph = self.document.add_paragraph()
        math_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Apply equation style
        if 'equation' in self.styles:
            self._apply_paragraph_style(math_paragraph, self.styles['equation'])
        
        # Add the math content
        run = math_paragraph.add_run(math_content)
        run.italic = True
        
        # Add equation number if needed
        if numbered:
            self.equation_counter += 1
            number_run = math_paragraph.add_run(f"\t({self.equation_counter})")
            number_run.italic = False
    
    def _apply_paragraph_style(self, paragraph: Paragraph, style_config: Dict) -> None:
        """Apply style configuration to a paragraph."""
        if 'alignment' in style_config:
            # Handle both enum values and string values
            alignment = style_config['alignment']
            if isinstance(alignment, str):
                alignment_map = {
                    'left': WD_ALIGN_PARAGRAPH.LEFT,
                    'center': WD_ALIGN_PARAGRAPH.CENTER,
                    'right': WD_ALIGN_PARAGRAPH.RIGHT,
                    'justify': WD_ALIGN_PARAGRAPH.JUSTIFY
                }
                alignment = alignment_map.get(alignment.lower(), WD_ALIGN_PARAGRAPH.LEFT)
            paragraph.alignment = alignment

        paragraph_format = paragraph.paragraph_format
        if 'space_before' in style_config:
            space_before = style_config['space_before']
            if isinstance(space_before, str):
                # Convert string values like '12pt' to Pt objects
                if space_before.endswith('pt'):
                    space_before = Pt(float(space_before[:-2]))
                else:
                    space_before = Pt(float(space_before))
            paragraph_format.space_before = space_before
        if 'space_after' in style_config:
            space_after = style_config['space_after']
            if isinstance(space_after, str):
                # Convert string values like '12pt' to Pt objects
                if space_after.endswith('pt'):
                    space_after = Pt(float(space_after[:-2]))
                else:
                    space_after = Pt(float(space_after))
            paragraph_format.space_after = space_after
    
    def _add_list(self, element: ParsedElement) -> None:
        """Add a list element."""
        # Simple list implementation
        list_items = self._parse_list_items(element.content)
        
        for item in list_items:
            paragraph = self.document.add_paragraph(item, style='List Bullet')
    
    def _parse_list_items(self, list_content: str) -> List[str]:
        """Parse list items from LaTeX list content."""
        # Simple implementation - extract \item content
        items = []
        item_pattern = r'\\item\s+(.*?)(?=\\item|\Z)'
        matches = re.finditer(item_pattern, list_content, re.DOTALL)
        
        for match in matches:
            item_text = match.group(1).strip()
            items.append(item_text)
        
        return items
    
    def _add_table(self, element: ParsedElement) -> None:
        """Add a table element."""
        # Placeholder for table implementation
        paragraph = self.document.add_paragraph()
        paragraph.add_run(f"[Table: {element.attributes.get('environment', 'table')}]")
    
    def _add_figure(self, element: ParsedElement) -> None:
        """Add a figure element."""
        # Placeholder for figure implementation
        paragraph = self.document.add_paragraph()
        paragraph.add_run(f"[Figure: {element.attributes.get('environment', 'figure')}]")
    
    def _add_generic_element(self, element: ParsedElement) -> None:
        """Add a generic element as a paragraph."""
        paragraph = self.document.add_paragraph()
        paragraph.add_run(element.content)
    
    def add_mathml_equation(self, mathml_element: MathMLElement) -> None:
        """
        Add a MathML equation to the document.
        
        Args:
            mathml_element: MathMLElement containing the equation
        """
        # This is a placeholder for future MathML integration
        # Currently python-docx has limited MathML support
        if mathml_element.display_type == 'block':
            self._add_display_math(mathml_element.original_latex)
        else:
            paragraph = self.document.add_paragraph()
            self._add_inline_math(paragraph, mathml_element.original_latex)
    
    def get_document_statistics(self) -> Dict[str, int]:
        """
        Get statistics about the generated document.
        
        Returns:
            Dictionary with document statistics
        """
        return {
            'total_paragraphs': len(self.document.paragraphs),
            'total_equations': self.equation_counter,
            'total_sections': sum(1 for p in self.document.paragraphs 
                                if p.style.name.startswith('Heading'))
        }
