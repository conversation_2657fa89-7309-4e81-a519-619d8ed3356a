# -*- coding: utf-8 -*-
#
# Automatically generated from unicode.xml by gen_xml_dic.py
#

uni2latex = {
0x0023: '\\#',
0x0024: '\\textdollar',
0x0025: '\\%',
0x0026: '\\&',
0x0027: '\\textquotesingle',
0x002A: '\\ast',
0x005C: '\\textbackslash',
0x005E: '\\^{}',
0x005F: '\\_',
0x0060: '\\textasciigrave',
0x007B: '\\lbrace',
0x007C: '\\vert',
0x007D: '\\rbrace',
0x007E: '\\textasciitilde',
0x00A0: '~',
0x00A1: '\\textexclamdown',
0x00A2: '\\textcent',
0x00A3: '\\textsterling',
0x00A4: '\\textcurrency',
0x00A5: '\\textyen',
0x00A6: '\\textbrokenbar',
0x00A7: '\\textsection',
0x00A8: '\\textasciidieresis',
0x00A9: '\\textcopyright',
0x00AA: '\\textordfeminine',
0x00AB: '\\guillemotleft',
0x00AC: '\\lnot',
0x00AD: '\\-',
0x00AE: '\\textregistered',
0x00AF: '\\textasciimacron',
0x00B0: '\\textdegree',
0x00B1: '\\pm',
0x00B2: '{^2}',
0x00B3: '{^3}',
0x00B4: '\\textasciiacute',
0x00B5: '\\mathrm{\\mu}',
0x00B6: '\\textparagraph',
0x00B7: '\\cdot',
0x00B8: '\\c{}',
0x00B9: '{^1}',
0x00BA: '\\textordmasculine',
0x00BB: '\\guillemotright',
0x00BC: '\\textonequarter',
0x00BD: '\\textonehalf',
0x00BE: '\\textthreequarters',
0x00BF: '\\textquestiondown',
0x00C0: '\\`{A}',
0x00C1: "\\'{A}",
0x00C2: '\\^{A}',
0x00C3: '\\~{A}',
0x00C4: '\\"{A}',
0x00C5: '\\AA',
0x00C6: '\\AE',
0x00C7: '\\c{C}',
0x00C8: '\\`{E}',
0x00C9: "\\'{E}",
0x00CA: '\\^{E}',
0x00CB: '\\"{E}',
0x00CC: '\\`{I}',
0x00CD: "\\'{I}",
0x00CE: '\\^{I}',
0x00CF: '\\"{I}',
0x00D0: '\\DH',
0x00D1: '\\~{N}',
0x00D2: '\\`{O}',
0x00D3: "\\'{O}",
0x00D4: '\\^{O}',
0x00D5: '\\~{O}',
0x00D6: '\\"{O}',
0x00D7: '\\texttimes',
0x00D8: '\\O',
0x00D9: '\\`{U}',
0x00DA: "\\'{U}",
0x00DB: '\\^{U}',
0x00DC: '\\"{U}',
0x00DD: "\\'{Y}",
0x00DE: '\\TH',
0x00DF: '\\ss',
0x00E0: '\\`{a}',
0x00E1: "\\'{a}",
0x00E2: '\\^{a}',
0x00E3: '\\~{a}',
0x00E4: '\\"{a}',
0x00E5: '\\aa',
0x00E6: '\\ae',
0x00E7: '\\c{c}',
0x00E8: '\\`{e}',
0x00E9: "\\'{e}",
0x00EA: '\\^{e}',
0x00EB: '\\"{e}',
0x00EC: '\\`{\\i}',
0x00ED: "\\'{\\i}",
0x00EE: '\\^{\\i}',
0x00EF: '\\"{\\i}',
0x00F0: '\\dh',
0x00F1: '\\~{n}',
0x00F2: '\\`{o}',
0x00F3: "\\'{o}",
0x00F4: '\\^{o}',
0x00F5: '\\~{o}',
0x00F6: '\\"{o}',
0x00F7: '\\div',
0x00F8: '\\o',
0x00F9: '\\`{u}',
0x00FA: "\\'{u}",
0x00FB: '\\^{u}',
0x00FC: '\\"{u}',
0x00FD: "\\'{y}",
0x00FE: '\\th',
0x00FF: '\\"{y}',
0x0100: '\\={A}',
0x0101: '\\={a}',
0x0102: '\\u{A}',
0x0103: '\\u{a}',
0x0104: '\\k{A}',
0x0105: '\\k{a}',
0x0106: "\\'{C}",
0x0107: "\\'{c}",
0x0108: '\\^{C}',
0x0109: '\\^{c}',
0x010A: '\\.{C}',
0x010B: '\\.{c}',
0x010C: '\\v{C}',
0x010D: '\\v{c}',
0x010E: '\\v{D}',
0x010F: '\\v{d}',
0x0110: '\\DJ',
0x0111: '\\dj',
0x0112: '\\={E}',
0x0113: '\\={e}',
0x0114: '\\u{E}',
0x0115: '\\u{e}',
0x0116: '\\.{E}',
0x0117: '\\.{e}',
0x0118: '\\k{E}',
0x0119: '\\k{e}',
0x011A: '\\v{E}',
0x011B: '\\v{e}',
0x011C: '\\^{G}',
0x011D: '\\^{g}',
0x011E: '\\u{G}',
0x011F: '\\u{g}',
0x0120: '\\.{G}',
0x0121: '\\.{g}',
0x0122: '\\c{G}',
0x0123: '\\c{g}',
0x0124: '\\^{H}',
0x0125: '\\^{h}',
0x0126: '{\\fontencoding{LELA}\\selectfont\\char40}',
0x0128: '\\~{I}',
0x0129: '\\~{\\i}',
0x012A: '\\={I}',
0x012B: '\\={\\i}',
0x012C: '\\u{I}',
0x012D: '\\u{\\i}',
0x012E: '\\k{I}',
0x012F: '\\k{i}',
0x0130: '\\.{I}',
0x0131: '\\i',
0x0132: 'IJ',
0x0133: 'ij',
0x0134: '\\^{J}',
0x0135: '\\^{\\j}',
0x0136: '\\c{K}',
0x0137: '\\c{k}',
0x0138: '{\\fontencoding{LELA}\\selectfont\\char91}',
0x0139: "\\'{L}",
0x013A: "\\'{l}",
0x013B: '\\c{L}',
0x013C: '\\c{l}',
0x013D: '\\v{L}',
0x013E: '\\v{l}',
0x013F: '{\\fontencoding{LELA}\\selectfont\\char201}',
0x0140: '{\\fontencoding{LELA}\\selectfont\\char202}',
0x0141: '\\L',
0x0142: '\\l',
0x0143: "\\'{N}",
0x0144: "\\'{n}",
0x0145: '\\c{N}',
0x0146: '\\c{n}',
0x0147: '\\v{N}',
0x0148: '\\v{n}',
0x0149: "'n",
0x014A: '\\NG',
0x014B: '\\ng',
0x014C: '\\={O}',
0x014D: '\\={o}',
0x014E: '\\u{O}',
0x014F: '\\u{o}',
0x0150: '\\H{O}',
0x0151: '\\H{o}',
0x0152: '\\OE',
0x0153: '\\oe',
0x0154: "\\'{R}",
0x0155: "\\'{r}",
0x0156: '\\c{R}',
0x0157: '\\c{r}',
0x0158: '\\v{R}',
0x0159: '\\v{r}',
0x015A: "\\'{S}",
0x015B: "\\'{s}",
0x015C: '\\^{S}',
0x015D: '\\^{s}',
0x015E: '\\c{S}',
0x015F: '\\c{s}',
0x0160: '\\v{S}',
0x0161: '\\v{s}',
0x0162: '\\c{T}',
0x0163: '\\c{t}',
0x0164: '\\v{T}',
0x0165: '\\v{t}',
0x0166: '{\\fontencoding{LELA}\\selectfont\\char47}',
0x0167: '{\\fontencoding{LELA}\\selectfont\\char63}',
0x0168: '\\~{U}',
0x0169: '\\~{u}',
0x016A: '\\={U}',
0x016B: '\\={u}',
0x016C: '\\u{U}',
0x016D: '\\u{u}',
0x016E: '\\r{U}',
0x016F: '\\r{u}',
0x0170: '\\H{U}',
0x0171: '\\H{u}',
0x0172: '\\k{U}',
0x0173: '\\k{u}',
0x0174: '\\^{W}',
0x0175: '\\^{w}',
0x0176: '\\^{Y}',
0x0177: '\\^{y}',
0x0178: '\\"{Y}',
0x0179: "\\'{Z}",
0x017A: "\\'{z}",
0x017B: '\\.{Z}',
0x017C: '\\.{z}',
0x017D: '\\v{Z}',
0x017E: '\\v{z}',
0x0192: 'f',
0x0195: '\\texthvlig',
0x019E: '\\textnrleg',
0x01AA: '\\eth',
0x01BA: '{\\fontencoding{LELA}\\selectfont\\char195}',
0x01C2: '\\textdoublepipe',
0x01F5: "\\'{g}",
0x0258: '{\\fontencoding{LEIP}\\selectfont\\char61}',
0x025B: '\\varepsilon',
0x0261: 'g',
0x0278: '\\textphi',
0x027F: '{\\fontencoding{LEIP}\\selectfont\\char202}',
0x029E: '\\textturnk',
0x02BC: "'",
0x02C7: '\\textasciicaron',
0x02D8: '\\textasciibreve',
0x02D9: '\\textperiodcentered',
0x02DA: '\\r{}',
0x02DB: '\\k{}',
0x02DC: '\\texttildelow',
0x02DD: '\\H{}',
0x02E5: '\\tone{55}',
0x02E6: '\\tone{44}',
0x02E7: '\\tone{33}',
0x02E8: '\\tone{22}',
0x02E9: '\\tone{11}',
0x0300: '\\`',
0x0301: "\\'",
0x0302: '\\^',
0x0303: '\\~',
0x0304: '\\=',
0x0306: '\\u',
0x0307: '\\.',
0x0308: '\\"',
0x030A: '\\r',
0x030B: '\\H',
0x030C: '\\v',
0x030F: '\\cyrchar\\C',
0x0311: '{\\fontencoding{LECO}\\selectfont\\char177}',
0x0318: '{\\fontencoding{LECO}\\selectfont\\char184}',
0x0319: '{\\fontencoding{LECO}\\selectfont\\char185}',
0x0327: '\\c',
0x0328: '\\k',
0x032B: '{\\fontencoding{LECO}\\selectfont\\char203}',
0x032F: '{\\fontencoding{LECO}\\selectfont\\char207}',
0x0337: '{\\fontencoding{LECO}\\selectfont\\char215}',
0x0338: '{\\fontencoding{LECO}\\selectfont\\char216}',
0x033A: '{\\fontencoding{LECO}\\selectfont\\char218}',
0x033B: '{\\fontencoding{LECO}\\selectfont\\char219}',
0x033C: '{\\fontencoding{LECO}\\selectfont\\char220}',
0x033D: '{\\fontencoding{LECO}\\selectfont\\char221}',
0x0361: '{\\fontencoding{LECO}\\selectfont\\char225}',
0x0386: "\\'{A}",
0x0388: "\\'{E}",
0x0389: "\\'{H}",
0x038A: "\\'{}{I}",
0x038C: "\\'{}O",
0x038E: "\\mathrm{'Y}",
0x038F: "\\mathrm{'\\Omega}",
0x0390: '\\acute{\\ddot{\\iota}}',
0x0391: '\\Alpha',
0x0392: '\\Beta',
0x0393: '\\Gamma',
0x0394: '\\Delta',
0x0395: '\\Epsilon',
0x0396: '\\Zeta',
0x0397: '\\Eta',
0x0398: '\\Theta',
0x0399: '\\Iota',
0x039A: '\\Kappa',
0x039B: '\\Lambda',
0x039C: 'M',
0x039D: 'N',
0x039E: '\\Xi',
0x039F: 'O',
0x03A0: '\\Pi',
0x03A1: '\\Rho',
0x03A3: '\\Sigma',
0x03A4: '\\Tau',
0x03A5: '\\Upsilon',
0x03A6: '\\Phi',
0x03A7: '\\Chi',
0x03A8: '\\Psi',
0x03A9: '\\Omega',
0x03AA: '\\mathrm{\\ddot{I}}',
0x03AB: '\\mathrm{\\ddot{Y}}',
0x03AC: "\\'{$\\alpha$}",
0x03AD: '\\acute{\\epsilon}',
0x03AE: '\\acute{\\eta}',
0x03AF: '\\acute{\\iota}',
0x03B0: '\\acute{\\ddot{\\upsilon}}',
0x03B1: '\\alpha',
0x03B2: '\\beta',
0x03B3: '\\gamma',
0x03B4: '\\delta',
0x03B5: '\\epsilon',
0x03B6: '\\zeta',
0x03B7: '\\eta',
0x03B8: '\\texttheta',
0x03B9: '\\iota',
0x03BA: '\\kappa',
0x03BB: '\\lambda',
0x03BC: '\\mu',
0x03BD: '\\nu',
0x03BE: '\\xi',
0x03BF: 'o',
0x03C0: '\\pi',
0x03C1: '\\rho',
0x03C2: '\\varsigma',
0x03C3: '\\sigma',
0x03C4: '\\tau',
0x03C5: '\\upsilon',
0x03C6: '\\varphi',
0x03C7: '\\chi',
0x03C8: '\\psi',
0x03C9: '\\omega',
0x03CA: '\\ddot{\\iota}',
0x03CB: '\\ddot{\\upsilon}',
0x03CC: "\\'{o}",
0x03CD: '\\acute{\\upsilon}',
0x03CE: '\\acute{\\omega}',
0x03D0: '\\Pisymbol{ppi022}{87}',
0x03D1: '\\textvartheta',
0x03D2: '\\Upsilon',
0x03D5: '\\phi',
0x03D6: '\\varpi',
0x03DA: '\\Stigma',
0x03DC: '\\Digamma',
0x03DD: '\\digamma',
0x03DE: '\\Koppa',
0x03E0: '\\Sampi',
0x03F0: '\\varkappa',
0x03F1: '\\varrho',
0x03F4: '\\textTheta',
0x03F6: '\\backepsilon',
0x0401: '\\cyrchar\\CYRYO',
0x0402: '\\cyrchar\\CYRDJE',
0x0403: "\\cyrchar{\\'\\CYRG}",
0x0404: '\\cyrchar\\CYRIE',
0x0405: '\\cyrchar\\CYRDZE',
0x0406: '\\cyrchar\\CYRII',
0x0407: '\\cyrchar\\CYRYI',
0x0408: '\\cyrchar\\CYRJE',
0x0409: '\\cyrchar\\CYRLJE',
0x040A: '\\cyrchar\\CYRNJE',
0x040B: '\\cyrchar\\CYRTSHE',
0x040C: "\\cyrchar{\\'\\CYRK}",
0x040E: '\\cyrchar\\CYRUSHRT',
0x040F: '\\cyrchar\\CYRDZHE',
0x0410: '\\cyrchar\\CYRA',
0x0411: '\\cyrchar\\CYRB',
0x0412: '\\cyrchar\\CYRV',
0x0413: '\\cyrchar\\CYRG',
0x0414: '\\cyrchar\\CYRD',
0x0415: '\\cyrchar\\CYRE',
0x0416: '\\cyrchar\\CYRZH',
0x0417: '\\cyrchar\\CYRZ',
0x0418: '\\cyrchar\\CYRI',
0x0419: '\\cyrchar\\CYRISHRT',
0x041A: '\\cyrchar\\CYRK',
0x041B: '\\cyrchar\\CYRL',
0x041C: '\\cyrchar\\CYRM',
0x041D: '\\cyrchar\\CYRN',
0x041E: '\\cyrchar\\CYRO',
0x041F: '\\cyrchar\\CYRP',
0x0420: '\\cyrchar\\CYRR',
0x0421: '\\cyrchar\\CYRS',
0x0422: '\\cyrchar\\CYRT',
0x0423: '\\cyrchar\\CYRU',
0x0424: '\\cyrchar\\CYRF',
0x0425: '\\cyrchar\\CYRH',
0x0426: '\\cyrchar\\CYRC',
0x0427: '\\cyrchar\\CYRCH',
0x0428: '\\cyrchar\\CYRSH',
0x0429: '\\cyrchar\\CYRSHCH',
0x042A: '\\cyrchar\\CYRHRDSN',
0x042B: '\\cyrchar\\CYRERY',
0x042C: '\\cyrchar\\CYRSFTSN',
0x042D: '\\cyrchar\\CYREREV',
0x042E: '\\cyrchar\\CYRYU',
0x042F: '\\cyrchar\\CYRYA',
0x0430: '\\cyrchar\\cyra',
0x0431: '\\cyrchar\\cyrb',
0x0432: '\\cyrchar\\cyrv',
0x0433: '\\cyrchar\\cyrg',
0x0434: '\\cyrchar\\cyrd',
0x0435: '\\cyrchar\\cyre',
0x0436: '\\cyrchar\\cyrzh',
0x0437: '\\cyrchar\\cyrz',
0x0438: '\\cyrchar\\cyri',
0x0439: '\\cyrchar\\cyrishrt',
0x043A: '\\cyrchar\\cyrk',
0x043B: '\\cyrchar\\cyrl',
0x043C: '\\cyrchar\\cyrm',
0x043D: '\\cyrchar\\cyrn',
0x043E: '\\cyrchar\\cyro',
0x043F: '\\cyrchar\\cyrp',
0x0440: '\\cyrchar\\cyrr',
0x0441: '\\cyrchar\\cyrs',
0x0442: '\\cyrchar\\cyrt',
0x0443: '\\cyrchar\\cyru',
0x0444: '\\cyrchar\\cyrf',
0x0445: '\\cyrchar\\cyrh',
0x0446: '\\cyrchar\\cyrc',
0x0447: '\\cyrchar\\cyrch',
0x0448: '\\cyrchar\\cyrsh',
0x0449: '\\cyrchar\\cyrshch',
0x044A: '\\cyrchar\\cyrhrdsn',
0x044B: '\\cyrchar\\cyrery',
0x044C: '\\cyrchar\\cyrsftsn',
0x044D: '\\cyrchar\\cyrerev',
0x044E: '\\cyrchar\\cyryu',
0x044F: '\\cyrchar\\cyrya',
0x0451: '\\cyrchar\\cyryo',
0x0452: '\\cyrchar\\cyrdje',
0x0453: "\\cyrchar{\\'\\cyrg}",
0x0454: '\\cyrchar\\cyrie',
0x0455: '\\cyrchar\\cyrdze',
0x0456: '\\cyrchar\\cyrii',
0x0457: '\\cyrchar\\cyryi',
0x0458: '\\cyrchar\\cyrje',
0x0459: '\\cyrchar\\cyrlje',
0x045A: '\\cyrchar\\cyrnje',
0x045B: '\\cyrchar\\cyrtshe',
0x045C: "\\cyrchar{\\'\\cyrk}",
0x045E: '\\cyrchar\\cyrushrt',
0x045F: '\\cyrchar\\cyrdzhe',
0x0460: '\\cyrchar\\CYROMEGA',
0x0461: '\\cyrchar\\cyromega',
0x0462: '\\cyrchar\\CYRYAT',
0x0464: '\\cyrchar\\CYRIOTE',
0x0465: '\\cyrchar\\cyriote',
0x0466: '\\cyrchar\\CYRLYUS',
0x0467: '\\cyrchar\\cyrlyus',
0x0468: '\\cyrchar\\CYRIOTLYUS',
0x0469: '\\cyrchar\\cyriotlyus',
0x046A: '\\cyrchar\\CYRBYUS',
0x046C: '\\cyrchar\\CYRIOTBYUS',
0x046D: '\\cyrchar\\cyriotbyus',
0x046E: '\\cyrchar\\CYRKSI',
0x046F: '\\cyrchar\\cyrksi',
0x0470: '\\cyrchar\\CYRPSI',
0x0471: '\\cyrchar\\cyrpsi',
0x0472: '\\cyrchar\\CYRFITA',
0x0474: '\\cyrchar\\CYRIZH',
0x0478: '\\cyrchar\\CYRUK',
0x0479: '\\cyrchar\\cyruk',
0x047A: '\\cyrchar\\CYROMEGARND',
0x047B: '\\cyrchar\\cyromegarnd',
0x047C: '\\cyrchar\\CYROMEGATITLO',
0x047D: '\\cyrchar\\cyromegatitlo',
0x047E: '\\cyrchar\\CYROT',
0x047F: '\\cyrchar\\cyrot',
0x0480: '\\cyrchar\\CYRKOPPA',
0x0481: '\\cyrchar\\cyrkoppa',
0x0482: '\\cyrchar\\cyrthousands',
0x0488: '\\cyrchar\\cyrhundredthousands',
0x0489: '\\cyrchar\\cyrmillions',
0x048C: '\\cyrchar\\CYRSEMISFTSN',
0x048D: '\\cyrchar\\cyrsemisftsn',
0x048E: '\\cyrchar\\CYRRTICK',
0x048F: '\\cyrchar\\cyrrtick',
0x0490: '\\cyrchar\\CYRGUP',
0x0491: '\\cyrchar\\cyrgup',
0x0492: '\\cyrchar\\CYRGHCRS',
0x0493: '\\cyrchar\\cyrghcrs',
0x0494: '\\cyrchar\\CYRGHK',
0x0495: '\\cyrchar\\cyrghk',
0x0496: '\\cyrchar\\CYRZHDSC',
0x0497: '\\cyrchar\\cyrzhdsc',
0x0498: '\\cyrchar\\CYRZDSC',
0x0499: '\\cyrchar\\cyrzdsc',
0x049A: '\\cyrchar\\CYRKDSC',
0x049B: '\\cyrchar\\cyrkdsc',
0x049C: '\\cyrchar\\CYRKVCRS',
0x049D: '\\cyrchar\\cyrkvcrs',
0x049E: '\\cyrchar\\CYRKHCRS',
0x049F: '\\cyrchar\\cyrkhcrs',
0x04A0: '\\cyrchar\\CYRKBEAK',
0x04A1: '\\cyrchar\\cyrkbeak',
0x04A2: '\\cyrchar\\CYRNDSC',
0x04A3: '\\cyrchar\\cyrndsc',
0x04A4: '\\cyrchar\\CYRNG',
0x04A5: '\\cyrchar\\cyrng',
0x04A6: '\\cyrchar\\CYRPHK',
0x04A7: '\\cyrchar\\cyrphk',
0x04A8: '\\cyrchar\\CYRABHHA',
0x04A9: '\\cyrchar\\cyrabhha',
0x04AA: '\\cyrchar\\CYRSDSC',
0x04AB: '\\cyrchar\\cyrsdsc',
0x04AC: '\\cyrchar\\CYRTDSC',
0x04AD: '\\cyrchar\\cyrtdsc',
0x04AE: '\\cyrchar\\CYRY',
0x04AF: '\\cyrchar\\cyry',
0x04B0: '\\cyrchar\\CYRYHCRS',
0x04B1: '\\cyrchar\\cyryhcrs',
0x04B2: '\\cyrchar\\CYRHDSC',
0x04B3: '\\cyrchar\\cyrhdsc',
0x04B4: '\\cyrchar\\CYRTETSE',
0x04B5: '\\cyrchar\\cyrtetse',
0x04B6: '\\cyrchar\\CYRCHRDSC',
0x04B7: '\\cyrchar\\cyrchrdsc',
0x04B8: '\\cyrchar\\CYRCHVCRS',
0x04B9: '\\cyrchar\\cyrchvcrs',
0x04BA: '\\cyrchar\\CYRSHHA',
0x04BB: '\\cyrchar\\cyrshha',
0x04BC: '\\cyrchar\\CYRABHCH',
0x04BD: '\\cyrchar\\cyrabhch',
0x04BE: '\\cyrchar\\CYRABHCHDSC',
0x04BF: '\\cyrchar\\cyrabhchdsc',
0x04C0: '\\cyrchar\\CYRpalochka',
0x04C3: '\\cyrchar\\CYRKHK',
0x04C4: '\\cyrchar\\cyrkhk',
0x04C7: '\\cyrchar\\CYRNHK',
0x04C8: '\\cyrchar\\cyrnhk',
0x04CB: '\\cyrchar\\CYRCHLDSC',
0x04CC: '\\cyrchar\\cyrchldsc',
0x04D4: '\\cyrchar\\CYRAE',
0x04D5: '\\cyrchar\\cyrae',
0x04D8: '\\cyrchar\\CYRSCHWA',
0x04D9: '\\cyrchar\\cyrschwa',
0x04E0: '\\cyrchar\\CYRABHDZE',
0x04E1: '\\cyrchar\\cyrabhdze',
0x04E8: '\\cyrchar\\CYROTLD',
0x04E9: '\\cyrchar\\cyrotld',
0x2002: '\\hspace{0.6em}',
0x2003: '\\hspace{1em}',
0x2004: '\\hspace{0.33em}',
0x2005: '\\hspace{0.25em}',
0x2006: '\\hspace{0.166em}',
0x2007: '\\hphantom{0}',
0x2008: '\\hphantom{,}',
0x2009: '\\hspace{0.167em}',
0x200A: '\\mkern1mu ',
0x2010: '-',
0x2013: '\\textendash',
0x2014: '\\textemdash',
0x2015: '\\rule{1em}{1pt}',
0x2016: '\\Vert',
0x2018: '`',
0x2019: "'",
0x201A: ',',
0x201C: '\\textquotedblleft',
0x201D: '\\textquotedblright',
0x201E: ',,',
0x2020: '\\textdagger',
0x2021: '\\textdaggerdbl',
0x2022: '\\textbullet',
0x2024: '.',
0x2025: '..',
0x2026: '\\ldots',
0x2030: '\\textperthousand',
0x2031: '\\textpertenthousand',
0x2032: "{'}",
0x2033: "{''}",
0x2034: "{'''}",
0x2035: '\\backprime',
0x2039: '\\guilsinglleft',
0x203A: '\\guilsinglright',
0x2057: "''''",
0x205F: '\\mkern4mu ',
0x2060: '\\nolinebreak',
0x20AC: '\\mbox{\\texteuro} ',
0x20DB: '\\dddot',
0x20DC: '\\ddddot',
0x2102: '\\mathbb{C}',
0x210A: '\\mathscr{g}',
0x210B: '\\mathscr{H}',
0x210C: '\\mathfrak{H}',
0x210D: '\\mathbb{H}',
0x210F: '\\hslash',
0x2110: '\\mathscr{I}',
0x2111: '\\mathfrak{I}',
0x2112: '\\mathscr{L}',
0x2113: '\\mathscr{l}',
0x2115: '\\mathbb{N}',
0x2116: '\\cyrchar\\textnumero',
0x2118: '\\wp',
0x2119: '\\mathbb{P}',
0x211A: '\\mathbb{Q}',
0x211B: '\\mathscr{R}',
0x211C: '\\mathfrak{R}',
0x211D: '\\mathbb{R}',
0x2122: '\\texttrademark',
0x2124: '\\mathbb{Z}',
0x2126: '\\Omega',
0x2127: '\\mho',
0x2128: '\\mathfrak{Z}',
0x212B: '\\AA',
0x212C: '\\mathscr{B}',
0x212D: '\\mathfrak{C}',
0x212F: '\\mathscr{e}',
0x2130: '\\mathscr{E}',
0x2131: '\\mathscr{F}',
0x2133: '\\mathscr{M}',
0x2134: '\\mathscr{o}',
0x2135: '\\aleph',
0x2136: '\\beth',
0x2137: '\\gimel',
0x2138: '\\daleth',
0x2153: '\\textfrac{1}{3}',
0x2154: '\\textfrac{2}{3}',
0x2155: '\\textfrac{1}{5}',
0x2156: '\\textfrac{2}{5}',
0x2157: '\\textfrac{3}{5}',
0x2158: '\\textfrac{4}{5}',
0x2159: '\\textfrac{1}{6}',
0x215A: '\\textfrac{5}{6}',
0x215B: '\\textfrac{1}{8}',
0x215C: '\\textfrac{3}{8}',
0x215D: '\\textfrac{5}{8}',
0x215E: '\\textfrac{7}{8}',
0x2190: '\\leftarrow',
0x2191: '\\uparrow',
0x2192: '\\rightarrow',
0x2193: '\\downarrow',
0x2194: '\\leftrightarrow',
0x2195: '\\updownarrow',
0x2196: '\\nwarrow',
0x2197: '\\nearrow',
0x2198: '\\searrow',
0x2199: '\\swarrow',
0x219A: '\\nleftarrow',
0x219B: '\\nrightarrow',
0x219C: '\\arrowwaveleft',
0x219D: '\\arrowwaveright',
0x219E: '\\twoheadleftarrow',
0x21A0: '\\twoheadrightarrow',
0x21A2: '\\leftarrowtail',
0x21A3: '\\rightarrowtail',
0x21A6: '\\mapsto',
0x21A9: '\\hookleftarrow',
0x21AA: '\\hookrightarrow',
0x21AB: '\\looparrowleft',
0x21AC: '\\looparrowright',
0x21AD: '\\leftrightsquigarrow',
0x21AE: '\\nleftrightarrow',
0x21B0: '\\Lsh',
0x21B1: '\\Rsh',
0x21B6: '\\curvearrowleft',
0x21B7: '\\curvearrowright',
0x21BA: '\\circlearrowleft',
0x21BB: '\\circlearrowright',
0x21BC: '\\leftharpoonup',
0x21BD: '\\leftharpoondown',
0x21BE: '\\upharpoonright',
0x21BF: '\\upharpoonleft',
0x21C0: '\\rightharpoonup',
0x21C1: '\\rightharpoondown',
0x21C2: '\\downharpoonright',
0x21C3: '\\downharpoonleft',
0x21C4: '\\rightleftarrows',
0x21C5: '\\dblarrowupdown',
0x21C6: '\\leftrightarrows',
0x21C7: '\\leftleftarrows',
0x21C8: '\\upuparrows',
0x21C9: '\\rightrightarrows',
0x21CA: '\\downdownarrows',
0x21CB: '\\leftrightharpoons',
0x21CC: '\\rightleftharpoons',
0x21CD: '\\nLeftarrow',
0x21CE: '\\nLeftrightarrow',
0x21CF: '\\nRightarrow',
0x21D0: '\\Leftarrow',
0x21D1: '\\Uparrow',
0x21D2: '\\Rightarrow',
0x21D3: '\\Downarrow',
0x21D4: '\\Leftrightarrow',
0x21D5: '\\Updownarrow',
0x21DA: '\\Lleftarrow',
0x21DB: '\\Rrightarrow',
0x21DD: '\\rightsquigarrow',
0x21F5: '\\DownArrowUpArrow',
0x2200: '\\forall',
0x2201: '\\complement',
0x2202: '\\partial',
0x2203: '\\exists',
0x2204: '\\nexists',
0x2205: '\\varnothing',
0x2207: '\\nabla',
0x2208: '\\in',
0x2209: '\\not\\in',
0x220B: '\\ni',
0x220C: '\\not\\ni',
0x220F: '\\prod',
0x2210: '\\coprod',
0x2211: '\\sum',
0x2212: '-',
0x2213: '\\mp',
0x2214: '\\dotplus',
0x2216: '\\setminus',
0x2217: '{_\\ast}',
0x2218: '\\circ',
0x2219: '\\bullet',
0x221A: '\\surd',
0x221D: '\\propto',
0x221E: '\\infty',
0x221F: '\\rightangle',
0x2220: '\\angle',
0x2221: '\\measuredangle',
0x2222: '\\sphericalangle',
0x2223: '\\mid',
0x2224: '\\nmid',
0x2225: '\\parallel',
0x2226: '\\nparallel',
0x2227: '\\wedge',
0x2228: '\\vee',
0x2229: '\\cap',
0x222A: '\\cup',
0x222B: '\\int',
0x222C: '\\int\\!\\int',
0x222D: '\\int\\!\\int\\!\\int',
0x222E: '\\oint',
0x222F: '\\surfintegral',
0x2230: '\\volintegral',
0x2231: '\\clwintegral',
0x2234: '\\therefore',
0x2235: '\\because',
0x2237: '\\Colon',
0x223A: '\\mathbin{{:}\\!\\!{-}\\!\\!{:}}',
0x223B: '\\homothetic',
0x223C: '\\sim',
0x223D: '\\backsim',
0x223E: '\\lazysinv',
0x2240: '\\wr',
0x2241: '\\not\\sim',
0x2243: '\\simeq',
0x2244: '\\not\\simeq',
0x2245: '\\cong',
0x2246: '\\approxnotequal',
0x2247: '\\not\\cong',
0x2248: '\\approx',
0x2249: '\\not\\approx',
0x224A: '\\approxeq',
0x224B: '\\tildetrpl',
0x224C: '\\allequal',
0x224D: '\\asymp',
0x224E: '\\Bumpeq',
0x224F: '\\bumpeq',
0x2250: '\\doteq',
0x2251: '\\doteqdot',
0x2252: '\\fallingdotseq',
0x2253: '\\risingdotseq',
0x2254: ':=',
0x2255: '=:',
0x2256: '\\eqcirc',
0x2257: '\\circeq',
0x2259: '\\estimates',
0x225B: '\\starequal',
0x225C: '\\triangleq',
0x2260: '\\not =',
0x2261: '\\equiv',
0x2262: '\\not\\equiv',
0x2264: '\\leq',
0x2265: '\\geq',
0x2266: '\\leqq',
0x2267: '\\geqq',
0x2268: '\\lneqq',
0x2269: '\\gneqq',
0x226A: '\\ll',
0x226B: '\\gg',
0x226C: '\\between',
0x226D: '\\not\\kern-0.3em\\times',
0x226E: '\\not<',
0x226F: '\\not>',
0x2270: '\\not\\leq',
0x2271: '\\not\\geq',
0x2272: '\\lessequivlnt',
0x2273: '\\greaterequivlnt',
0x2276: '\\lessgtr',
0x2277: '\\gtrless',
0x2278: '\\notlessgreater',
0x2279: '\\notgreaterless',
0x227A: '\\prec',
0x227B: '\\succ',
0x227C: '\\preccurlyeq',
0x227D: '\\succcurlyeq',
0x227E: '\\precapprox',
0x227F: '\\succapprox',
0x2280: '\\not\\prec',
0x2281: '\\not\\succ',
0x2282: '\\subset',
0x2283: '\\supset',
0x2284: '\\not\\subset',
0x2285: '\\not\\supset',
0x2286: '\\subseteq',
0x2287: '\\supseteq',
0x2288: '\\not\\subseteq',
0x2289: '\\not\\supseteq',
0x228A: '\\subsetneq',
0x228B: '\\supsetneq',
0x228E: '\\uplus',
0x228F: '\\sqsubset',
0x2290: '\\sqsupset',
0x2291: '\\sqsubseteq',
0x2292: '\\sqsupseteq',
0x2293: '\\sqcap',
0x2294: '\\sqcup',
0x2295: '\\oplus',
0x2296: '\\ominus',
0x2297: '\\otimes',
0x2298: '\\oslash',
0x2299: '\\odot',
0x229A: '\\circledcirc',
0x229B: '\\circledast',
0x229D: '\\circleddash',
0x229E: '\\boxplus',
0x229F: '\\boxminus',
0x22A0: '\\boxtimes',
0x22A1: '\\boxdot',
0x22A2: '\\vdash',
0x22A3: '\\dashv',
0x22A4: '\\top',
0x22A5: '\\perp',
0x22A7: '\\truestate',
0x22A8: '\\forcesextra',
0x22A9: '\\Vdash',
0x22AA: '\\Vvdash',
0x22AB: '\\VDash',
0x22AC: '\\nvdash',
0x22AD: '\\nvDash',
0x22AE: '\\nVdash',
0x22AF: '\\nVDash',
0x22B2: '\\vartriangleleft',
0x22B3: '\\vartriangleright',
0x22B4: '\\trianglelefteq',
0x22B5: '\\trianglerighteq',
0x22B6: '\\original',
0x22B7: '\\image',
0x22B8: '\\multimap',
0x22B9: '\\hermitconjmatrix',
0x22BA: '\\intercal',
0x22BB: '\\veebar',
0x22BE: '\\rightanglearc',
0x22C2: '\\bigcap',
0x22C3: '\\bigcup',
0x22C4: '\\diamond',
0x22C5: '\\cdot',
0x22C6: '\\star',
0x22C7: '\\divideontimes',
0x22C8: '\\bowtie',
0x22C9: '\\ltimes',
0x22CA: '\\rtimes',
0x22CB: '\\leftthreetimes',
0x22CC: '\\rightthreetimes',
0x22CD: '\\backsimeq',
0x22CE: '\\curlyvee',
0x22CF: '\\curlywedge',
0x22D0: '\\Subset',
0x22D1: '\\Supset',
0x22D2: '\\Cap',
0x22D3: '\\Cup',
0x22D4: '\\pitchfork',
0x22D6: '\\lessdot',
0x22D7: '\\gtrdot',
0x22D8: '\\verymuchless',
0x22D9: '\\verymuchgreater',
0x22DA: '\\lesseqgtr',
0x22DB: '\\gtreqless',
0x22DE: '\\curlyeqprec',
0x22DF: '\\curlyeqsucc',
0x22E2: '\\not\\sqsubseteq',
0x22E3: '\\not\\sqsupseteq',
0x22E6: '\\lnsim',
0x22E7: '\\gnsim',
0x22E8: '\\precedesnotsimilar',
0x22E9: '\\succnsim',
0x22EA: '\\ntriangleleft',
0x22EB: '\\ntriangleright',
0x22EC: '\\ntrianglelefteq',
0x22ED: '\\ntrianglerighteq',
0x22EE: '\\vdots',
0x22EF: '\\cdots',
0x22F0: '\\upslopeellipsis',
0x22F1: '\\downslopeellipsis',
0x2305: '\\barwedge',
0x2306: '\\varperspcorrespond',
0x2308: '\\lceil',
0x2309: '\\rceil',
0x230A: '\\lfloor',
0x230B: '\\rfloor',
0x2315: '\\recorder',
0x2316: '\\mathchar"2208',
0x231C: '\\ulcorner',
0x231D: '\\urcorner',
0x231E: '\\llcorner',
0x231F: '\\lrcorner',
0x2322: '\\frown',
0x2323: '\\smile',
0x23B0: '\\lmoustache',
0x23B1: '\\rmoustache',
0x2423: '\\textvisiblespace',
0x2460: '\\ding{172}',
0x2461: '\\ding{173}',
0x2462: '\\ding{174}',
0x2463: '\\ding{175}',
0x2464: '\\ding{176}',
0x2465: '\\ding{177}',
0x2466: '\\ding{178}',
0x2467: '\\ding{179}',
0x2468: '\\ding{180}',
0x2469: '\\ding{181}',
0x24C8: '\\circledS',
0x2571: '\\diagup',
0x25A0: '\\ding{110}',
0x25A1: '\\square',
0x25AA: '\\blacksquare',
0x25AD: '\\fbox{~~}',
0x25B2: '\\ding{115}',
0x25B3: '\\bigtriangleup',
0x25B4: '\\blacktriangle',
0x25B5: '\\vartriangle',
0x25B8: '\\blacktriangleright',
0x25B9: '\\triangleright',
0x25BC: '\\ding{116}',
0x25BD: '\\bigtriangledown',
0x25BE: '\\blacktriangledown',
0x25BF: '\\triangledown',
0x25C2: '\\blacktriangleleft',
0x25C3: '\\triangleleft',
0x25C6: '\\ding{117}',
0x25CA: '\\lozenge',
0x25CB: '\\bigcirc',
0x25CF: '\\ding{108}',
0x25D7: '\\ding{119}',
0x25EF: '\\bigcirc',
0x2605: '\\ding{72}',
0x2606: '\\ding{73}',
0x260E: '\\ding{37}',
0x261B: '\\ding{42}',
0x261E: '\\ding{43}',
0x263E: '\\rightmoon',
0x263F: '\\mercury',
0x2640: '\\venus',
0x2642: '\\male',
0x2643: '\\jupiter',
0x2644: '\\saturn',
0x2645: '\\uranus',
0x2646: '\\neptune',
0x2647: '\\pluto',
0x2648: '\\aries',
0x2649: '\\taurus',
0x264A: '\\gemini',
0x264B: '\\cancer',
0x264C: '\\leo',
0x264D: '\\virgo',
0x264E: '\\libra',
0x264F: '\\scorpio',
0x2650: '\\sagittarius',
0x2651: '\\capricornus',
0x2652: '\\aquarius',
0x2653: '\\pisces',
0x2660: '\\ding{171}',
0x2662: '\\diamond',
0x2663: '\\ding{168}',
0x2665: '\\ding{170}',
0x2666: '\\ding{169}',
0x2669: '\\quarternote',
0x266A: '\\eighthnote',
0x266D: '\\flat',
0x266E: '\\natural',
0x266F: '\\sharp',
0x2701: '\\ding{33}',
0x2702: '\\ding{34}',
0x2703: '\\ding{35}',
0x2704: '\\ding{36}',
0x2706: '\\ding{38}',
0x2707: '\\ding{39}',
0x2708: '\\ding{40}',
0x2709: '\\ding{41}',
0x270C: '\\ding{44}',
0x270D: '\\ding{45}',
0x270E: '\\ding{46}',
0x270F: '\\ding{47}',
0x2710: '\\ding{48}',
0x2711: '\\ding{49}',
0x2712: '\\ding{50}',
0x2713: '\\ding{51}',
0x2714: '\\ding{52}',
0x2715: '\\ding{53}',
0x2716: '\\ding{54}',
0x2717: '\\ding{55}',
0x2718: '\\ding{56}',
0x2719: '\\ding{57}',
0x271A: '\\ding{58}',
0x271B: '\\ding{59}',
0x271C: '\\ding{60}',
0x271D: '\\ding{61}',
0x271E: '\\ding{62}',
0x271F: '\\ding{63}',
0x2720: '\\ding{64}',
0x2721: '\\ding{65}',
0x2722: '\\ding{66}',
0x2723: '\\ding{67}',
0x2724: '\\ding{68}',
0x2725: '\\ding{69}',
0x2726: '\\ding{70}',
0x2727: '\\ding{71}',
0x2729: '\\ding{73}',
0x272A: '\\ding{74}',
0x272B: '\\ding{75}',
0x272C: '\\ding{76}',
0x272D: '\\ding{77}',
0x272E: '\\ding{78}',
0x272F: '\\ding{79}',
0x2730: '\\ding{80}',
0x2731: '\\ding{81}',
0x2732: '\\ding{82}',
0x2733: '\\ding{83}',
0x2734: '\\ding{84}',
0x2735: '\\ding{85}',
0x2736: '\\ding{86}',
0x2737: '\\ding{87}',
0x2738: '\\ding{88}',
0x2739: '\\ding{89}',
0x273A: '\\ding{90}',
0x273B: '\\ding{91}',
0x273C: '\\ding{92}',
0x273D: '\\ding{93}',
0x273E: '\\ding{94}',
0x273F: '\\ding{95}',
0x2740: '\\ding{96}',
0x2741: '\\ding{97}',
0x2742: '\\ding{98}',
0x2743: '\\ding{99}',
0x2744: '\\ding{100}',
0x2745: '\\ding{101}',
0x2746: '\\ding{102}',
0x2747: '\\ding{103}',
0x2748: '\\ding{104}',
0x2749: '\\ding{105}',
0x274A: '\\ding{106}',
0x274B: '\\ding{107}',
0x274D: '\\ding{109}',
0x274F: '\\ding{111}',
0x2750: '\\ding{112}',
0x2751: '\\ding{113}',
0x2752: '\\ding{114}',
0x2756: '\\ding{118}',
0x2758: '\\ding{120}',
0x2759: '\\ding{121}',
0x275A: '\\ding{122}',
0x275B: '\\ding{123}',
0x275C: '\\ding{124}',
0x275D: '\\ding{125}',
0x275E: '\\ding{126}',
0x2761: '\\ding{161}',
0x2762: '\\ding{162}',
0x2763: '\\ding{163}',
0x2764: '\\ding{164}',
0x2765: '\\ding{165}',
0x2766: '\\ding{166}',
0x2767: '\\ding{167}',
0x2776: '\\ding{182}',
0x2777: '\\ding{183}',
0x2778: '\\ding{184}',
0x2779: '\\ding{185}',
0x277A: '\\ding{186}',
0x277B: '\\ding{187}',
0x277C: '\\ding{188}',
0x277D: '\\ding{189}',
0x277E: '\\ding{190}',
0x277F: '\\ding{191}',
0x2780: '\\ding{192}',
0x2781: '\\ding{193}',
0x2782: '\\ding{194}',
0x2783: '\\ding{195}',
0x2784: '\\ding{196}',
0x2785: '\\ding{197}',
0x2786: '\\ding{198}',
0x2787: '\\ding{199}',
0x2788: '\\ding{200}',
0x2789: '\\ding{201}',
0x278A: '\\ding{202}',
0x278B: '\\ding{203}',
0x278C: '\\ding{204}',
0x278D: '\\ding{205}',
0x278E: '\\ding{206}',
0x278F: '\\ding{207}',
0x2790: '\\ding{208}',
0x2791: '\\ding{209}',
0x2792: '\\ding{210}',
0x2793: '\\ding{211}',
0x2794: '\\ding{212}',
0x2798: '\\ding{216}',
0x2799: '\\ding{217}',
0x279A: '\\ding{218}',
0x279B: '\\ding{219}',
0x279C: '\\ding{220}',
0x279D: '\\ding{221}',
0x279E: '\\ding{222}',
0x279F: '\\ding{223}',
0x27A0: '\\ding{224}',
0x27A1: '\\ding{225}',
0x27A2: '\\ding{226}',
0x27A3: '\\ding{227}',
0x27A4: '\\ding{228}',
0x27A5: '\\ding{229}',
0x27A6: '\\ding{230}',
0x27A7: '\\ding{231}',
0x27A8: '\\ding{232}',
0x27A9: '\\ding{233}',
0x27AA: '\\ding{234}',
0x27AB: '\\ding{235}',
0x27AC: '\\ding{236}',
0x27AD: '\\ding{237}',
0x27AE: '\\ding{238}',
0x27AF: '\\ding{239}',
0x27B1: '\\ding{241}',
0x27B2: '\\ding{242}',
0x27B3: '\\ding{243}',
0x27B4: '\\ding{244}',
0x27B5: '\\ding{245}',
0x27B6: '\\ding{246}',
0x27B7: '\\ding{247}',
0x27B8: '\\ding{248}',
0x27B9: '\\ding{249}',
0x27BA: '\\ding{250}',
0x27BB: '\\ding{251}',
0x27BC: '\\ding{252}',
0x27BD: '\\ding{253}',
0x27BE: '\\ding{254}',
0x27E8: '\\langle',
0x27E9: '\\rangle',
0x27F5: '\\longleftarrow',
0x27F6: '\\longrightarrow',
0x27F7: '\\longleftrightarrow',
0x27F8: '\\Longleftarrow',
0x27F9: '\\Longrightarrow',
0x27FA: '\\Longleftrightarrow',
0x27FC: '\\longmapsto',
0x27FF: '\\sim\\joinrel\\leadsto',
0x2912: '\\UpArrowBar',
0x2913: '\\DownArrowBar',
0x294E: '\\LeftRightVector',
0x294F: '\\RightUpDownVector',
0x2950: '\\DownLeftRightVector',
0x2951: '\\LeftUpDownVector',
0x2952: '\\LeftVectorBar',
0x2953: '\\RightVectorBar',
0x2954: '\\RightUpVectorBar',
0x2955: '\\RightDownVectorBar',
0x2956: '\\DownLeftVectorBar',
0x2957: '\\DownRightVectorBar',
0x2958: '\\LeftUpVectorBar',
0x2959: '\\LeftDownVectorBar',
0x295A: '\\LeftTeeVector',
0x295B: '\\RightTeeVector',
0x295C: '\\RightUpTeeVector',
0x295D: '\\RightDownTeeVector',
0x295E: '\\DownLeftTeeVector',
0x295F: '\\DownRightTeeVector',
0x2960: '\\LeftUpTeeVector',
0x2961: '\\LeftDownTeeVector',
0x296E: '\\UpEquilibrium',
0x296F: '\\ReverseUpEquilibrium',
0x2970: '\\RoundImplies',
0x2993: '<\\kern-0.58em(',
0x299C: '\\Angle',
0x29CF: '\\LeftTriangleBar',
0x29D0: '\\RightTriangleBar',
0x29EB: '\\blacklozenge',
0x29F4: '\\RuleDelayed',
0x2A0F: '\\clockoint',
0x2A16: '\\sqrint',
0x2A3F: '\\amalg',
0x2A5E: '\\perspcorrespond',
0x2A6E: '\\stackrel{*}{=}',
0x2A75: '\\Equal',
0x2A7D: '\\leqslant',
0x2A7E: '\\geqslant',
0x2A85: '\\lessapprox',
0x2A86: '\\gtrapprox',
0x2A87: '\\lneq',
0x2A88: '\\gneq',
0x2A89: '\\lnapprox',
0x2A8A: '\\gnapprox',
0x2A8B: '\\lesseqqgtr',
0x2A8C: '\\gtreqqless',
0x2A95: '\\eqslantless',
0x2A96: '\\eqslantgtr',
0x2A9D: '\\Pisymbol{ppi020}{117}',
0x2A9E: '\\Pisymbol{ppi020}{105}',
0x2AA1: '\\NestedLessLess',
0x2AA2: '\\NestedGreaterGreater',
0x2AAF: '\\preceq',
0x2AB0: '\\succeq',
0x2AB5: '\\precneqq',
0x2AB6: '\\succneqq',
0x2AB7: '\\precapprox',
0x2AB8: '\\succapprox',
0x2AB9: '\\precnapprox',
0x2ABA: '\\succnapprox',
0x2AC5: '\\subseteqq',
0x2AC6: '\\supseteqq',
0x2ACB: '\\subsetneqq',
0x2ACC: '\\supsetneqq',
0x2AFD: '{{/}\\!\\!{/}}',
0x301A: '\\openbracketleft',
0x301B: '\\openbracketright',
0xFB00: 'ff',
0xFB01: 'fi',
0xFB02: 'fl',
0xFB03: 'ffi',
0xFB04: 'ffl',
0x1D400: '\\mathbf{A}',
0x1D401: '\\mathbf{B}',
0x1D402: '\\mathbf{C}',
0x1D403: '\\mathbf{D}',
0x1D404: '\\mathbf{E}',
0x1D405: '\\mathbf{F}',
0x1D406: '\\mathbf{G}',
0x1D407: '\\mathbf{H}',
0x1D408: '\\mathbf{I}',
0x1D409: '\\mathbf{J}',
0x1D40A: '\\mathbf{K}',
0x1D40B: '\\mathbf{L}',
0x1D40C: '\\mathbf{M}',
0x1D40D: '\\mathbf{N}',
0x1D40E: '\\mathbf{O}',
0x1D40F: '\\mathbf{P}',
0x1D410: '\\mathbf{Q}',
0x1D411: '\\mathbf{R}',
0x1D412: '\\mathbf{S}',
0x1D413: '\\mathbf{T}',
0x1D414: '\\mathbf{U}',
0x1D415: '\\mathbf{V}',
0x1D416: '\\mathbf{W}',
0x1D417: '\\mathbf{X}',
0x1D418: '\\mathbf{Y}',
0x1D419: '\\mathbf{Z}',
0x1D41A: '\\mathbf{a}',
0x1D41B: '\\mathbf{b}',
0x1D41C: '\\mathbf{c}',
0x1D41D: '\\mathbf{d}',
0x1D41E: '\\mathbf{e}',
0x1D41F: '\\mathbf{f}',
0x1D420: '\\mathbf{g}',
0x1D421: '\\mathbf{h}',
0x1D422: '\\mathbf{i}',
0x1D423: '\\mathbf{j}',
0x1D424: '\\mathbf{k}',
0x1D425: '\\mathbf{l}',
0x1D426: '\\mathbf{m}',
0x1D427: '\\mathbf{n}',
0x1D428: '\\mathbf{o}',
0x1D429: '\\mathbf{p}',
0x1D42A: '\\mathbf{q}',
0x1D42B: '\\mathbf{r}',
0x1D42C: '\\mathbf{s}',
0x1D42D: '\\mathbf{t}',
0x1D42E: '\\mathbf{u}',
0x1D42F: '\\mathbf{v}',
0x1D430: '\\mathbf{w}',
0x1D431: '\\mathbf{x}',
0x1D432: '\\mathbf{y}',
0x1D433: '\\mathbf{z}',
0x1D434: '\\mathmit{A}',
0x1D435: '\\mathmit{B}',
0x1D436: '\\mathmit{C}',
0x1D437: '\\mathmit{D}',
0x1D438: '\\mathmit{E}',
0x1D439: '\\mathmit{F}',
0x1D43A: '\\mathmit{G}',
0x1D43B: '\\mathmit{H}',
0x1D43C: '\\mathmit{I}',
0x1D43D: '\\mathmit{J}',
0x1D43E: '\\mathmit{K}',
0x1D43F: '\\mathmit{L}',
0x1D440: '\\mathmit{M}',
0x1D441: '\\mathmit{N}',
0x1D442: '\\mathmit{O}',
0x1D443: '\\mathmit{P}',
0x1D444: '\\mathmit{Q}',
0x1D445: '\\mathmit{R}',
0x1D446: '\\mathmit{S}',
0x1D447: '\\mathmit{T}',
0x1D448: '\\mathmit{U}',
0x1D449: '\\mathmit{V}',
0x1D44A: '\\mathmit{W}',
0x1D44B: '\\mathmit{X}',
0x1D44C: '\\mathmit{Y}',
0x1D44D: '\\mathmit{Z}',
0x1D44E: '\\mathmit{a}',
0x1D44F: '\\mathmit{b}',
0x1D450: '\\mathmit{c}',
0x1D451: '\\mathmit{d}',
0x1D452: '\\mathmit{e}',
0x1D453: '\\mathmit{f}',
0x1D454: '\\mathmit{g}',
0x1D456: '\\mathmit{i}',
0x1D457: '\\mathmit{j}',
0x1D458: '\\mathmit{k}',
0x1D459: '\\mathmit{l}',
0x1D45A: '\\mathmit{m}',
0x1D45B: '\\mathmit{n}',
0x1D45C: '\\mathmit{o}',
0x1D45D: '\\mathmit{p}',
0x1D45E: '\\mathmit{q}',
0x1D45F: '\\mathmit{r}',
0x1D460: '\\mathmit{s}',
0x1D461: '\\mathmit{t}',
0x1D462: '\\mathmit{u}',
0x1D463: '\\mathmit{v}',
0x1D464: '\\mathmit{w}',
0x1D465: '\\mathmit{x}',
0x1D466: '\\mathmit{y}',
0x1D467: '\\mathmit{z}',
0x1D468: '\\mathbit{A}',
0x1D469: '\\mathbit{B}',
0x1D46A: '\\mathbit{C}',
0x1D46B: '\\mathbit{D}',
0x1D46C: '\\mathbit{E}',
0x1D46D: '\\mathbit{F}',
0x1D46E: '\\mathbit{G}',
0x1D46F: '\\mathbit{H}',
0x1D470: '\\mathbit{I}',
0x1D471: '\\mathbit{J}',
0x1D472: '\\mathbit{K}',
0x1D473: '\\mathbit{L}',
0x1D474: '\\mathbit{M}',
0x1D475: '\\mathbit{N}',
0x1D476: '\\mathbit{O}',
0x1D477: '\\mathbit{P}',
0x1D478: '\\mathbit{Q}',
0x1D479: '\\mathbit{R}',
0x1D47A: '\\mathbit{S}',
0x1D47B: '\\mathbit{T}',
0x1D47C: '\\mathbit{U}',
0x1D47D: '\\mathbit{V}',
0x1D47E: '\\mathbit{W}',
0x1D47F: '\\mathbit{X}',
0x1D480: '\\mathbit{Y}',
0x1D481: '\\mathbit{Z}',
0x1D482: '\\mathbit{a}',
0x1D483: '\\mathbit{b}',
0x1D484: '\\mathbit{c}',
0x1D485: '\\mathbit{d}',
0x1D486: '\\mathbit{e}',
0x1D487: '\\mathbit{f}',
0x1D488: '\\mathbit{g}',
0x1D489: '\\mathbit{h}',
0x1D48A: '\\mathbit{i}',
0x1D48B: '\\mathbit{j}',
0x1D48C: '\\mathbit{k}',
0x1D48D: '\\mathbit{l}',
0x1D48E: '\\mathbit{m}',
0x1D48F: '\\mathbit{n}',
0x1D490: '\\mathbit{o}',
0x1D491: '\\mathbit{p}',
0x1D492: '\\mathbit{q}',
0x1D493: '\\mathbit{r}',
0x1D494: '\\mathbit{s}',
0x1D495: '\\mathbit{t}',
0x1D496: '\\mathbit{u}',
0x1D497: '\\mathbit{v}',
0x1D498: '\\mathbit{w}',
0x1D499: '\\mathbit{x}',
0x1D49A: '\\mathbit{y}',
0x1D49B: '\\mathbit{z}',
0x1D49C: '\\mathscr{A}',
0x1D49E: '\\mathscr{C}',
0x1D49F: '\\mathscr{D}',
0x1D4A2: '\\mathscr{G}',
0x1D4A5: '\\mathscr{J}',
0x1D4A6: '\\mathscr{K}',
0x1D4A9: '\\mathscr{N}',
0x1D4AA: '\\mathscr{O}',
0x1D4AB: '\\mathscr{P}',
0x1D4AC: '\\mathscr{Q}',
0x1D4AE: '\\mathscr{S}',
0x1D4AF: '\\mathscr{T}',
0x1D4B0: '\\mathscr{U}',
0x1D4B1: '\\mathscr{V}',
0x1D4B2: '\\mathscr{W}',
0x1D4B3: '\\mathscr{X}',
0x1D4B4: '\\mathscr{Y}',
0x1D4B5: '\\mathscr{Z}',
0x1D4B6: '\\mathscr{a}',
0x1D4B7: '\\mathscr{b}',
0x1D4B8: '\\mathscr{c}',
0x1D4B9: '\\mathscr{d}',
0x1D4BB: '\\mathscr{f}',
0x1D4BD: '\\mathscr{h}',
0x1D4BE: '\\mathscr{i}',
0x1D4BF: '\\mathscr{j}',
0x1D4C0: '\\mathscr{k}',
0x1D4C1: '\\mathscr{l}',
0x1D4C2: '\\mathscr{m}',
0x1D4C3: '\\mathscr{n}',
0x1D4C5: '\\mathscr{p}',
0x1D4C6: '\\mathscr{q}',
0x1D4C7: '\\mathscr{r}',
0x1D4C8: '\\mathscr{s}',
0x1D4C9: '\\mathscr{t}',
0x1D4CA: '\\mathscr{u}',
0x1D4CB: '\\mathscr{v}',
0x1D4CC: '\\mathscr{w}',
0x1D4CD: '\\mathscr{x}',
0x1D4CE: '\\mathscr{y}',
0x1D4CF: '\\mathscr{z}',
0x1D4D0: '\\mathbcal{A}',
0x1D4D1: '\\mathbcal{B}',
0x1D4D2: '\\mathbcal{C}',
0x1D4D3: '\\mathbcal{D}',
0x1D4D4: '\\mathbcal{E}',
0x1D4D5: '\\mathbcal{F}',
0x1D4D6: '\\mathbcal{G}',
0x1D4D7: '\\mathbcal{H}',
0x1D4D8: '\\mathbcal{I}',
0x1D4D9: '\\mathbcal{J}',
0x1D4DA: '\\mathbcal{K}',
0x1D4DB: '\\mathbcal{L}',
0x1D4DC: '\\mathbcal{M}',
0x1D4DD: '\\mathbcal{N}',
0x1D4DE: '\\mathbcal{O}',
0x1D4DF: '\\mathbcal{P}',
0x1D4E0: '\\mathbcal{Q}',
0x1D4E1: '\\mathbcal{R}',
0x1D4E2: '\\mathbcal{S}',
0x1D4E3: '\\mathbcal{T}',
0x1D4E4: '\\mathbcal{U}',
0x1D4E5: '\\mathbcal{V}',
0x1D4E6: '\\mathbcal{W}',
0x1D4E7: '\\mathbcal{X}',
0x1D4E8: '\\mathbcal{Y}',
0x1D4E9: '\\mathbcal{Z}',
0x1D4EA: '\\mathbcal{a}',
0x1D4EB: '\\mathbcal{b}',
0x1D4EC: '\\mathbcal{c}',
0x1D4ED: '\\mathbcal{d}',
0x1D4EE: '\\mathbcal{e}',
0x1D4EF: '\\mathbcal{f}',
0x1D4F0: '\\mathbcal{g}',
0x1D4F1: '\\mathbcal{h}',
0x1D4F2: '\\mathbcal{i}',
0x1D4F3: '\\mathbcal{j}',
0x1D4F4: '\\mathbcal{k}',
0x1D4F5: '\\mathbcal{l}',
0x1D4F6: '\\mathbcal{m}',
0x1D4F7: '\\mathbcal{n}',
0x1D4F8: '\\mathbcal{o}',
0x1D4F9: '\\mathbcal{p}',
0x1D4FA: '\\mathbcal{q}',
0x1D4FB: '\\mathbcal{r}',
0x1D4FC: '\\mathbcal{s}',
0x1D4FD: '\\mathbcal{t}',
0x1D4FE: '\\mathbcal{u}',
0x1D4FF: '\\mathbcal{v}',
0x1D500: '\\mathbcal{w}',
0x1D501: '\\mathbcal{x}',
0x1D502: '\\mathbcal{y}',
0x1D503: '\\mathbcal{z}',
0x1D504: '\\mathfrak{A}',
0x1D505: '\\mathfrak{B}',
0x1D507: '\\mathfrak{D}',
0x1D508: '\\mathfrak{E}',
0x1D509: '\\mathfrak{F}',
0x1D50A: '\\mathfrak{G}',
0x1D50D: '\\mathfrak{J}',
0x1D50E: '\\mathfrak{K}',
0x1D50F: '\\mathfrak{L}',
0x1D510: '\\mathfrak{M}',
0x1D511: '\\mathfrak{N}',
0x1D512: '\\mathfrak{O}',
0x1D513: '\\mathfrak{P}',
0x1D514: '\\mathfrak{Q}',
0x1D516: '\\mathfrak{S}',
0x1D517: '\\mathfrak{T}',
0x1D518: '\\mathfrak{U}',
0x1D519: '\\mathfrak{V}',
0x1D51A: '\\mathfrak{W}',
0x1D51B: '\\mathfrak{X}',
0x1D51C: '\\mathfrak{Y}',
0x1D51E: '\\mathfrak{a}',
0x1D51F: '\\mathfrak{b}',
0x1D520: '\\mathfrak{c}',
0x1D521: '\\mathfrak{d}',
0x1D522: '\\mathfrak{e}',
0x1D523: '\\mathfrak{f}',
0x1D524: '\\mathfrak{g}',
0x1D525: '\\mathfrak{h}',
0x1D526: '\\mathfrak{i}',
0x1D527: '\\mathfrak{j}',
0x1D528: '\\mathfrak{k}',
0x1D529: '\\mathfrak{l}',
0x1D52A: '\\mathfrak{m}',
0x1D52B: '\\mathfrak{n}',
0x1D52C: '\\mathfrak{o}',
0x1D52D: '\\mathfrak{p}',
0x1D52E: '\\mathfrak{q}',
0x1D52F: '\\mathfrak{r}',
0x1D530: '\\mathfrak{s}',
0x1D531: '\\mathfrak{t}',
0x1D532: '\\mathfrak{u}',
0x1D533: '\\mathfrak{v}',
0x1D534: '\\mathfrak{w}',
0x1D535: '\\mathfrak{x}',
0x1D536: '\\mathfrak{y}',
0x1D537: '\\mathfrak{z}',
0x1D538: '\\mathbb{A}',
0x1D539: '\\mathbb{B}',
0x1D53B: '\\mathbb{D}',
0x1D53C: '\\mathbb{E}',
0x1D53D: '\\mathbb{F}',
0x1D53E: '\\mathbb{G}',
0x1D540: '\\mathbb{I}',
0x1D541: '\\mathbb{J}',
0x1D542: '\\mathbb{K}',
0x1D543: '\\mathbb{L}',
0x1D544: '\\mathbb{M}',
0x1D546: '\\mathbb{O}',
0x1D54A: '\\mathbb{S}',
0x1D54B: '\\mathbb{T}',
0x1D54C: '\\mathbb{U}',
0x1D54D: '\\mathbb{V}',
0x1D54E: '\\mathbb{W}',
0x1D54F: '\\mathbb{X}',
0x1D550: '\\mathbb{Y}',
0x1D552: '\\mathbb{a}',
0x1D553: '\\mathbb{b}',
0x1D554: '\\mathbb{c}',
0x1D555: '\\mathbb{d}',
0x1D556: '\\mathbb{e}',
0x1D557: '\\mathbb{f}',
0x1D558: '\\mathbb{g}',
0x1D559: '\\mathbb{h}',
0x1D55A: '\\mathbb{i}',
0x1D55B: '\\mathbb{j}',
0x1D55C: '\\mathbb{k}',
0x1D55D: '\\mathbb{l}',
0x1D55E: '\\mathbb{m}',
0x1D55F: '\\mathbb{n}',
0x1D560: '\\mathbb{o}',
0x1D561: '\\mathbb{p}',
0x1D562: '\\mathbb{q}',
0x1D563: '\\mathbb{r}',
0x1D564: '\\mathbb{s}',
0x1D565: '\\mathbb{t}',
0x1D566: '\\mathbb{u}',
0x1D567: '\\mathbb{v}',
0x1D568: '\\mathbb{w}',
0x1D569: '\\mathbb{x}',
0x1D56A: '\\mathbb{y}',
0x1D56B: '\\mathbb{z}',
0x1D56C: '\\mathbfrak{A}',
0x1D56D: '\\mathbfrak{B}',
0x1D56E: '\\mathbfrak{C}',
0x1D56F: '\\mathbfrak{D}',
0x1D570: '\\mathbfrak{E}',
0x1D571: '\\mathbfrak{F}',
0x1D572: '\\mathbfrak{G}',
0x1D573: '\\mathbfrak{H}',
0x1D574: '\\mathbfrak{I}',
0x1D575: '\\mathbfrak{J}',
0x1D576: '\\mathbfrak{K}',
0x1D577: '\\mathbfrak{L}',
0x1D578: '\\mathbfrak{M}',
0x1D579: '\\mathbfrak{N}',
0x1D57A: '\\mathbfrak{O}',
0x1D57B: '\\mathbfrak{P}',
0x1D57C: '\\mathbfrak{Q}',
0x1D57D: '\\mathbfrak{R}',
0x1D57E: '\\mathbfrak{S}',
0x1D57F: '\\mathbfrak{T}',
0x1D580: '\\mathbfrak{U}',
0x1D581: '\\mathbfrak{V}',
0x1D582: '\\mathbfrak{W}',
0x1D583: '\\mathbfrak{X}',
0x1D584: '\\mathbfrak{Y}',
0x1D585: '\\mathbfrak{Z}',
0x1D586: '\\mathbfrak{a}',
0x1D587: '\\mathbfrak{b}',
0x1D588: '\\mathbfrak{c}',
0x1D589: '\\mathbfrak{d}',
0x1D58A: '\\mathbfrak{e}',
0x1D58B: '\\mathbfrak{f}',
0x1D58C: '\\mathbfrak{g}',
0x1D58D: '\\mathbfrak{h}',
0x1D58E: '\\mathbfrak{i}',
0x1D58F: '\\mathbfrak{j}',
0x1D590: '\\mathbfrak{k}',
0x1D591: '\\mathbfrak{l}',
0x1D592: '\\mathbfrak{m}',
0x1D593: '\\mathbfrak{n}',
0x1D594: '\\mathbfrak{o}',
0x1D595: '\\mathbfrak{p}',
0x1D596: '\\mathbfrak{q}',
0x1D597: '\\mathbfrak{r}',
0x1D598: '\\mathbfrak{s}',
0x1D599: '\\mathbfrak{t}',
0x1D59A: '\\mathbfrak{u}',
0x1D59B: '\\mathbfrak{v}',
0x1D59C: '\\mathbfrak{w}',
0x1D59D: '\\mathbfrak{x}',
0x1D59E: '\\mathbfrak{y}',
0x1D59F: '\\mathbfrak{z}',
0x1D5A0: '\\mathsf{A}',
0x1D5A1: '\\mathsf{B}',
0x1D5A2: '\\mathsf{C}',
0x1D5A3: '\\mathsf{D}',
0x1D5A4: '\\mathsf{E}',
0x1D5A5: '\\mathsf{F}',
0x1D5A6: '\\mathsf{G}',
0x1D5A7: '\\mathsf{H}',
0x1D5A8: '\\mathsf{I}',
0x1D5A9: '\\mathsf{J}',
0x1D5AA: '\\mathsf{K}',
0x1D5AB: '\\mathsf{L}',
0x1D5AC: '\\mathsf{M}',
0x1D5AD: '\\mathsf{N}',
0x1D5AE: '\\mathsf{O}',
0x1D5AF: '\\mathsf{P}',
0x1D5B0: '\\mathsf{Q}',
0x1D5B1: '\\mathsf{R}',
0x1D5B2: '\\mathsf{S}',
0x1D5B3: '\\mathsf{T}',
0x1D5B4: '\\mathsf{U}',
0x1D5B5: '\\mathsf{V}',
0x1D5B6: '\\mathsf{W}',
0x1D5B7: '\\mathsf{X}',
0x1D5B8: '\\mathsf{Y}',
0x1D5B9: '\\mathsf{Z}',
0x1D5BA: '\\mathsf{a}',
0x1D5BB: '\\mathsf{b}',
0x1D5BC: '\\mathsf{c}',
0x1D5BD: '\\mathsf{d}',
0x1D5BE: '\\mathsf{e}',
0x1D5BF: '\\mathsf{f}',
0x1D5C0: '\\mathsf{g}',
0x1D5C1: '\\mathsf{h}',
0x1D5C2: '\\mathsf{i}',
0x1D5C3: '\\mathsf{j}',
0x1D5C4: '\\mathsf{k}',
0x1D5C5: '\\mathsf{l}',
0x1D5C6: '\\mathsf{m}',
0x1D5C7: '\\mathsf{n}',
0x1D5C8: '\\mathsf{o}',
0x1D5C9: '\\mathsf{p}',
0x1D5CA: '\\mathsf{q}',
0x1D5CB: '\\mathsf{r}',
0x1D5CC: '\\mathsf{s}',
0x1D5CD: '\\mathsf{t}',
0x1D5CE: '\\mathsf{u}',
0x1D5CF: '\\mathsf{v}',
0x1D5D0: '\\mathsf{w}',
0x1D5D1: '\\mathsf{x}',
0x1D5D2: '\\mathsf{y}',
0x1D5D3: '\\mathsf{z}',
0x1D5D4: '\\mathsfbf{A}',
0x1D5D5: '\\mathsfbf{B}',
0x1D5D6: '\\mathsfbf{C}',
0x1D5D7: '\\mathsfbf{D}',
0x1D5D8: '\\mathsfbf{E}',
0x1D5D9: '\\mathsfbf{F}',
0x1D5DA: '\\mathsfbf{G}',
0x1D5DB: '\\mathsfbf{H}',
0x1D5DC: '\\mathsfbf{I}',
0x1D5DD: '\\mathsfbf{J}',
0x1D5DE: '\\mathsfbf{K}',
0x1D5DF: '\\mathsfbf{L}',
0x1D5E0: '\\mathsfbf{M}',
0x1D5E1: '\\mathsfbf{N}',
0x1D5E2: '\\mathsfbf{O}',
0x1D5E3: '\\mathsfbf{P}',
0x1D5E4: '\\mathsfbf{Q}',
0x1D5E5: '\\mathsfbf{R}',
0x1D5E6: '\\mathsfbf{S}',
0x1D5E7: '\\mathsfbf{T}',
0x1D5E8: '\\mathsfbf{U}',
0x1D5E9: '\\mathsfbf{V}',
0x1D5EA: '\\mathsfbf{W}',
0x1D5EB: '\\mathsfbf{X}',
0x1D5EC: '\\mathsfbf{Y}',
0x1D5ED: '\\mathsfbf{Z}',
0x1D5EE: '\\mathsfbf{a}',
0x1D5EF: '\\mathsfbf{b}',
0x1D5F0: '\\mathsfbf{c}',
0x1D5F1: '\\mathsfbf{d}',
0x1D5F2: '\\mathsfbf{e}',
0x1D5F3: '\\mathsfbf{f}',
0x1D5F4: '\\mathsfbf{g}',
0x1D5F5: '\\mathsfbf{h}',
0x1D5F6: '\\mathsfbf{i}',
0x1D5F7: '\\mathsfbf{j}',
0x1D5F8: '\\mathsfbf{k}',
0x1D5F9: '\\mathsfbf{l}',
0x1D5FA: '\\mathsfbf{m}',
0x1D5FB: '\\mathsfbf{n}',
0x1D5FC: '\\mathsfbf{o}',
0x1D5FD: '\\mathsfbf{p}',
0x1D5FE: '\\mathsfbf{q}',
0x1D5FF: '\\mathsfbf{r}',
0x1D600: '\\mathsfbf{s}',
0x1D601: '\\mathsfbf{t}',
0x1D602: '\\mathsfbf{u}',
0x1D603: '\\mathsfbf{v}',
0x1D604: '\\mathsfbf{w}',
0x1D605: '\\mathsfbf{x}',
0x1D606: '\\mathsfbf{y}',
0x1D607: '\\mathsfbf{z}',
0x1D608: '\\mathsfsl{A}',
0x1D609: '\\mathsfsl{B}',
0x1D60A: '\\mathsfsl{C}',
0x1D60B: '\\mathsfsl{D}',
0x1D60C: '\\mathsfsl{E}',
0x1D60D: '\\mathsfsl{F}',
0x1D60E: '\\mathsfsl{G}',
0x1D60F: '\\mathsfsl{H}',
0x1D610: '\\mathsfsl{I}',
0x1D611: '\\mathsfsl{J}',
0x1D612: '\\mathsfsl{K}',
0x1D613: '\\mathsfsl{L}',
0x1D614: '\\mathsfsl{M}',
0x1D615: '\\mathsfsl{N}',
0x1D616: '\\mathsfsl{O}',
0x1D617: '\\mathsfsl{P}',
0x1D618: '\\mathsfsl{Q}',
0x1D619: '\\mathsfsl{R}',
0x1D61A: '\\mathsfsl{S}',
0x1D61B: '\\mathsfsl{T}',
0x1D61C: '\\mathsfsl{U}',
0x1D61D: '\\mathsfsl{V}',
0x1D61E: '\\mathsfsl{W}',
0x1D61F: '\\mathsfsl{X}',
0x1D620: '\\mathsfsl{Y}',
0x1D621: '\\mathsfsl{Z}',
0x1D622: '\\mathsfsl{a}',
0x1D623: '\\mathsfsl{b}',
0x1D624: '\\mathsfsl{c}',
0x1D625: '\\mathsfsl{d}',
0x1D626: '\\mathsfsl{e}',
0x1D627: '\\mathsfsl{f}',
0x1D628: '\\mathsfsl{g}',
0x1D629: '\\mathsfsl{h}',
0x1D62A: '\\mathsfsl{i}',
0x1D62B: '\\mathsfsl{j}',
0x1D62C: '\\mathsfsl{k}',
0x1D62D: '\\mathsfsl{l}',
0x1D62E: '\\mathsfsl{m}',
0x1D62F: '\\mathsfsl{n}',
0x1D630: '\\mathsfsl{o}',
0x1D631: '\\mathsfsl{p}',
0x1D632: '\\mathsfsl{q}',
0x1D633: '\\mathsfsl{r}',
0x1D634: '\\mathsfsl{s}',
0x1D635: '\\mathsfsl{t}',
0x1D636: '\\mathsfsl{u}',
0x1D637: '\\mathsfsl{v}',
0x1D638: '\\mathsfsl{w}',
0x1D639: '\\mathsfsl{x}',
0x1D63A: '\\mathsfsl{y}',
0x1D63B: '\\mathsfsl{z}',
0x1D63C: '\\mathsfbfsl{A}',
0x1D63D: '\\mathsfbfsl{B}',
0x1D63E: '\\mathsfbfsl{C}',
0x1D63F: '\\mathsfbfsl{D}',
0x1D640: '\\mathsfbfsl{E}',
0x1D641: '\\mathsfbfsl{F}',
0x1D642: '\\mathsfbfsl{G}',
0x1D643: '\\mathsfbfsl{H}',
0x1D644: '\\mathsfbfsl{I}',
0x1D645: '\\mathsfbfsl{J}',
0x1D646: '\\mathsfbfsl{K}',
0x1D647: '\\mathsfbfsl{L}',
0x1D648: '\\mathsfbfsl{M}',
0x1D649: '\\mathsfbfsl{N}',
0x1D64A: '\\mathsfbfsl{O}',
0x1D64B: '\\mathsfbfsl{P}',
0x1D64C: '\\mathsfbfsl{Q}',
0x1D64D: '\\mathsfbfsl{R}',
0x1D64E: '\\mathsfbfsl{S}',
0x1D64F: '\\mathsfbfsl{T}',
0x1D650: '\\mathsfbfsl{U}',
0x1D651: '\\mathsfbfsl{V}',
0x1D652: '\\mathsfbfsl{W}',
0x1D653: '\\mathsfbfsl{X}',
0x1D654: '\\mathsfbfsl{Y}',
0x1D655: '\\mathsfbfsl{Z}',
0x1D656: '\\mathsfbfsl{a}',
0x1D657: '\\mathsfbfsl{b}',
0x1D658: '\\mathsfbfsl{c}',
0x1D659: '\\mathsfbfsl{d}',
0x1D65A: '\\mathsfbfsl{e}',
0x1D65B: '\\mathsfbfsl{f}',
0x1D65C: '\\mathsfbfsl{g}',
0x1D65D: '\\mathsfbfsl{h}',
0x1D65E: '\\mathsfbfsl{i}',
0x1D65F: '\\mathsfbfsl{j}',
0x1D660: '\\mathsfbfsl{k}',
0x1D661: '\\mathsfbfsl{l}',
0x1D662: '\\mathsfbfsl{m}',
0x1D663: '\\mathsfbfsl{n}',
0x1D664: '\\mathsfbfsl{o}',
0x1D665: '\\mathsfbfsl{p}',
0x1D666: '\\mathsfbfsl{q}',
0x1D667: '\\mathsfbfsl{r}',
0x1D668: '\\mathsfbfsl{s}',
0x1D669: '\\mathsfbfsl{t}',
0x1D66A: '\\mathsfbfsl{u}',
0x1D66B: '\\mathsfbfsl{v}',
0x1D66C: '\\mathsfbfsl{w}',
0x1D66D: '\\mathsfbfsl{x}',
0x1D66E: '\\mathsfbfsl{y}',
0x1D66F: '\\mathsfbfsl{z}',
0x1D670: '\\mathtt{A}',
0x1D671: '\\mathtt{B}',
0x1D672: '\\mathtt{C}',
0x1D673: '\\mathtt{D}',
0x1D674: '\\mathtt{E}',
0x1D675: '\\mathtt{F}',
0x1D676: '\\mathtt{G}',
0x1D677: '\\mathtt{H}',
0x1D678: '\\mathtt{I}',
0x1D679: '\\mathtt{J}',
0x1D67A: '\\mathtt{K}',
0x1D67B: '\\mathtt{L}',
0x1D67C: '\\mathtt{M}',
0x1D67D: '\\mathtt{N}',
0x1D67E: '\\mathtt{O}',
0x1D67F: '\\mathtt{P}',
0x1D680: '\\mathtt{Q}',
0x1D681: '\\mathtt{R}',
0x1D682: '\\mathtt{S}',
0x1D683: '\\mathtt{T}',
0x1D684: '\\mathtt{U}',
0x1D685: '\\mathtt{V}',
0x1D686: '\\mathtt{W}',
0x1D687: '\\mathtt{X}',
0x1D688: '\\mathtt{Y}',
0x1D689: '\\mathtt{Z}',
0x1D68A: '\\mathtt{a}',
0x1D68B: '\\mathtt{b}',
0x1D68C: '\\mathtt{c}',
0x1D68D: '\\mathtt{d}',
0x1D68E: '\\mathtt{e}',
0x1D68F: '\\mathtt{f}',
0x1D690: '\\mathtt{g}',
0x1D691: '\\mathtt{h}',
0x1D692: '\\mathtt{i}',
0x1D693: '\\mathtt{j}',
0x1D694: '\\mathtt{k}',
0x1D695: '\\mathtt{l}',
0x1D696: '\\mathtt{m}',
0x1D697: '\\mathtt{n}',
0x1D698: '\\mathtt{o}',
0x1D699: '\\mathtt{p}',
0x1D69A: '\\mathtt{q}',
0x1D69B: '\\mathtt{r}',
0x1D69C: '\\mathtt{s}',
0x1D69D: '\\mathtt{t}',
0x1D69E: '\\mathtt{u}',
0x1D69F: '\\mathtt{v}',
0x1D6A0: '\\mathtt{w}',
0x1D6A1: '\\mathtt{x}',
0x1D6A2: '\\mathtt{y}',
0x1D6A3: '\\mathtt{z}',
0x1D6A8: '\\mathbf{\\Alpha}',
0x1D6A9: '\\mathbf{\\Beta}',
0x1D6AA: '\\mathbf{\\Gamma}',
0x1D6AB: '\\mathbf{\\Delta}',
0x1D6AC: '\\mathbf{\\Epsilon}',
0x1D6AD: '\\mathbf{\\Zeta}',
0x1D6AE: '\\mathbf{\\Eta}',
0x1D6AF: '\\mathbf{\\Theta}',
0x1D6B0: '\\mathbf{\\Iota}',
0x1D6B1: '\\mathbf{\\Kappa}',
0x1D6B2: '\\mathbf{\\Lambda}',
0x1D6B3: '\\mathbf{M}',
0x1D6B4: 'N',
0x1D6B5: '\\mathbf{\\Xi}',
0x1D6B6: 'O',
0x1D6B7: '\\mathbf{\\Pi}',
0x1D6B8: '\\mathbf{\\Rho}',
0x1D6B9: '\\mathbf{\\vartheta}',
0x1D6BA: '\\mathbf{\\Sigma}',
0x1D6BB: '\\mathbf{\\Tau}',
0x1D6BC: '\\mathbf{\\Upsilon}',
0x1D6BD: '\\mathbf{\\Phi}',
0x1D6BE: '\\mathbf{\\Chi}',
0x1D6BF: '\\mathbf{\\Psi}',
0x1D6C0: '\\mathbf{\\Omega}',
0x1D6C1: '\\mathbf{\\nabla}',
0x1D6C2: '\\mathbf{\\alpha}',
0x1D6C3: '\\mathbf{\\beta}',
0x1D6C4: '\\mathbf{\\gamma}',
0x1D6C5: '\\mathbf{\\delta}',
0x1D6C6: '\\mathbf{\\epsilon}',
0x1D6C7: '\\mathbf{\\zeta}',
0x1D6C8: '\\mathbf{\\eta}',
0x1D6C9: '\\mathbf{\\theta}',
0x1D6CA: '\\mathbf{\\iota}',
0x1D6CB: '\\mathbf{\\kappa}',
0x1D6CC: '\\mathbf{\\lambda}',
0x1D6CD: '\\mathbf{\\mu}',
0x1D6CE: '\\mathbf{\\nu}',
0x1D6CF: '\\mathbf{\\xi}',
0x1D6D0: '\\mathbf{o}',
0x1D6D1: '\\mathbf{\\pi}',
0x1D6D2: '\\mathbf{\\rho}',
0x1D6D3: '\\mathbf{\\varsigma}',
0x1D6D4: '\\mathbf{\\sigma}',
0x1D6D5: '\\mathbf{\\tau}',
0x1D6D6: '\\mathbf{\\upsilon}',
0x1D6D7: '\\mathbf{\\phi}',
0x1D6D8: '\\mathbf{\\chi}',
0x1D6D9: '\\mathbf{\\psi}',
0x1D6DA: '\\mathbf{\\omega}',
0x1D6DB: '\\partial',
0x1D6DC: '\\mathbf{\\varepsilon}',
0x1D6DD: '\\mathbf{\\vartheta}',
0x1D6DE: '\\mathbf{\\varkappa}',
0x1D6DF: '\\mathbf{\\phi}',
0x1D6E0: '\\mathbf{\\varrho}',
0x1D6E1: '\\mathbf{\\varpi}',
0x1D6E2: '\\mathmit{\\Alpha}',
0x1D6E3: '\\mathmit{\\Beta}',
0x1D6E4: '\\mathmit{\\Gamma}',
0x1D6E5: '\\mathmit{\\Delta}',
0x1D6E6: '\\mathmit{\\Epsilon}',
0x1D6E7: '\\mathmit{\\Zeta}',
0x1D6E8: '\\mathmit{\\Eta}',
0x1D6E9: '\\mathmit{\\Theta}',
0x1D6EA: '\\mathmit{\\Iota}',
0x1D6EB: '\\mathmit{\\Kappa}',
0x1D6EC: '\\mathmit{\\Lambda}',
0x1D6ED: '\\mathmit{M}',
0x1D6EE: 'N',
0x1D6EF: '\\mathmit{\\Xi}',
0x1D6F0: 'O',
0x1D6F1: '\\mathmit{\\Pi}',
0x1D6F2: '\\mathmit{\\Rho}',
0x1D6F3: '\\mathmit{\\vartheta}',
0x1D6F4: '\\mathmit{\\Sigma}',
0x1D6F5: '\\mathmit{\\Tau}',
0x1D6F6: '\\mathmit{\\Upsilon}',
0x1D6F7: '\\mathmit{\\Phi}',
0x1D6F8: '\\mathmit{\\Chi}',
0x1D6F9: '\\mathmit{\\Psi}',
0x1D6FA: '\\mathmit{\\Omega}',
0x1D6FB: '\\mathmit{\\nabla}',
0x1D6FC: '\\mathmit{\\alpha}',
0x1D6FD: '\\mathmit{\\beta}',
0x1D6FE: '\\mathmit{\\gamma}',
0x1D6FF: '\\mathmit{\\delta}',
0x1D700: '\\mathmit{\\epsilon}',
0x1D701: '\\mathmit{\\zeta}',
0x1D702: '\\mathmit{\\eta}',
0x1D703: '\\mathmit{\\theta}',
0x1D704: '\\mathmit{\\iota}',
0x1D705: '\\mathmit{\\kappa}',
0x1D706: '\\mathmit{\\lambda}',
0x1D707: '\\mathmit{\\mu}',
0x1D708: '\\mathmit{\\nu}',
0x1D709: '\\mathmit{\\xi}',
0x1D70A: '\\mathmit{o}',
0x1D70B: '\\mathmit{\\pi}',
0x1D70C: '\\mathmit{\\rho}',
0x1D70D: '\\mathmit{\\varsigma}',
0x1D70E: '\\mathmit{\\sigma}',
0x1D70F: '\\mathmit{\\tau}',
0x1D710: '\\mathmit{\\upsilon}',
0x1D711: '\\mathmit{\\phi}',
0x1D712: '\\mathmit{\\chi}',
0x1D713: '\\mathmit{\\psi}',
0x1D714: '\\mathmit{\\omega}',
0x1D715: '\\partial',
0x1D716: '\\in',
0x1D717: '\\mathmit{\\vartheta}',
0x1D718: '\\mathmit{\\varkappa}',
0x1D719: '\\mathmit{\\phi}',
0x1D71A: '\\mathmit{\\varrho}',
0x1D71B: '\\mathmit{\\varpi}',
0x1D71C: '\\mathbit{\\Alpha}',
0x1D71D: '\\mathbit{\\Beta}',
0x1D71E: '\\mathbit{\\Gamma}',
0x1D71F: '\\mathbit{\\Delta}',
0x1D720: '\\mathbit{\\Epsilon}',
0x1D721: '\\mathbit{\\Zeta}',
0x1D722: '\\mathbit{\\Eta}',
0x1D723: '\\mathbit{\\Theta}',
0x1D724: '\\mathbit{\\Iota}',
0x1D725: '\\mathbit{\\Kappa}',
0x1D726: '\\mathbit{\\Lambda}',
0x1D727: '\\mathbit{M}',
0x1D728: '\\mathbit{N}',
0x1D729: '\\mathbit{\\Xi}',
0x1D72A: 'O',
0x1D72B: '\\mathbit{\\Pi}',
0x1D72C: '\\mathbit{\\Rho}',
0x1D72D: '\\mathbit{O}',
0x1D72E: '\\mathbit{\\Sigma}',
0x1D72F: '\\mathbit{\\Tau}',
0x1D730: '\\mathbit{\\Upsilon}',
0x1D731: '\\mathbit{\\Phi}',
0x1D732: '\\mathbit{\\Chi}',
0x1D733: '\\mathbit{\\Psi}',
0x1D734: '\\mathbit{\\Omega}',
0x1D735: '\\mathbit{\\nabla}',
0x1D736: '\\mathbit{\\alpha}',
0x1D737: '\\mathbit{\\beta}',
0x1D738: '\\mathbit{\\gamma}',
0x1D739: '\\mathbit{\\delta}',
0x1D73A: '\\mathbit{\\epsilon}',
0x1D73B: '\\mathbit{\\zeta}',
0x1D73C: '\\mathbit{\\eta}',
0x1D73D: '\\mathbit{\\theta}',
0x1D73E: '\\mathbit{\\iota}',
0x1D73F: '\\mathbit{\\kappa}',
0x1D740: '\\mathbit{\\lambda}',
0x1D741: '\\mathbit{\\mu}',
0x1D742: '\\mathbit{\\nu}',
0x1D743: '\\mathbit{\\xi}',
0x1D744: '\\mathbit{o}',
0x1D745: '\\mathbit{\\pi}',
0x1D746: '\\mathbit{\\rho}',
0x1D747: '\\mathbit{\\varsigma}',
0x1D748: '\\mathbit{\\sigma}',
0x1D749: '\\mathbit{\\tau}',
0x1D74A: '\\mathbit{\\upsilon}',
0x1D74B: '\\mathbit{\\phi}',
0x1D74C: '\\mathbit{\\chi}',
0x1D74D: '\\mathbit{\\psi}',
0x1D74E: '\\mathbit{\\omega}',
0x1D74F: '\\partial',
0x1D750: '\\in',
0x1D751: '\\mathbit{\\vartheta}',
0x1D752: '\\mathbit{\\varkappa}',
0x1D753: '\\mathbit{\\phi}',
0x1D754: '\\mathbit{\\varrho}',
0x1D755: '\\mathbit{\\varpi}',
0x1D756: '\\mathsfbf{\\Alpha}',
0x1D757: '\\mathsfbf{\\Beta}',
0x1D758: '\\mathsfbf{\\Gamma}',
0x1D759: '\\mathsfbf{\\Delta}',
0x1D75A: '\\mathsfbf{\\Epsilon}',
0x1D75B: '\\mathsfbf{\\Zeta}',
0x1D75C: '\\mathsfbf{\\Eta}',
0x1D75D: '\\mathsfbf{\\Theta}',
0x1D75E: '\\mathsfbf{\\Iota}',
0x1D75F: '\\mathsfbf{\\Kappa}',
0x1D760: '\\mathsfbf{\\Lambda}',
0x1D761: '\\mathsfbf{M}',
0x1D762: '\\mathsfbf{N}',
0x1D763: '\\mathsfbf{\\Xi}',
0x1D764: 'O',
0x1D765: '\\mathsfbf{\\Pi}',
0x1D766: '\\mathsfbf{\\Rho}',
0x1D767: '\\mathsfbf{\\vartheta}',
0x1D768: '\\mathsfbf{\\Sigma}',
0x1D769: '\\mathsfbf{\\Tau}',
0x1D76A: '\\mathsfbf{\\Upsilon}',
0x1D76B: '\\mathsfbf{\\Phi}',
0x1D76C: '\\mathsfbf{\\Chi}',
0x1D76D: '\\mathsfbf{\\Psi}',
0x1D76E: '\\mathsfbf{\\Omega}',
0x1D76F: '\\mathsfbf{\\nabla}',
0x1D770: '\\mathsfbf{\\alpha}',
0x1D771: '\\mathsfbf{\\beta}',
0x1D772: '\\mathsfbf{\\gamma}',
0x1D773: '\\mathsfbf{\\delta}',
0x1D774: '\\mathsfbf{\\epsilon}',
0x1D775: '\\mathsfbf{\\zeta}',
0x1D776: '\\mathsfbf{\\eta}',
0x1D777: '\\mathsfbf{\\theta}',
0x1D778: '\\mathsfbf{\\iota}',
0x1D779: '\\mathsfbf{\\kappa}',
0x1D77A: '\\mathsfbf{\\lambda}',
0x1D77B: '\\mathsfbf{\\mu}',
0x1D77C: '\\mathsfbf{\\nu}',
0x1D77D: '\\mathsfbf{\\xi}',
0x1D77E: '\\mathsfbf{o}',
0x1D77F: '\\mathsfbf{\\pi}',
0x1D780: '\\mathsfbf{\\rho}',
0x1D781: '\\mathsfbf{\\varsigma}',
0x1D782: '\\mathsfbf{\\sigma}',
0x1D783: '\\mathsfbf{\\tau}',
0x1D784: '\\mathsfbf{\\upsilon}',
0x1D785: '\\mathsfbf{\\phi}',
0x1D786: '\\mathsfbf{\\chi}',
0x1D787: '\\mathsfbf{\\psi}',
0x1D788: '\\mathsfbf{\\omega}',
0x1D789: '\\partial',
0x1D78A: '\\mathsfbf{\\varepsilon}',
0x1D78B: '\\mathsfbf{\\vartheta}',
0x1D78C: '\\mathsfbf{\\varkappa}',
0x1D78D: '\\mathsfbf{\\phi}',
0x1D78E: '\\mathsfbf{\\varrho}',
0x1D78F: '\\mathsfbf{\\varpi}',
0x1D790: '\\mathsfbfsl{\\Alpha}',
0x1D791: '\\mathsfbfsl{\\Beta}',
0x1D792: '\\mathsfbfsl{\\Gamma}',
0x1D793: '\\mathsfbfsl{\\Delta}',
0x1D794: '\\mathsfbfsl{\\Epsilon}',
0x1D795: '\\mathsfbfsl{\\Zeta}',
0x1D796: '\\mathsfbfsl{\\Eta}',
0x1D797: '\\mathsfbfsl{\\vartheta}',
0x1D798: '\\mathsfbfsl{\\Iota}',
0x1D799: '\\mathsfbfsl{\\Kappa}',
0x1D79A: '\\mathsfbfsl{\\Lambda}',
0x1D79B: '\\mathsfbfsl{M}',
0x1D79C: '\\mathsfbfsl{N}',
0x1D79D: '\\mathsfbfsl{\\Xi}',
0x1D79E: 'O',
0x1D79F: '\\mathsfbfsl{\\Pi}',
0x1D7A0: '\\mathsfbfsl{\\Rho}',
0x1D7A1: '\\mathsfbfsl{\\vartheta}',
0x1D7A2: '\\mathsfbfsl{\\Sigma}',
0x1D7A3: '\\mathsfbfsl{\\Tau}',
0x1D7A4: '\\mathsfbfsl{\\Upsilon}',
0x1D7A5: '\\mathsfbfsl{\\Phi}',
0x1D7A6: '\\mathsfbfsl{\\Chi}',
0x1D7A7: '\\mathsfbfsl{\\Psi}',
0x1D7A8: '\\mathsfbfsl{\\Omega}',
0x1D7A9: '\\mathsfbfsl{\\nabla}',
0x1D7AA: '\\mathsfbfsl{\\alpha}',
0x1D7AB: '\\mathsfbfsl{\\beta}',
0x1D7AC: '\\mathsfbfsl{\\gamma}',
0x1D7AD: '\\mathsfbfsl{\\delta}',
0x1D7AE: '\\mathsfbfsl{\\epsilon}',
0x1D7AF: '\\mathsfbfsl{\\zeta}',
0x1D7B0: '\\mathsfbfsl{\\eta}',
0x1D7B1: '\\mathsfbfsl{\\vartheta}',
0x1D7B2: '\\mathsfbfsl{\\iota}',
0x1D7B3: '\\mathsfbfsl{\\kappa}',
0x1D7B4: '\\mathsfbfsl{\\lambda}',
0x1D7B5: '\\mathsfbfsl{\\mu}',
0x1D7B6: '\\mathsfbfsl{\\nu}',
0x1D7B7: '\\mathsfbfsl{\\xi}',
0x1D7B8: '\\mathsfbfsl{o}',
0x1D7B9: '\\mathsfbfsl{\\pi}',
0x1D7BA: '\\mathsfbfsl{\\rho}',
0x1D7BB: '\\mathsfbfsl{\\varsigma}',
0x1D7BC: '\\mathsfbfsl{\\sigma}',
0x1D7BD: '\\mathsfbfsl{\\tau}',
0x1D7BE: '\\mathsfbfsl{\\upsilon}',
0x1D7BF: '\\mathsfbfsl{\\phi}',
0x1D7C0: '\\mathsfbfsl{\\chi}',
0x1D7C1: '\\mathsfbfsl{\\psi}',
0x1D7C2: '\\mathsfbfsl{\\omega}',
0x1D7C3: '\\partial',
0x1D7C4: '\\in',
0x1D7C5: '\\mathsfbfsl{\\vartheta}',
0x1D7C6: '\\mathsfbfsl{\\varkappa}',
0x1D7C7: '\\mathsfbfsl{\\phi}',
0x1D7C8: '\\mathsfbfsl{\\varrho}',
0x1D7C9: '\\mathsfbfsl{\\varpi}',
0x1D7CE: '\\mathbf{0}',
0x1D7CF: '\\mathbf{1}',
0x1D7D0: '\\mathbf{2}',
0x1D7D1: '\\mathbf{3}',
0x1D7D2: '\\mathbf{4}',
0x1D7D3: '\\mathbf{5}',
0x1D7D4: '\\mathbf{6}',
0x1D7D5: '\\mathbf{7}',
0x1D7D6: '\\mathbf{8}',
0x1D7D7: '\\mathbf{9}',
0x1D7D8: '\\mathbb{0}',
0x1D7D9: '\\mathbb{1}',
0x1D7DA: '\\mathbb{2}',
0x1D7DB: '\\mathbb{3}',
0x1D7DC: '\\mathbb{4}',
0x1D7DD: '\\mathbb{5}',
0x1D7DE: '\\mathbb{6}',
0x1D7DF: '\\mathbb{7}',
0x1D7E0: '\\mathbb{8}',
0x1D7E1: '\\mathbb{9}',
0x1D7E2: '\\mathsf{0}',
0x1D7E3: '\\mathsf{1}',
0x1D7E4: '\\mathsf{2}',
0x1D7E5: '\\mathsf{3}',
0x1D7E6: '\\mathsf{4}',
0x1D7E7: '\\mathsf{5}',
0x1D7E8: '\\mathsf{6}',
0x1D7E9: '\\mathsf{7}',
0x1D7EA: '\\mathsf{8}',
0x1D7EB: '\\mathsf{9}',
0x1D7EC: '\\mathsfbf{0}',
0x1D7ED: '\\mathsfbf{1}',
0x1D7EE: '\\mathsfbf{2}',
0x1D7EF: '\\mathsfbf{3}',
0x1D7F0: '\\mathsfbf{4}',
0x1D7F1: '\\mathsfbf{5}',
0x1D7F2: '\\mathsfbf{6}',
0x1D7F3: '\\mathsfbf{7}',
0x1D7F4: '\\mathsfbf{8}',
0x1D7F5: '\\mathsfbf{9}',
0x1D7F6: '\\mathtt{0}',
0x1D7F7: '\\mathtt{1}',
0x1D7F8: '\\mathtt{2}',
0x1D7F9: '\\mathtt{3}',
0x1D7FA: '\\mathtt{4}',
0x1D7FB: '\\mathtt{5}',
0x1D7FC: '\\mathtt{6}',
0x1D7FD: '\\mathtt{7}',
0x1D7FE: '\\mathtt{8}',
0x1D7FF: '\\mathtt{9}',
}
