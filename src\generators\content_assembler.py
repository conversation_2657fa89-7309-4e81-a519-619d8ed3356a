"""
内容组装器

负责组装和整合各种内容元素到Word文档中。
"""

from typing import Dict, List, Any, Optional
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager

logger = get_logger(__name__)


class ContentAssembler:
    """
    内容组装器
    
    负责将解析后的LaTeX内容组装到Word文档中。
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化内容组装器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or ConfigManager()
        self.config = self.config_manager.get_config()
    
    def assemble_document_content(
        self,
        document: Document,
        latex_document: Dict[str, Any],
        formula_results: List[Any]
    ) -> None:
        """
        组装文档内容
        
        Args:
            document: Word文档对象
            latex_document: 解析后的LaTeX文档
            formula_results: 公式转换结果
        """
        logger.debug("开始组装文档内容")
        
        # 创建公式映射
        formula_map = self._create_formula_map(formula_results)
        
        # 组装各个部分
        self._assemble_title_section(document, latex_document)
        self._assemble_main_content(document, latex_document, formula_map)
        self._assemble_references(document, latex_document)
    
    def _create_formula_map(self, formula_results: List[Any]) -> Dict[int, Any]:
        """创建公式映射"""
        formula_map = {}
        for i, result in enumerate(formula_results):
            formula_map[i] = result
        return formula_map
    
    def _assemble_title_section(
        self,
        document: Document,
        latex_document: Dict[str, Any]
    ) -> None:
        """组装标题部分"""
        # 添加标题
        title = latex_document.get('title')
        if title:
            clean_title = self._clean_latex_text(title)
            title_paragraph = document.add_heading(clean_title, level=0)
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加作者
        author = latex_document.get('author')
        if author:
            clean_author = self._clean_latex_text(author)
            author_paragraph = document.add_paragraph(clean_author)
            author_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加日期
        date = latex_document.get('date')
        if date and date != r'\today':
            clean_date = self._clean_latex_text(date)
            date_paragraph = document.add_paragraph(clean_date)
            date_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加分隔
        document.add_paragraph()
    
    def _assemble_main_content(
        self,
        document: Document,
        latex_document: Dict[str, Any],
        formula_map: Dict[int, Any]
    ) -> None:
        """组装主要内容"""
        sections = latex_document.get('sections', [])
        
        for section in sections:
            self._assemble_section(document, section, formula_map)
    
    def _assemble_section(
        self,
        document: Document,
        section: Dict[str, Any],
        formula_map: Dict[int, Any]
    ) -> None:
        """组装章节"""
        # 添加章节标题
        title = section.get('title', '')
        level = section.get('level', 1)
        
        if title:
            clean_title = self._clean_latex_text(title)
            word_level = min(level, 9)  # Word最多支持9级标题
            document.add_heading(clean_title, level=word_level)
        
        # 组装章节内容
        content = section.get('content', '')
        if content:
            self._assemble_text_content(document, content, formula_map)
        
        # 递归处理子章节
        subsections = section.get('subsections', [])
        for subsection in subsections:
            self._assemble_section(document, subsection, formula_map)
    
    def _assemble_text_content(
        self,
        document: Document,
        content: str,
        formula_map: Dict[int, Any]
    ) -> None:
        """组装文本内容"""
        # 分段处理
        paragraphs = content.split('\n\n')
        
        for para_text in paragraphs:
            para_text = para_text.strip()
            if para_text:
                # 处理包含公式的段落
                self._process_paragraph_with_formulas(document, para_text, formula_map)
    
    def _process_paragraph_with_formulas(
        self,
        document: Document,
        paragraph_text: str,
        formula_map: Dict[int, Any]
    ) -> None:
        """处理包含公式的段落"""
        # 简化版本：直接添加文本段落
        # 在完整实现中，这里需要解析内联公式并替换
        
        clean_text = self._clean_latex_text(paragraph_text)
        if clean_text:
            paragraph = document.add_paragraph(clean_text)
            
            # 查找并处理内联公式（简化版本）
            import re
            inline_formulas = re.findall(r'\$([^$]+)\$', paragraph_text)
            
            if inline_formulas:
                # 添加公式占位符
                for i, formula in enumerate(inline_formulas):
                    placeholder = f" [公式{i+1}: {formula[:20]}...] "
                    # 在实际实现中，这里会插入真正的公式
    
    def _assemble_references(
        self,
        document: Document,
        latex_document: Dict[str, Any]
    ) -> None:
        """组装参考文献"""
        references = latex_document.get('references', [])
        
        if references:
            # 添加参考文献标题
            document.add_heading("参考文献", level=1)
            
            # 添加参考文献列表
            for i, ref in enumerate(references):
                if ref.get('type') == 'citation':
                    ref_text = f"[{i+1}] {ref.get('key', '')}"
                    document.add_paragraph(ref_text)
    
    def assemble_tables(
        self,
        document: Document,
        tables: List[Dict[str, Any]]
    ) -> None:
        """
        组装表格
        
        Args:
            document: Word文档对象
            tables: 表格列表
        """
        for table_data in tables:
            self._add_table_to_document(document, table_data)
    
    def _add_table_to_document(
        self,
        document: Document,
        table_data: Dict[str, Any]
    ) -> None:
        """添加表格到文档"""
        try:
            # 解析表格内容（简化版本）
            content = table_data.get('content', '')
            caption = table_data.get('caption')
            
            # 添加表格标题
            if caption:
                caption_paragraph = document.add_paragraph(f"表: {caption}")
                caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 创建简单表格（2x2示例）
            table = document.add_table(rows=2, cols=2)
            table.style = 'Table Grid'
            
            # 填充示例数据
            cells = table._cells
            cells[0].text = "列1"
            cells[1].text = "列2"
            cells[2].text = "数据1"
            cells[3].text = "数据2"
            
            # 添加空行
            document.add_paragraph()
            
        except Exception as e:
            logger.warning(f"添加表格失败: {e}")
            # 添加占位符
            document.add_paragraph(f"[表格: {table_data.get('caption', '未知')}]")
    
    def assemble_figures(
        self,
        document: Document,
        figures: List[Dict[str, Any]]
    ) -> None:
        """
        组装图形
        
        Args:
            document: Word文档对象
            figures: 图形列表
        """
        for figure_data in figures:
            self._add_figure_to_document(document, figure_data)
    
    def _add_figure_to_document(
        self,
        document: Document,
        figure_data: Dict[str, Any]
    ) -> None:
        """添加图形到文档"""
        try:
            filename = figure_data.get('filename')
            caption = figure_data.get('caption')
            
            # 添加图形占位符
            if filename:
                placeholder_text = f"[图形: {filename}]"
            else:
                placeholder_text = "[图形]"
            
            figure_paragraph = document.add_paragraph(placeholder_text)
            figure_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 添加图形标题
            if caption:
                caption_paragraph = document.add_paragraph(f"图: {caption}")
                caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 添加空行
            document.add_paragraph()
            
        except Exception as e:
            logger.warning(f"添加图形失败: {e}")
            document.add_paragraph(f"[图形: {figure_data.get('caption', '未知')}]")
    
    def assemble_formulas(
        self,
        document: Document,
        formulas: List[Dict[str, Any]],
        formula_results: List[Any]
    ) -> None:
        """
        组装公式
        
        Args:
            document: Word文档对象
            formulas: 公式列表
            formula_results: 公式转换结果
        """
        for i, (formula, result) in enumerate(zip(formulas, formula_results)):
            self._add_formula_to_document(document, formula, result, i)
    
    def _add_formula_to_document(
        self,
        document: Document,
        formula: Dict[str, Any],
        result: Any,
        index: int
    ) -> None:
        """添加公式到文档"""
        try:
            formula_type = formula.get('type', 'inline')
            
            if result.success:
                # 成功转换的公式
                if formula_type == 'inline':
                    # 内联公式
                    placeholder = f"[内联公式{index+1}]"
                else:
                    # 显示公式
                    placeholder = f"[显示公式{index+1}]"
                    formula_paragraph = document.add_paragraph(placeholder)
                    formula_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            else:
                # 转换失败的公式
                error_text = f"[公式转换失败: {result.error_message}]"
                document.add_paragraph(error_text)
                
        except Exception as e:
            logger.warning(f"添加公式失败: {e}")
            document.add_paragraph(f"[公式{index+1}: 处理失败]")
    
    def _clean_latex_text(self, text: str) -> str:
        """清理LaTeX文本"""
        if not text:
            return ""
        
        import re
        
        # 移除常见的LaTeX命令
        text = re.sub(r'\\textbf\{([^}]+)\}', r'\1', text)  # 粗体
        text = re.sub(r'\\textit\{([^}]+)\}', r'\1', text)  # 斜体
        text = re.sub(r'\\emph\{([^}]+)\}', r'\1', text)    # 强调
        text = re.sub(r'\\texttt\{([^}]+)\}', r'\1', text)  # 等宽字体
        
        # 移除其他命令
        text = re.sub(r'\\[a-zA-Z]+\{([^}]*)\}', r'\1', text)
        text = re.sub(r'\\[a-zA-Z]+', '', text)
        
        # 处理特殊字符
        text = text.replace(r'\&', '&')
        text = text.replace(r'\%', '%')
        text = text.replace(r'\$', '$')
        text = text.replace(r'\#', '#')
        text = text.replace(r'\_', '_')
        text = text.replace(r'\{', '{')
        text = text.replace(r'\}', '}')
        text = text.replace(r'\\', '\n')
        text = text.replace(r'\~', ' ')
        
        # 清理多余的空白
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def add_page_break_before_section(
        self,
        document: Document,
        section_level: int = 1
    ) -> None:
        """
        在指定级别的章节前添加分页符
        
        Args:
            document: Word文档对象
            section_level: 章节级别
        """
        if section_level <= 2:  # 只在主要章节前分页
            document.add_page_break()
    
    def get_content_statistics(
        self,
        latex_document: Dict[str, Any]
    ) -> Dict[str, int]:
        """
        获取内容统计信息
        
        Args:
            latex_document: LaTeX文档对象
            
        Returns:
            统计信息字典
        """
        stats = {
            'sections': len(latex_document.get('sections', [])),
            'formulas': len(latex_document.get('formulas', [])),
            'tables': len(latex_document.get('tables', [])),
            'figures': len(latex_document.get('figures', [])),
            'references': len(latex_document.get('references', [])),
            'total_characters': 0,
            'total_words': 0
        }
        
        # 计算文本统计
        all_content = ""
        for section in latex_document.get('sections', []):
            all_content += section.get('content', '')
        
        stats['total_characters'] = len(all_content)
        stats['total_words'] = len(all_content.split())
        
        return stats
