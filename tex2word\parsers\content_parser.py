"""
Content parser for LaTeX documents.

This module provides parsing for general LaTeX content including
text, commands, and other non-structural elements.
"""

import re
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

from ..core.parser import BaseParser, ParsedElement
from ..core.exceptions import ParseError


class ContentParser(BaseParser):
    """Parser for general LaTeX content."""
    
    # Common LaTeX commands
    TEXT_COMMANDS = {
        'textbf', 'textit', 'texttt', 'textsc', 'emph', 'underline',
        'textcolor', 'colorbox', 'fbox', 'framebox'
    }
    
    # Special characters that need escaping
    SPECIAL_CHARS = {
        r'\&': '&',
        r'\%': '%',
        r'\$': '$',
        r'\#': '#',
        r'\_': '_',
        r'\{': '{',
        r'\}': '}',
        r'\\': '\n',
        r'\~': ' ',  # Non-breaking space
        r'\ ': ' ',  # Explicit space
    }
    
    def __init__(self):
        """Initialize the content parser."""
        super().__init__()
        self.in_math_mode = False
        self.current_environment = None
    
    def parse(self) -> List[ParsedElement]:
        """
        Parse general content from the text.
        
        Returns:
            List of parsed content elements
        """
        elements = []
        self.current_position = 0
        self.current_line = 1
        
        while not self.is_at_end():
            element = self._parse_next_content()
            if element:
                elements.append(element)
            else:
                self.advance()
        
        return elements
    
    def _parse_next_content(self) -> Optional[ParsedElement]:
        """
        Parse the next content element.
        
        Returns:
            ParsedElement if found, None otherwise
        """
        start_pos = self.current_position
        
        # Skip whitespace
        self.skip_whitespace()
        
        if self.is_at_end():
            return None
        
        # Check for commands
        if self.peek() == '\\':
            return self._parse_command(start_pos)
        
        # Check for special characters
        special_element = self._parse_special_chars(start_pos)
        if special_element:
            return special_element
        
        # Parse regular text
        return self._parse_text(start_pos)
    
    def _parse_command(self, start_pos: int) -> Optional[ParsedElement]:
        """Parse LaTeX commands."""
        if not self.peek() == '\\':
            return None
        
        # Match command pattern
        cmd_match = self.match_pattern(r'\\([a-zA-Z]+)\*?')
        if not cmd_match:
            # Handle single character commands like \\, \{, etc.
            single_char_match = self.match_pattern(r'\\(.)')
            if single_char_match:
                cmd_name = single_char_match.group(1)
                self.advance(2)
                
                return ParsedElement(
                    element_type='command',
                    content=self.SPECIAL_CHARS.get(f'\\{cmd_name}', cmd_name),
                    attributes={'command': cmd_name, 'type': 'special'},
                    start_pos=start_pos,
                    end_pos=self.current_position,
                    line_number=self.current_line
                )
            return None
        
        cmd_name = cmd_match.group(1)
        is_starred = '*' in cmd_match.group(0)
        
        # Advance past command name
        self.advance(len(cmd_match.group(0)))
        
        # Parse command arguments
        arguments = self._parse_command_arguments()
        
        # Handle specific command types
        if cmd_name in self.TEXT_COMMANDS:
            return self._create_text_command_element(
                start_pos, cmd_name, arguments, is_starred
            )
        else:
            return self._create_generic_command_element(
                start_pos, cmd_name, arguments, is_starred
            )
    
    def _parse_command_arguments(self) -> List[str]:
        """Parse command arguments (both required {} and optional [])."""
        arguments = []
        
        # Skip whitespace
        self.skip_whitespace()
        
        # Parse optional arguments [...]
        while self.peek() == '[':
            self.advance()  # Skip [
            arg_start = self.current_position
            
            bracket_count = 1
            while not self.is_at_end() and bracket_count > 0:
                char = self.peek()
                if char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
                elif char == '\\' and self.peek(1) in '[]':
                    self.advance()  # Skip escaped bracket
                self.advance()
            
            if bracket_count == 0:
                arg_content = self.text[arg_start:self.current_position - 1]
                arguments.append(f'[{arg_content}]')
            
            self.skip_whitespace()
        
        # Parse required arguments {...}
        while self.peek() == '{':
            try:
                content, end_pos = self.extract_braced_content()
                arguments.append(content)
                self.current_position = end_pos + 1
                self.skip_whitespace()
            except ParseError:
                break
        
        return arguments
    
    def _create_text_command_element(self, start_pos: int, cmd_name: str,
                                   arguments: List[str], is_starred: bool) -> ParsedElement:
        """Create element for text formatting commands."""
        content = arguments[0] if arguments else ""
        
        # Determine formatting type
        format_type = {
            'textbf': 'bold',
            'textit': 'italic',
            'texttt': 'monospace',
            'textsc': 'small_caps',
            'emph': 'emphasis',
            'underline': 'underline'
        }.get(cmd_name, 'unknown')
        
        return ParsedElement(
            element_type='formatted_text',
            content=content,
            attributes={
                'command': cmd_name,
                'format_type': format_type,
                'starred': is_starred,
                'arguments': arguments
            },
            start_pos=start_pos,
            end_pos=self.current_position,
            line_number=self.current_line
        )
    
    def _create_generic_command_element(self, start_pos: int, cmd_name: str,
                                      arguments: List[str], is_starred: bool) -> ParsedElement:
        """Create element for generic commands."""
        return ParsedElement(
            element_type='command',
            content=' '.join(arguments) if arguments else '',
            attributes={
                'command': cmd_name,
                'starred': is_starred,
                'arguments': arguments
            },
            start_pos=start_pos,
            end_pos=self.current_position,
            line_number=self.current_line
        )
    
    def _parse_special_chars(self, start_pos: int) -> Optional[ParsedElement]:
        """Parse special characters and escape sequences."""
        # Check for escaped characters
        for escape_seq, replacement in self.SPECIAL_CHARS.items():
            if self.text[self.current_position:].startswith(escape_seq):
                self.advance(len(escape_seq))
                
                return ParsedElement(
                    element_type='text',
                    content=replacement,
                    attributes={'type': 'special_char', 'original': escape_seq},
                    start_pos=start_pos,
                    end_pos=self.current_position,
                    line_number=self.current_line
                )
        
        return None
    
    def _parse_text(self, start_pos: int) -> Optional[ParsedElement]:
        """Parse regular text content."""
        text_start = self.current_position
        text_content = ""
        
        # Collect text until we hit a special character or command
        while not self.is_at_end():
            char = self.peek()
            
            # Stop at LaTeX commands
            if char == '\\':
                break
            
            # Stop at math delimiters
            if char == '$':
                break
            
            # Stop at environment markers
            if char == '{' or char == '}':
                break
            
            # Stop at comment markers
            if char == '%':
                break
            
            # Collect the character
            text_content += char
            self.advance()
        
        if text_content.strip():
            return ParsedElement(
                element_type='text',
                content=text_content,
                attributes={'type': 'regular'},
                start_pos=start_pos,
                end_pos=self.current_position,
                line_number=self.current_line
            )
        
        return None
    
    def _skip_comments(self) -> None:
        """Skip LaTeX comments."""
        while (self.current_position < self.length and 
               self.text[self.current_position] == '%'):
            # Skip to end of line
            while (self.current_position < self.length and 
                   self.text[self.current_position] != '\n'):
                self.current_position += 1
            
            # Skip the newline
            if (self.current_position < self.length and 
                self.text[self.current_position] == '\n'):
                self.current_line += 1
                self.current_position += 1
    
    def extract_paragraph_content(self, text: str) -> List[ParsedElement]:
        """
        Extract content from a paragraph, handling inline formatting.
        
        Args:
            text: Paragraph text to parse
            
        Returns:
            List of content elements
        """
        # Save current state
        saved_pos = self.current_position
        saved_line = self.current_line
        saved_text = self.text
        
        # Set new text
        self.set_text(text)
        
        # Parse content
        elements = self.parse()
        
        # Restore state
        self.current_position = saved_pos
        self.current_line = saved_line
        self.text = saved_text
        self.length = len(saved_text)
        
        return elements
    
    def normalize_whitespace(self, text: str) -> str:
        """
        Normalize whitespace in text content.
        
        Args:
            text: Text to normalize
            
        Returns:
            Normalized text
        """
        # Replace multiple whitespace with single space
        normalized = re.sub(r'\s+', ' ', text)
        
        # Handle LaTeX line breaks
        normalized = re.sub(r'\\\\', '\n', normalized)
        
        # Handle explicit spaces
        normalized = re.sub(r'\\ ', ' ', normalized)
        
        return normalized.strip()
