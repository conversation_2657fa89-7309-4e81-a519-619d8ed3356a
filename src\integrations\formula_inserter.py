"""
公式插入器

负责将转换后的公式插入到Word文档中。
"""

from typing import Dict, List, Any, Optional
from docx import Document
from docx.shared import Pt

from ..utils.logger import get_logger
from ..core.exceptions import WordGenerationError

logger = get_logger(__name__)


class FormulaInserter:
    """
    公式插入器
    
    负责将转换后的公式插入到Word文档的适当位置。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化公式插入器
        
        Args:
            config: 插入器配置
        """
        self.config = config or {}
    
    def insert_inline_formula(
        self,
        paragraph,
        formula_content: str,
        position: Optional[int] = None
    ) -> bool:
        """
        插入内联公式
        
        Args:
            paragraph: 段落对象
            formula_content: 公式内容
            position: 插入位置
            
        Returns:
            是否成功插入
        """
        try:
            # 目前使用占位符
            placeholder = f"[内联公式: {formula_content[:20]}...]"
            
            if position is not None:
                # 在指定位置插入
                run = paragraph.runs[position] if position < len(paragraph.runs) else paragraph.add_run()
            else:
                # 在末尾添加
                run = paragraph.add_run()
            
            run.text = placeholder
            run.font.italic = True
            run.font.size = Pt(11)
            
            logger.debug(f"插入内联公式: {formula_content[:30]}...")
            return True
            
        except Exception as e:
            logger.error(f"插入内联公式失败: {e}")
            return False
    
    def insert_display_formula(
        self,
        document: Document,
        formula_content: str,
        formula_number: Optional[str] = None
    ) -> bool:
        """
        插入显示公式
        
        Args:
            document: 文档对象
            formula_content: 公式内容
            formula_number: 公式编号
            
        Returns:
            是否成功插入
        """
        try:
            # 创建公式段落
            formula_paragraph = document.add_paragraph()
            formula_paragraph.alignment = 1  # 居中对齐
            
            # 添加公式内容
            placeholder = f"[显示公式: {formula_content[:30]}...]"
            run = formula_paragraph.add_run(placeholder)
            run.font.italic = True
            run.font.size = Pt(12)
            
            # 添加公式编号（如果有）
            if formula_number:
                formula_paragraph.add_run(f"  ({formula_number})")
            
            logger.debug(f"插入显示公式: {formula_content[:30]}...")
            return True
            
        except Exception as e:
            logger.error(f"插入显示公式失败: {e}")
            return False
    
    def insert_equation_array(
        self,
        document: Document,
        equations: List[str],
        alignment: str = "center"
    ) -> bool:
        """
        插入方程组
        
        Args:
            document: 文档对象
            equations: 方程列表
            alignment: 对齐方式
            
        Returns:
            是否成功插入
        """
        try:
            # 创建方程组段落
            eq_paragraph = document.add_paragraph()
            
            if alignment == "center":
                eq_paragraph.alignment = 1
            elif alignment == "left":
                eq_paragraph.alignment = 0
            elif alignment == "right":
                eq_paragraph.alignment = 2
            
            # 添加方程组内容
            for i, equation in enumerate(equations):
                if i > 0:
                    eq_paragraph.add_run("\n")
                
                placeholder = f"[方程{i+1}: {equation[:20]}...]"
                run = eq_paragraph.add_run(placeholder)
                run.font.italic = True
            
            logger.debug(f"插入方程组: {len(equations)} 个方程")
            return True
            
        except Exception as e:
            logger.error(f"插入方程组失败: {e}")
            return False
    
    def insert_matrix(
        self,
        document: Document,
        matrix_data: Dict[str, Any]
    ) -> bool:
        """
        插入矩阵
        
        Args:
            document: 文档对象
            matrix_data: 矩阵数据
            
        Returns:
            是否成功插入
        """
        try:
            matrix_type = matrix_data.get('matrix_type', 'matrix')
            content = matrix_data.get('matrix_content', '')
            
            # 创建矩阵段落
            matrix_paragraph = document.add_paragraph()
            matrix_paragraph.alignment = 1  # 居中
            
            placeholder = f"[{matrix_type}矩阵: {content[:30]}...]"
            run = matrix_paragraph.add_run(placeholder)
            run.font.italic = True
            run.font.size = Pt(12)
            
            logger.debug(f"插入矩阵: {matrix_type}")
            return True
            
        except Exception as e:
            logger.error(f"插入矩阵失败: {e}")
            return False
    
    def replace_formula_placeholder(
        self,
        document: Document,
        placeholder: str,
        formula_content: str
    ) -> bool:
        """
        替换公式占位符
        
        Args:
            document: 文档对象
            placeholder: 占位符文本
            formula_content: 公式内容
            
        Returns:
            是否成功替换
        """
        try:
            replaced_count = 0
            
            for paragraph in document.paragraphs:
                if placeholder in paragraph.text:
                    # 简单的文本替换
                    paragraph.text = paragraph.text.replace(placeholder, formula_content)
                    replaced_count += 1
            
            logger.debug(f"替换了 {replaced_count} 个占位符")
            return replaced_count > 0
            
        except Exception as e:
            logger.error(f"替换占位符失败: {e}")
            return False
    
    def format_formula_style(
        self,
        run,
        formula_type: str = "inline"
    ) -> None:
        """
        格式化公式样式
        
        Args:
            run: 文本运行对象
            formula_type: 公式类型
        """
        try:
            if formula_type == "inline":
                run.font.size = Pt(11)
                run.font.italic = True
            elif formula_type == "display":
                run.font.size = Pt(12)
                run.font.italic = True
            
            # 可以添加更多样式设置
            
        except Exception as e:
            logger.warning(f"格式化公式样式失败: {e}")
    
    def get_insertion_statistics(self) -> Dict[str, int]:
        """
        获取插入统计信息
        
        Returns:
            统计信息字典
        """
        # 这里可以实现插入统计的跟踪
        return {
            'inline_formulas': 0,
            'display_formulas': 0,
            'matrices': 0,
            'equation_arrays': 0,
            'total_insertions': 0
        }
