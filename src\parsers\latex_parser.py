"""
LaTeX解析器

主要的LaTeX文档解析器，负责解析LaTeX文档的整体结构和内容。
"""

import re
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field

from ..core.exceptions import LaTeXParseError
from ..utils.logger import get_logger
from ..utils.error_handler import handle_errors, log_performance

logger = get_logger(__name__)


@dataclass
class LaTeXDocument:
    """LaTeX文档数据结构"""
    
    # 文档元信息
    title: Optional[str] = None
    author: Optional[str] = None
    date: Optional[str] = None
    document_class: Optional[str] = None
    
    # 文档内容
    preamble: str = ""
    body: str = ""
    
    # 解析结果
    sections: List[Dict[str, Any]] = field(default_factory=list)
    formulas: List[Dict[str, Any]] = field(default_factory=list)
    tables: List[Dict[str, Any]] = field(default_factory=list)
    figures: List[Dict[str, Any]] = field(default_factory=list)
    references: List[Dict[str, Any]] = field(default_factory=list)
    
    # 包和命令
    packages: List[str] = field(default_factory=list)
    custom_commands: Dict[str, str] = field(default_factory=dict)
    
    # 原始内容
    raw_content: str = ""
    file_path: Optional[str] = None


class LaTeXParser:
    """
    LaTeX解析器
    
    负责解析LaTeX文档，提取文档结构、内容和元信息。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化LaTeX解析器
        
        Args:
            config: 解析器配置
        """
        self.config = config or {}
        self.strict_mode = self.config.get("strict_mode", False)
        self.ignore_unknown_commands = self.config.get("ignore_unknown_commands", True)
        self.preserve_comments = self.config.get("preserve_comments", False)
        self.max_recursion_depth = self.config.get("max_recursion_depth", 100)
        
        # 编译正则表达式
        self._compile_patterns()
        
        # 已知的LaTeX命令
        self._known_commands = self._load_known_commands()
    
    def _compile_patterns(self) -> None:
        """编译常用的正则表达式模式"""
        
        # 文档类
        self.documentclass_pattern = re.compile(
            r'\\documentclass(?:\[([^\]]*)\])?\{([^}]+)\}',
            re.MULTILINE
        )
        
        # 包引用
        self.usepackage_pattern = re.compile(
            r'\\usepackage(?:\[([^\]]*)\])?\{([^}]+)\}',
            re.MULTILINE
        )
        
        # 标题信息
        self.title_pattern = re.compile(r'\\title\{([^}]+)\}', re.MULTILINE)
        self.author_pattern = re.compile(r'\\author\{([^}]+)\}', re.MULTILINE)
        self.date_pattern = re.compile(r'\\date\{([^}]+)\}', re.MULTILINE)
        
        # 文档环境
        self.document_begin_pattern = re.compile(r'\\begin\{document\}', re.MULTILINE)
        self.document_end_pattern = re.compile(r'\\end\{document\}', re.MULTILINE)
        
        # 章节标题
        self.section_pattern = re.compile(
            r'\\((?:sub)*section\*?)\{([^}]+)\}',
            re.MULTILINE
        )
        
        # 数学公式
        self.inline_math_pattern = re.compile(r'\$([^$]+)\$')
        self.display_math_pattern = re.compile(r'\$\$([^$]+)\$\$', re.DOTALL)
        self.equation_pattern = re.compile(
            r'\\begin\{(equation\*?|align\*?|eqnarray\*?)\}(.*?)\\end\{\1\}',
            re.DOTALL
        )
        
        # 环境
        self.environment_pattern = re.compile(
            r'\\begin\{([^}]+)\}(.*?)\\end\{\1\}',
            re.DOTALL
        )
        
        # 命令
        self.command_pattern = re.compile(r'\\([a-zA-Z]+)(?:\[([^\]]*)\])?\{([^}]*)\}')
        
        # 注释
        self.comment_pattern = re.compile(r'(?<!\\)%.*$', re.MULTILINE)
        
        # 自定义命令定义
        self.newcommand_pattern = re.compile(
            r'\\newcommand\{\\([^}]+)\}(?:\[(\d+)\])?\{([^}]+)\}',
            re.MULTILINE
        )
    
    def _load_known_commands(self) -> set:
        """加载已知的LaTeX命令"""
        return {
            # 文档结构
            'documentclass', 'usepackage', 'begin', 'end',
            'title', 'author', 'date', 'maketitle',
            'tableofcontents', 'newpage', 'clearpage',
            
            # 章节
            'part', 'chapter', 'section', 'subsection', 'subsubsection',
            'paragraph', 'subparagraph',
            
            # 文本格式
            'textbf', 'textit', 'texttt', 'emph', 'underline',
            'large', 'Large', 'LARGE', 'huge', 'Huge',
            'small', 'footnotesize', 'scriptsize', 'tiny',
            
            # 数学
            'frac', 'sqrt', 'sum', 'int', 'lim', 'prod',
            'alpha', 'beta', 'gamma', 'delta', 'epsilon',
            'theta', 'lambda', 'mu', 'pi', 'sigma', 'omega',
            
            # 列表
            'itemize', 'enumerate', 'description', 'item',
            
            # 表格
            'tabular', 'table', 'caption', 'label',
            
            # 图形
            'figure', 'includegraphics', 'centering',
            
            # 引用
            'cite', 'ref', 'label', 'bibliography',
        }
    
    @handle_errors(context="解析LaTeX文档")
    @log_performance
    def parse(self, content: str, file_path: Optional[str] = None) -> LaTeXDocument:
        """
        解析LaTeX文档
        
        Args:
            content: LaTeX文档内容
            file_path: 文件路径（可选）
            
        Returns:
            解析后的文档对象
            
        Raises:
            LaTeXParseError: 解析失败
        """
        logger.info(f"开始解析LaTeX文档: {file_path or '内存内容'}")
        
        # 创建文档对象
        document = LaTeXDocument(
            raw_content=content,
            file_path=file_path
        )
        
        try:
            # 预处理
            processed_content = self._preprocess_content(content)
            
            # 解析文档类和包
            self._parse_document_class(processed_content, document)
            self._parse_packages(processed_content, document)
            
            # 解析元信息
            self._parse_metadata(processed_content, document)
            
            # 分离前言和正文
            self._split_preamble_and_body(processed_content, document)
            
            # 解析自定义命令
            self._parse_custom_commands(document.preamble, document)
            
            # 解析文档结构
            from .structure_analyzer import StructureAnalyzer
            structure_analyzer = StructureAnalyzer(self.config)
            document.sections = structure_analyzer.analyze_structure(document.body)
            
            # 提取公式
            from .formula_extractor import FormulaExtractor
            formula_extractor = FormulaExtractor(self.config)
            document.formulas = formula_extractor.extract_formulas(document.body)
            
            # 解析其他元素
            self._parse_tables(document.body, document)
            self._parse_figures(document.body, document)
            self._parse_references(document.body, document)
            
            logger.info(f"LaTeX文档解析完成，包含 {len(document.sections)} 个章节，"
                       f"{len(document.formulas)} 个公式")
            
            return document
            
        except Exception as e:
            raise LaTeXParseError(
                f"LaTeX文档解析失败: {e}",
                latex_command=None
            )
    
    def _preprocess_content(self, content: str) -> str:
        """
        预处理LaTeX内容
        
        Args:
            content: 原始内容
            
        Returns:
            预处理后的内容
        """
        # 移除注释（如果配置要求）
        if not self.preserve_comments:
            content = self.comment_pattern.sub('', content)
        
        # 标准化换行符
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        
        # 移除多余的空白行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        return content.strip()
    
    def _parse_document_class(self, content: str, document: LaTeXDocument) -> None:
        """解析文档类"""
        match = self.documentclass_pattern.search(content)
        if match:
            options, doc_class = match.groups()
            document.document_class = doc_class
            logger.debug(f"文档类: {doc_class}")
    
    def _parse_packages(self, content: str, document: LaTeXDocument) -> None:
        """解析包引用"""
        matches = self.usepackage_pattern.findall(content)
        for options, packages in matches:
            # 处理多个包的情况
            package_list = [pkg.strip() for pkg in packages.split(',')]
            document.packages.extend(package_list)
        
        logger.debug(f"引用的包: {document.packages}")
    
    def _parse_metadata(self, content: str, document: LaTeXDocument) -> None:
        """解析文档元信息"""
        # 标题
        title_match = self.title_pattern.search(content)
        if title_match:
            document.title = title_match.group(1).strip()
        
        # 作者
        author_match = self.author_pattern.search(content)
        if author_match:
            document.author = author_match.group(1).strip()
        
        # 日期
        date_match = self.date_pattern.search(content)
        if date_match:
            document.date = date_match.group(1).strip()
        
        logger.debug(f"文档元信息 - 标题: {document.title}, 作者: {document.author}")
    
    def _split_preamble_and_body(self, content: str, document: LaTeXDocument) -> None:
        """分离前言和正文"""
        begin_match = self.document_begin_pattern.search(content)
        end_match = self.document_end_pattern.search(content)
        
        if begin_match:
            document.preamble = content[:begin_match.start()].strip()
            
            if end_match:
                document.body = content[begin_match.end():end_match.start()].strip()
            else:
                document.body = content[begin_match.end():].strip()
                logger.warning("未找到 \\end{document}，使用文档末尾")
        else:
            # 如果没有找到 \begin{document}，整个内容作为正文
            document.preamble = ""
            document.body = content
            logger.warning("未找到 \\begin{document}，将整个内容作为正文处理")
    
    def _parse_custom_commands(self, preamble: str, document: LaTeXDocument) -> None:
        """解析自定义命令"""
        matches = self.newcommand_pattern.findall(preamble)
        for command_name, num_args, definition in matches:
            document.custom_commands[command_name] = {
                'definition': definition,
                'num_args': int(num_args) if num_args else 0
            }
        
        logger.debug(f"自定义命令: {list(document.custom_commands.keys())}")

    def _parse_tables(self, content: str, document: LaTeXDocument) -> None:
        """解析表格"""
        # 查找表格环境
        table_pattern = re.compile(
            r'\\begin\{(table\*?|tabular)\}(.*?)\\end\{\1\}',
            re.DOTALL
        )

        matches = table_pattern.finditer(content)
        for match in matches:
            env_type, table_content = match.groups()

            # 提取表格标题
            caption_match = re.search(r'\\caption\{([^}]+)\}', table_content)
            caption = caption_match.group(1) if caption_match else None

            # 提取标签
            label_match = re.search(r'\\label\{([^}]+)\}', table_content)
            label = label_match.group(1) if label_match else None

            table_info = {
                'type': env_type,
                'content': table_content.strip(),
                'caption': caption,
                'label': label,
                'start_pos': match.start(),
                'end_pos': match.end()
            }

            document.tables.append(table_info)

        logger.debug(f"找到 {len(document.tables)} 个表格")

    def _parse_figures(self, content: str, document: LaTeXDocument) -> None:
        """解析图形"""
        # 查找图形环境
        figure_pattern = re.compile(
            r'\\begin\{figure\*?\}(.*?)\\end\{figure\*?\}',
            re.DOTALL
        )

        matches = figure_pattern.finditer(content)
        for match in matches:
            figure_content = match.group(1)

            # 提取图形文件
            includegraphics_match = re.search(
                r'\\includegraphics(?:\[([^\]]*)\])?\{([^}]+)\}',
                figure_content
            )

            if includegraphics_match:
                options, filename = includegraphics_match.groups()
            else:
                options, filename = None, None

            # 提取标题
            caption_match = re.search(r'\\caption\{([^}]+)\}', figure_content)
            caption = caption_match.group(1) if caption_match else None

            # 提取标签
            label_match = re.search(r'\\label\{([^}]+)\}', figure_content)
            label = label_match.group(1) if label_match else None

            figure_info = {
                'filename': filename,
                'options': options,
                'caption': caption,
                'label': label,
                'content': figure_content.strip(),
                'start_pos': match.start(),
                'end_pos': match.end()
            }

            document.figures.append(figure_info)

        logger.debug(f"找到 {len(document.figures)} 个图形")

    def _parse_references(self, content: str, document: LaTeXDocument) -> None:
        """解析引用"""
        # 查找引用命令
        cite_pattern = re.compile(r'\\cite(?:\[([^\]]*)\])?\{([^}]+)\}')
        ref_pattern = re.compile(r'\\ref\{([^}]+)\}')

        # 处理引用
        cite_matches = cite_pattern.finditer(content)
        for match in cite_matches:
            options, keys = match.groups()
            key_list = [key.strip() for key in keys.split(',')]

            for key in key_list:
                ref_info = {
                    'type': 'citation',
                    'key': key,
                    'options': options,
                    'start_pos': match.start(),
                    'end_pos': match.end()
                }
                document.references.append(ref_info)

        # 处理交叉引用
        ref_matches = ref_pattern.finditer(content)
        for match in ref_matches:
            key = match.group(1)

            ref_info = {
                'type': 'cross_reference',
                'key': key,
                'start_pos': match.start(),
                'end_pos': match.end()
            }
            document.references.append(ref_info)

        logger.debug(f"找到 {len(document.references)} 个引用")

    def parse_file(self, file_path: Union[str, Path]) -> LaTeXDocument:
        """
        解析LaTeX文件

        Args:
            file_path: 文件路径

        Returns:
            解析后的文档对象

        Raises:
            LaTeXParseError: 文件读取或解析失败
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise LaTeXParseError(
                f"文件不存在: {file_path}",
                latex_command=None
            )

        try:
            # 读取文件内容
            from ..utils.file_utils import FileUtils
            content = FileUtils.read_text_file(file_path)

            # 解析内容
            return self.parse(content, str(file_path))

        except Exception as e:
            raise LaTeXParseError(
                f"文件解析失败: {e}",
                latex_command=None
            )

    def validate_syntax(self, content: str) -> List[Dict[str, Any]]:
        """
        验证LaTeX语法

        Args:
            content: LaTeX内容

        Returns:
            语法错误列表
        """
        errors = []

        # 检查括号匹配
        brace_errors = self._check_brace_matching(content)
        errors.extend(brace_errors)

        # 检查环境匹配
        env_errors = self._check_environment_matching(content)
        errors.extend(env_errors)

        # 检查未知命令
        if not self.ignore_unknown_commands:
            unknown_errors = self._check_unknown_commands(content)
            errors.extend(unknown_errors)

        return errors

    def _check_brace_matching(self, content: str) -> List[Dict[str, Any]]:
        """检查括号匹配"""
        errors = []
        stack = []

        for i, char in enumerate(content):
            if char == '{':
                stack.append(('brace', i))
            elif char == '}':
                if not stack or stack[-1][0] != 'brace':
                    errors.append({
                        'type': 'unmatched_brace',
                        'message': '未匹配的右括号',
                        'position': i
                    })
                else:
                    stack.pop()

        # 检查未关闭的括号
        for brace_type, pos in stack:
            if brace_type == 'brace':
                errors.append({
                    'type': 'unclosed_brace',
                    'message': '未关闭的左括号',
                    'position': pos
                })

        return errors

    def _check_environment_matching(self, content: str) -> List[Dict[str, Any]]:
        """检查环境匹配"""
        errors = []

        # 查找所有环境
        begin_pattern = re.compile(r'\\begin\{([^}]+)\}')
        end_pattern = re.compile(r'\\end\{([^}]+)\}')

        begins = [(match.group(1), match.start()) for match in begin_pattern.finditer(content)]
        ends = [(match.group(1), match.start()) for match in end_pattern.finditer(content)]

        # 简单的环境匹配检查
        env_stack = []

        # 合并并排序所有环境标记
        all_envs = []
        for env_name, pos in begins:
            all_envs.append(('begin', env_name, pos))
        for env_name, pos in ends:
            all_envs.append(('end', env_name, pos))

        all_envs.sort(key=lambda x: x[2])  # 按位置排序

        for env_type, env_name, pos in all_envs:
            if env_type == 'begin':
                env_stack.append((env_name, pos))
            elif env_type == 'end':
                if not env_stack:
                    errors.append({
                        'type': 'unmatched_end',
                        'message': f'未匹配的 \\end{{{env_name}}}',
                        'position': pos,
                        'environment': env_name
                    })
                else:
                    last_env, last_pos = env_stack.pop()
                    if last_env != env_name:
                        errors.append({
                            'type': 'mismatched_environment',
                            'message': f'环境不匹配: \\begin{{{last_env}}} 与 \\end{{{env_name}}}',
                            'position': pos,
                            'environment': env_name
                        })

        # 检查未关闭的环境
        for env_name, pos in env_stack:
            errors.append({
                'type': 'unclosed_environment',
                'message': f'未关闭的环境: \\begin{{{env_name}}}',
                'position': pos,
                'environment': env_name
            })

        return errors

    def _check_unknown_commands(self, content: str) -> List[Dict[str, Any]]:
        """检查未知命令"""
        errors = []

        # 查找所有命令
        command_matches = self.command_pattern.finditer(content)

        for match in command_matches:
            command = match.group(1)
            if command not in self._known_commands:
                errors.append({
                    'type': 'unknown_command',
                    'message': f'未知命令: \\{command}',
                    'position': match.start(),
                    'command': command
                })

        return errors
