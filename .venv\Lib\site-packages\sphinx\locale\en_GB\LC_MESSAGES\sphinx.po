# Translations template for Sphinx.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <PERSON>, 2022-2023
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-10-10 15:47+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: <PERSON>, 2022-2023\n"
"Language-Team: English (United Kingdom) (http://app.transifex.com/sphinx-doc/sphinx-1/language/en_GB/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Language: en_GB\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: events.py:77
#, python-format
msgid "Event %r already present"
msgstr "Event %r already present"

#: events.py:370
#, python-format
msgid "Unknown event name: %s"
msgstr "Unknown event name: %s"

#: events.py:416
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr "Handler %r for event %r threw an exception"

#: application.py:186
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "Cannot find source directory (%s)"

#: application.py:190
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr "Output directory (%s) is not a directory"

#: application.py:194
msgid "Source directory and destination directory cannot be identical"
msgstr "Source directory and destination directory cannot be identical"

#: application.py:224
#, python-format
msgid "Running Sphinx v%s"
msgstr "Running Sphinx v%s"

#: application.py:246
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "This project needs at least Sphinx v%s and therefore cannot be built with this version."

#: application.py:262
msgid "making output directory"
msgstr "making output directory"

#: application.py:267 registry.py:450
#, python-format
msgid "while setting up extension %s:"
msgstr "while setting up extension %s:"

#: application.py:273
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "'setup' as currently defined in conf.py isn't a Python callable. Please modify its definition to make it a callable function. This is needed for conf.py to behave as a Sphinx extension."

#: application.py:308
#, python-format
msgid "loading translations [%s]... "
msgstr "loading translations [%s]... "

#: application.py:325 util/display.py:90
msgid "done"
msgstr "done"

#: application.py:327
msgid "not available for built-in messages"
msgstr "not available for built-in messages"

#: application.py:341
msgid "loading pickled environment"
msgstr "loading pickled environment"

#: application.py:349
#, python-format
msgid "failed: %s"
msgstr "failed: %s"

#: application.py:362
msgid "No builder selected, using default: html"
msgstr "No builder selected, using default: html"

#: application.py:394
msgid "build finished with problems."
msgstr ""

#: application.py:396
msgid "build succeeded."
msgstr ""

#: application.py:400
msgid ""
"build finished with problems, 1 warning (with warnings treated as errors)."
msgstr ""

#: application.py:403
msgid "build finished with problems, 1 warning."
msgstr ""

#: application.py:405
msgid "build succeeded, 1 warning."
msgstr ""

#: application.py:410
#, python-format
msgid ""
"build finished with problems, %s warnings (with warnings treated as errors)."
msgstr ""

#: application.py:413
#, python-format
msgid "build finished with problems, %s warnings."
msgstr ""

#: application.py:415
#, python-format
msgid "build succeeded, %s warnings."
msgstr ""

#: application.py:964
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "node class %r is already registered, its visitors will be overridden"

#: application.py:1043
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr "directive %r is already registered, it will be overridden"

#: application.py:1065 application.py:1090
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr "role %r is already registered, it will be overridden"

#: application.py:1640
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "the %s extension does not declare if it is safe for parallel reading, assuming it isn't - please ask the extension author to check and make it explicit"

#: application.py:1644
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "the %s extension is not safe for parallel reading"

#: application.py:1647
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "the %s extension does not declare if it is safe for parallel writing, assuming it isn't - please ask the extension author to check and make it explicit"

#: application.py:1651
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "the %s extension is not safe for parallel writing"

#: application.py:1659 application.py:1663
#, python-format
msgid "doing serial %s"
msgstr "doing serial %s"

#: roles.py:205
#, python-format
msgid "Common Vulnerabilities and Exposures; CVE %s"
msgstr ""

#: roles.py:228
#, python-format
msgid "invalid CVE number %s"
msgstr ""

#: roles.py:249
#, python-format
msgid "Common Weakness Enumeration; CWE %s"
msgstr ""

#: roles.py:272
#, python-format
msgid "invalid CWE number %s"
msgstr ""

#: roles.py:293
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "Python Enhancement Proposals; PEP %s"

#: roles.py:316
#, python-format
msgid "invalid PEP number %s"
msgstr "invalid PEP number %s"

#: roles.py:354
#, python-format
msgid "invalid RFC number %s"
msgstr "invalid RFC number %s"

#: registry.py:142
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "Builder class %s has no \"name\" attribute"

#: registry.py:144
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "Builder %r already exists (in module %s)"

#: registry.py:157
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "Builder name %s not registered or available through entry point"

#: registry.py:164
#, python-format
msgid "Builder name %s not registered"
msgstr "Builder name %s not registered"

#: registry.py:171
#, python-format
msgid "domain %s already registered"
msgstr "domain %s already registered"

#: registry.py:194 registry.py:207 registry.py:218
#, python-format
msgid "domain %s not yet registered"
msgstr "domain %s not yet registered"

#: registry.py:198
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "The %r directive is already registered to domain %s"

#: registry.py:210
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "The %r role is already registered to domain %s"

#: registry.py:221
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "The %r index is already registered to domain %s"

#: registry.py:252
#, python-format
msgid "The %r object_type is already registered"
msgstr "The %r object_type is already registered"

#: registry.py:278
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "The %r crossref_type is already registered"

#: registry.py:285
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r is already registered"

#: registry.py:294
#, python-format
msgid "source_parser for %r is already registered"
msgstr "source_parser for %r is already registered"

#: registry.py:302
#, python-format
msgid "Source parser for %s not registered"
msgstr "Source parser for %s not registered"

#: registry.py:318
#, python-format
msgid "Translator for %r already exists"
msgstr "Translator for %r already exists"

#: registry.py:334
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"

#: registry.py:417
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r already registered"

#: registry.py:429
#, python-format
msgid "math renderer %s is already registered"
msgstr "maths renderer %s is already registered"

#: registry.py:444
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "the extension %r was already merged with Sphinx since version %s; this extension is ignored."

#: registry.py:455
msgid "Original exception:\n"
msgstr "Original exception:\n"

#: registry.py:456
#, python-format
msgid "Could not import extension %s"
msgstr "Could not import extension %s"

#: registry.py:461
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "extension %r has no setup() function; is it really a Sphinx extension module?"

#: registry.py:470
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "The %s extension used by this project needs at least Sphinx v%s; it therefore cannot be built with this version."

#: registry.py:478
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "extension %r returned an unsupported object from its setup() function; it should return None or a metadata dictionary"

#: registry.py:512
#, python-format
msgid "`None` is not a valid filetype for %r."
msgstr ""

#: project.py:71
#, python-format
msgid ""
"multiple files found for the document \"%s\": %s\n"
"Use %r for the build."
msgstr ""

#: project.py:87
#, python-format
msgid "Ignored unreadable document %r."
msgstr ""

#: highlighting.py:168
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr "Pygments lexer name %r is not known"

#: highlighting.py:202
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr ""

#: extension.py:55
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "The %s extension is required by needs_extensions settings, but it is not loaded."

#: extension.py:76
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "This project needs the extension %s at least in version %s and therefore cannot be built with the loaded version (%s)."

#: theming.py:121
#, python-format
msgid ""
"Theme configuration sections other than [theme] and [options] are not "
"supported (tried to get a value from %r)."
msgstr ""

#: theming.py:127
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "setting %s.%s occurs in none of the searched theme configs"

#: theming.py:142
#, python-format
msgid "unsupported theme option %r given"
msgstr "unsupported theme option %r given"

#: theming.py:215
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "file %r on theme path is not a valid zipfile or contains no theme"

#: theming.py:236
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr ""

#: theming.py:276
#, python-format
msgid "The %r theme has circular inheritance"
msgstr ""

#: theming.py:283
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr ""

#: theming.py:290
#, python-format
msgid "The %r theme has too many ancestors"
msgstr ""

#: theming.py:318
#, python-format
msgid "no theme configuration file found in %r"
msgstr ""

#: theming.py:346 theming.py:399
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr ""

#: theming.py:350
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr ""

#: theming.py:354 theming.py:402
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr ""

#: theming.py:358
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr ""

#: theming.py:377
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr ""

#: config.py:314
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "config directory doesn't contain a conf.py file (%s)"

#: config.py:323
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr "Invalid configuration value found: 'language = None'. Update your configuration to a valid language code. Falling back to 'en' (English)."

#: config.py:346
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "cannot override dictionary config setting %r, ignoring (use %r to set individual elements)"

#: config.py:355
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "invalid number %r for config value %r, ignoring"

#: config.py:361
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "cannot override config setting %r with unsupported type, ignoring"

#: config.py:382
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "unknown config value %r in override, ignoring"

#: config.py:435
#, python-format
msgid "No such config value: %r"
msgstr ""

#: config.py:458
#, python-format
msgid "Config value %r already present"
msgstr "Config value %r already present"

#: config.py:494
#, python-format
msgid ""
"cannot cache unpickable configuration value: %r (because it contains a "
"function, class, or module object)"
msgstr ""

#: config.py:531
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "There is a syntax error in your configuration file: %s\n"

#: config.py:534
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "The configuration file (or one of the modules it imports) called sys.exit()"

#: config.py:541
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "There is a programmable error in your configuration file:\n\n%s"

#: config.py:564
#, python-format
msgid "Failed to convert %r to a set or tuple"
msgstr ""

#: config.py:585 config.py:590
#, python-format
msgid "Converting `source_suffix = %r` to `source_suffix = %r`."
msgstr ""

#: config.py:593
#, python-format
msgid ""
"The config value `source_suffix' expects a dictionary, a string, or a list "
"of strings. Got `%r' instead (type %s)."
msgstr ""

#: config.py:612
#, python-format
msgid "Section %s"
msgstr "Section %s"

#: config.py:613
#, python-format
msgid "Fig. %s"
msgstr "Fig. %s"

#: config.py:614
#, python-format
msgid "Table %s"
msgstr "Table %s"

#: config.py:615
#, python-format
msgid "Listing %s"
msgstr "Listing %s"

#: config.py:722
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "The config value `{name}` has to be a one of {candidates}, but `{current}` is given."

#: config.py:746
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "The config value `{name}' has type `{current.__name__}'; expected {permitted}."

#: config.py:759
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "The config value `{name}' has type `{current.__name__}', defaults to `{default.__name__}'."

#: config.py:770
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "primary_domain %r not found, ignored."

#: config.py:782
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr "Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add \"root_doc = 'contents'\" to your conf.py."

#: domains/rst.py:127 domains/rst.py:184
#, python-format
msgid "%s (directive)"
msgstr "%s (directive)"

#: domains/rst.py:185 domains/rst.py:189
#, python-format
msgid ":%s: (directive option)"
msgstr ":%s: (directive option)"

#: domains/rst.py:213
#, python-format
msgid "%s (role)"
msgstr "%s (role)"

#: domains/rst.py:223
msgid "directive"
msgstr "directive"

#: domains/rst.py:224
msgid "directive-option"
msgstr "directive-option"

#: domains/rst.py:225
msgid "role"
msgstr "role"

#: domains/rst.py:247
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr "duplicate description of %s %s, other instance in %s"

#: domains/javascript.py:165
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (built-in function)"

#: domains/javascript.py:166 domains/python/__init__.py:253
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (%s method)"

#: domains/javascript.py:168
#, python-format
msgid "%s() (class)"
msgstr "%s() (class)"

#: domains/javascript.py:170
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (global variable or constant)"

#: domains/javascript.py:172 domains/python/__init__.py:338
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (%s attribute)"

#: domains/javascript.py:255
msgid "Arguments"
msgstr "Arguments"

#: domains/cpp/__init__.py:442 domains/javascript.py:258
msgid "Throws"
msgstr "Throws"

#: domains/c/__init__.py:304 domains/cpp/__init__.py:453
#: domains/javascript.py:261 domains/python/_object.py:176
msgid "Returns"
msgstr "Returns"

#: domains/c/__init__.py:306 domains/javascript.py:263
#: domains/python/_object.py:178
msgid "Return type"
msgstr "Return type"

#: domains/javascript.py:334
#, python-format
msgid "%s (module)"
msgstr "%s (module)"

#: domains/c/__init__.py:675 domains/cpp/__init__.py:854
#: domains/javascript.py:371 domains/python/__init__.py:629
msgid "function"
msgstr "function"

#: domains/javascript.py:372 domains/python/__init__.py:633
msgid "method"
msgstr "method"

#: domains/cpp/__init__.py:852 domains/javascript.py:373
#: domains/python/__init__.py:631
msgid "class"
msgstr "class"

#: domains/javascript.py:374 domains/python/__init__.py:630
msgid "data"
msgstr "data"

#: domains/javascript.py:375 domains/python/__init__.py:636
msgid "attribute"
msgstr "attribute"

#: domains/javascript.py:376 domains/python/__init__.py:639
msgid "module"
msgstr "module"

#: domains/javascript.py:407
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr "duplicate %s description of %s, other %s in %s"

#: domains/changeset.py:25
#, python-format
msgid "Added in version %s"
msgstr ""

#: domains/changeset.py:26
#, python-format
msgid "Changed in version %s"
msgstr "Changed in version %s"

#: domains/changeset.py:27
#, python-format
msgid "Deprecated since version %s"
msgstr "Deprecated since version %s"

#: domains/changeset.py:28
#, python-format
msgid "Removed in version %s"
msgstr ""

#: domains/__init__.py:299
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: domains/citation.py:73
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr "duplicate citation %s, other instance in %s"

#: domains/citation.py:84
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "Citation [%s] is not referenced."

#: domains/math.py:63
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "duplicate label of equation %s, other instance in %s"

#: domains/math.py:119 writers/latex.py:2479
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "Invalid math_eqref_format: %r"

#: environment/__init__.py:86
msgid "new config"
msgstr "new config"

#: environment/__init__.py:87
msgid "config changed"
msgstr "config changed"

#: environment/__init__.py:88
msgid "extensions changed"
msgstr "extensions changed"

#: environment/__init__.py:249
msgid "build environment version not current"
msgstr "build environment version not current"

#: environment/__init__.py:251
msgid "source directory has changed"
msgstr "source directory has changed"

#: environment/__init__.py:311
#, python-format
msgid "The configuration has changed (1 option: %r)"
msgstr ""

#: environment/__init__.py:316
#, python-format
msgid "The configuration has changed (%d options: %s)"
msgstr ""

#: environment/__init__.py:322
#, python-format
msgid "The configuration has changed (%d options: %s, ...)"
msgstr ""

#: environment/__init__.py:364
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "This environment is incompatible with the selected builder, please choose another doctree directory."

#: environment/__init__.py:473
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "Failed to scan documents in %s: %r"

#: environment/__init__.py:622
#, python-format
msgid "Domain %r is not registered"
msgstr "Domain %r is not registered"

#: environment/__init__.py:773
msgid "document isn't included in any toctree"
msgstr "document isn't included in any toctree"

#: environment/__init__.py:806
msgid "self referenced toctree found. Ignored."
msgstr "self referenced toctree found. Ignored."

#: environment/__init__.py:835
#, python-format
msgid "document is referenced in multiple toctrees: %s, selecting: %s <- %s"
msgstr ""

#: locale/__init__.py:229
msgid "Attention"
msgstr "Attention"

#: locale/__init__.py:230
msgid "Caution"
msgstr "Caution"

#: locale/__init__.py:231
msgid "Danger"
msgstr "Danger"

#: locale/__init__.py:232
msgid "Error"
msgstr "Error"

#: locale/__init__.py:233
msgid "Hint"
msgstr "Hint"

#: locale/__init__.py:234
msgid "Important"
msgstr "Important"

#: locale/__init__.py:235
msgid "Note"
msgstr "Note"

#: locale/__init__.py:236
msgid "See also"
msgstr "See also"

#: locale/__init__.py:237
msgid "Tip"
msgstr "Tip"

#: locale/__init__.py:238
msgid "Warning"
msgstr "Warning"

#: cmd/quickstart.py:43
msgid "automatically insert docstrings from modules"
msgstr "automatically insert docstrings from modules"

#: cmd/quickstart.py:44
msgid "automatically test code snippets in doctest blocks"
msgstr "automatically test code snippets in doctest blocks"

#: cmd/quickstart.py:45
msgid "link between Sphinx documentation of different projects"
msgstr "link between Sphinx documentation of different projects"

#: cmd/quickstart.py:46
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr "write \"todo\" entries that can be shown or hidden on build"

#: cmd/quickstart.py:47
msgid "checks for documentation coverage"
msgstr "checks for documentation coverage"

#: cmd/quickstart.py:48
msgid "include math, rendered as PNG or SVG images"
msgstr "include maths, rendered as PNG or SVG images"

#: cmd/quickstart.py:49
msgid "include math, rendered in the browser by MathJax"
msgstr "include maths, rendered in the browser by MathJax"

#: cmd/quickstart.py:50
msgid "conditional inclusion of content based on config values"
msgstr "conditional inclusion of content based on config values"

#: cmd/quickstart.py:51
msgid "include links to the source code of documented Python objects"
msgstr "include links to the source code of documented Python objects"

#: cmd/quickstart.py:52
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr "create .nojekyll file to publish the document on GitHub pages"

#: cmd/quickstart.py:94
msgid "Please enter a valid path name."
msgstr "Please enter a valid path name."

#: cmd/quickstart.py:110
msgid "Please enter some text."
msgstr "Please enter some text."

#: cmd/quickstart.py:117
#, python-format
msgid "Please enter one of %s."
msgstr "Please enter one of %s."

#: cmd/quickstart.py:125
msgid "Please enter either 'y' or 'n'."
msgstr "Please enter either 'y' or 'n'."

#: cmd/quickstart.py:131
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "Please enter a file suffix, e.g. '.rst' or '.txt'."

#: cmd/quickstart.py:213
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "Welcome to the Sphinx %s quickstart utility."

#: cmd/quickstart.py:217
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr "Please enter values for the following settings (just press Enter to\naccept a default value, if one is given in brackets)."

#: cmd/quickstart.py:225
#, python-format
msgid "Selected root path: %s"
msgstr "Selected root path: %s"

#: cmd/quickstart.py:228
msgid "Enter the root path for documentation."
msgstr "Enter the root path for documentation."

#: cmd/quickstart.py:229
msgid "Root path for the documentation"
msgstr "Root path for the documentation"

#: cmd/quickstart.py:237
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "Error: an existing conf.py has been found in the selected root path."

#: cmd/quickstart.py:243
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "sphinx-quickstart will not overwrite existing Sphinx projects."

#: cmd/quickstart.py:246
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "Please enter a new root path (or just Enter to exit)"

#: cmd/quickstart.py:256
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr "You have two options for placing the build directory for Sphinx output.\nEither, you use a directory \"_build\" within the root path, or you separate\n\"source\" and \"build\" directories within the root path."

#: cmd/quickstart.py:263
msgid "Separate source and build directories (y/n)"
msgstr "Separate source and build directories (y/n)"

#: cmd/quickstart.py:269
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr "Inside the root directory, two more directories will be created; \"_templates\"\nfor custom HTML templates and \"_static\" for custom stylesheets and other static\nfiles. You can enter another prefix (such as \".\") to replace the underscore."

#: cmd/quickstart.py:275
msgid "Name prefix for templates and static dir"
msgstr "Name prefix for templates and static dir"

#: cmd/quickstart.py:280
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "The project name will occur in several places in the built documentation."

#: cmd/quickstart.py:284
msgid "Project name"
msgstr "Project name"

#: cmd/quickstart.py:286
msgid "Author name(s)"
msgstr "Author name(s)"

#: cmd/quickstart.py:291
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr "Sphinx has the notion of a \"version\" and a \"release\" for the\nsoftware. Each version can have multiple releases. For example, for\nPython the version is something like 2.5 or 3.0, while the release is\nsomething like 2.5.1 or 3.0a1. If you don't need this dual structure,\njust set both to the same value."

#: cmd/quickstart.py:299
msgid "Project version"
msgstr "Project version"

#: cmd/quickstart.py:301
msgid "Project release"
msgstr "Project release"

#: cmd/quickstart.py:306
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr "If the documents are to be written in a language other than English,\nyou can select a language here by its language code. Sphinx will then\ntranslate text that it generates into that language.\n\nFor a list of supported codes, see\nhttps://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."

#: cmd/quickstart.py:315
msgid "Project language"
msgstr "Project language"

#: cmd/quickstart.py:322
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr "The file name suffix for source files. Commonly, this is either \".txt\"\nor \".rst\". Only files with this suffix are considered documents."

#: cmd/quickstart.py:327
msgid "Source file suffix"
msgstr "Source file suffix"

#: cmd/quickstart.py:332
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr "One document is special in that it is considered the top node of the\n\"contents tree\", that is, it is the root of the hierarchical structure\nof the documents. Normally, this is \"index\", but if your \"index\"\ndocument is a custom template, you can also set this to another filename."

#: cmd/quickstart.py:340
msgid "Name of your master document (without suffix)"
msgstr "Name of your master document (without suffix)"

#: cmd/quickstart.py:350
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr "Error: the master file %s has already been found in the selected root path."

#: cmd/quickstart.py:357
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "sphinx-quickstart will not overwrite the existing file."

#: cmd/quickstart.py:360
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr "Please enter a new file name, or rename the existing file and press Enter"

#: cmd/quickstart.py:369
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "Indicate which of the following Sphinx extensions should be enabled:"

#: cmd/quickstart.py:379
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr "Note: imgmath and mathjax cannot be enabled at the same time. imgmath has been deselected."

#: cmd/quickstart.py:389
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr "A Makefile and a Windows command file can be generated for you so that you\nonly have to run e.g. `make html' instead of invoking sphinx-build\ndirectly."

#: cmd/quickstart.py:395
msgid "Create Makefile? (y/n)"
msgstr "Create Makefile? (y/n)"

#: cmd/quickstart.py:399
msgid "Create Windows command file? (y/n)"
msgstr "Create Windows command file? (y/n)"

#: cmd/quickstart.py:451 ext/apidoc.py:92
#, python-format
msgid "Creating file %s."
msgstr "Creating file %s."

#: cmd/quickstart.py:456 ext/apidoc.py:89
#, python-format
msgid "File %s already exists, skipping."
msgstr "File %s already exists, skipping."

#: cmd/quickstart.py:499
msgid "Finished: An initial directory structure has been created."
msgstr "Finished: An initial directory structure has been created."

#: cmd/quickstart.py:502
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr "You should now populate your master file %s and create other documentation\nsource files. "

#: cmd/quickstart.py:510
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr "Use the Makefile to build the docs, like so:\n   make builder"

#: cmd/quickstart.py:513
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr "Use the sphinx-build command to build the docs, like so:\n   sphinx-build -b builder %s %s"

#: cmd/quickstart.py:520
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr "where \"builder\" is one of the supported builders, e.g. html, latex or linkcheck."

#: cmd/quickstart.py:555
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr "\nGenerate required files for a Sphinx project.\n\nsphinx-quickstart is an interactive tool that asks some questions about your\nproject and then generates a complete documentation directory and sample\nMakefile to be used with sphinx-build.\n"

#: cmd/build.py:153 cmd/quickstart.py:565 ext/apidoc.py:374
#: ext/autosummary/generate.py:765
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr "For more information, visit <https://www.sphinx-doc.org/>."

#: cmd/quickstart.py:575
msgid "quiet mode"
msgstr "quiet mode"

#: cmd/quickstart.py:585
msgid "project root"
msgstr "project root"

#: cmd/quickstart.py:588
msgid "Structure options"
msgstr "Structure options"

#: cmd/quickstart.py:594
msgid "if specified, separate source and build dirs"
msgstr "if specified, separate source and build dirs"

#: cmd/quickstart.py:600
msgid "if specified, create build dir under source dir"
msgstr "if specified, create build dir under source dir"

#: cmd/quickstart.py:606
msgid "replacement for dot in _templates etc."
msgstr "replacement for dot in _templates etc."

#: cmd/quickstart.py:609
msgid "Project basic options"
msgstr "Project basic options"

#: cmd/quickstart.py:611
msgid "project name"
msgstr "project name"

#: cmd/quickstart.py:614
msgid "author names"
msgstr "author names"

#: cmd/quickstart.py:621
msgid "version of project"
msgstr "version of project"

#: cmd/quickstart.py:628
msgid "release of project"
msgstr "release of project"

#: cmd/quickstart.py:635
msgid "document language"
msgstr "document language"

#: cmd/quickstart.py:638
msgid "source file suffix"
msgstr "source file suffix"

#: cmd/quickstart.py:641
msgid "master document name"
msgstr "master document name"

#: cmd/quickstart.py:644
msgid "use epub"
msgstr "use epub"

#: cmd/quickstart.py:647
msgid "Extension options"
msgstr "Extension options"

#: cmd/quickstart.py:654 ext/apidoc.py:578
#, python-format
msgid "enable %s extension"
msgstr "enable %s extension"

#: cmd/quickstart.py:661 ext/apidoc.py:570
msgid "enable arbitrary extensions"
msgstr "enable arbitrary extensions"

#: cmd/quickstart.py:664
msgid "Makefile and Batchfile creation"
msgstr "Makefile and Batchfile creation"

#: cmd/quickstart.py:670
msgid "create makefile"
msgstr "create makefile"

#: cmd/quickstart.py:676
msgid "do not create makefile"
msgstr "do not create makefile"

#: cmd/quickstart.py:683
msgid "create batchfile"
msgstr "create batchfile"

#: cmd/quickstart.py:689
msgid "do not create batchfile"
msgstr "do not create batchfile"

#: cmd/quickstart.py:698
msgid "use make-mode for Makefile/make.bat"
msgstr "use make-mode for Makefile/make.bat"

#: cmd/quickstart.py:701 ext/apidoc.py:581
msgid "Project templating"
msgstr "Project templating"

#: cmd/quickstart.py:707 ext/apidoc.py:587
msgid "template directory for template files"
msgstr "template directory for template files"

#: cmd/quickstart.py:714
msgid "define a template variable"
msgstr "define a template variable"

#: cmd/quickstart.py:749
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."

#: cmd/quickstart.py:768
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr "Error: specified path is not a directory, or sphinx files already exist."

#: cmd/quickstart.py:775
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr "sphinx-quickstart only generate into a empty directory. Please specify a new root path."

#: cmd/quickstart.py:793
#, python-format
msgid "Invalid template variable: %s"
msgstr "Invalid template variable: %s"

#: cmd/build.py:49
msgid "Exception occurred while building, starting debugger:"
msgstr "Exception occurred while building, starting debugger:"

#: _cli/util/errors.py:129 cmd/build.py:65
msgid "Interrupted!"
msgstr "Interrupted!"

#: cmd/build.py:67
msgid "reST markup error:"
msgstr "reST markup error:"

#: _cli/util/errors.py:143 cmd/build.py:73
msgid "Encoding error:"
msgstr "Encoding error:"

#: cmd/build.py:78 cmd/build.py:108
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr "The full traceback has been saved in %s, if you want to report the issue to the developers."

#: _cli/util/errors.py:148 cmd/build.py:90
msgid "Recursion error:"
msgstr "Recursion error:"

#: _cli/util/errors.py:152 cmd/build.py:94
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr "This can happen with very large or deeply nested source files. You can carefully increase the default Python recursion limit of 1000 in conf.py with e.g.:"

#: _cli/util/errors.py:165 cmd/build.py:103
msgid "Exception occurred:"
msgstr "Exception occurred:"

#: _cli/util/errors.py:178 cmd/build.py:117
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr "Please also report this if it was a user error, so that a better error message can be provided next time."

#: cmd/build.py:124
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr "A bug report can be filed in the tracker at <https://github.com/sphinx-doc/sphinx/issues>. Thanks!"

#: cmd/build.py:144
msgid "job number should be a positive number"
msgstr "job number should be a positive number"

#: cmd/build.py:154
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr "\nGenerate documentation from source files.\n\nsphinx-build generates documentation from the files in SOURCEDIR and places it\nin OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\nsettings. The 'sphinx-quickstart' tool may be used to generate template files,\nincluding 'conf.py'\n\nsphinx-build can create documentation in different formats. A format is\nselected by specifying the builder name on the command line; it defaults to\nHTML. Builders can also perform other tasks related to documentation\nprocessing.\n\nBy default, everything that is outdated is built. Output only for selected\nfiles can be built by specifying individual filenames.\n"

#: cmd/build.py:180
msgid "path to documentation source files"
msgstr "path to documentation source files"

#: cmd/build.py:183
msgid "path to output directory"
msgstr "path to output directory"

#: cmd/build.py:188
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr ""

#: cmd/build.py:194
msgid "general options"
msgstr "general options"

#: cmd/build.py:201
msgid "builder to use (default: 'html')"
msgstr ""

#: cmd/build.py:210
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: cmd/build.py:220
msgid "write all files (default: only write new and changed files)"
msgstr "write all files (default: only write new and changed files)"

#: cmd/build.py:227
msgid "don't use a saved environment, always read all files"
msgstr "don't use a saved environment, always read all files"

#: cmd/build.py:230
msgid "path options"
msgstr ""

#: cmd/build.py:236
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr ""

#: cmd/build.py:246
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr ""

#: cmd/build.py:257
msgid "use no configuration file, only use settings from -D options"
msgstr ""

#: cmd/build.py:266
msgid "override a setting in configuration file"
msgstr "override a setting in configuration file"

#: cmd/build.py:275
msgid "pass a value into HTML templates"
msgstr "pass a value into HTML templates"

#: cmd/build.py:284
msgid "define tag: include \"only\" blocks with TAG"
msgstr "define tag: include \"only\" blocks with TAG"

#: cmd/build.py:291
msgid "nitpicky mode: warn about all missing references"
msgstr ""

#: cmd/build.py:294
msgid "console output options"
msgstr "console output options"

#: cmd/build.py:301
msgid "increase verbosity (can be repeated)"
msgstr "increase verbosity (can be repeated)"

#: cmd/build.py:308 ext/apidoc.py:413
msgid "no output on stdout, just warnings on stderr"
msgstr "no output on stdout, just warnings on stderr"

#: cmd/build.py:315
msgid "no output at all, not even warnings"
msgstr "no output at all, not even warnings"

#: cmd/build.py:323
msgid "do emit colored output (default: auto-detect)"
msgstr "do emit colored output (default: auto-detect)"

#: cmd/build.py:331
msgid "do not emit colored output (default: auto-detect)"
msgstr "do not emit coloured output (default: auto-detect)"

#: cmd/build.py:334
msgid "warning control options"
msgstr ""

#: cmd/build.py:340
msgid "write warnings (and errors) to given file"
msgstr "write warnings (and errors) to given file"

#: cmd/build.py:347
msgid "turn warnings into errors"
msgstr "turn warnings into errors"

#: cmd/build.py:355
msgid "show full traceback on exception"
msgstr "show full traceback on exception"

#: cmd/build.py:358
msgid "run Pdb on exception"
msgstr "run Pdb on exception"

#: cmd/build.py:364
msgid "raise an exception on warnings"
msgstr ""

#: cmd/build.py:407
msgid "cannot combine -a option and filenames"
msgstr "cannot combine -a option and filenames"

#: cmd/build.py:439
#, python-format
msgid "cannot open warning file %r: %s"
msgstr "cannot open warning file %r: %s"

#: cmd/build.py:458
msgid "-D option argument must be in the form name=value"
msgstr "-D option argument must be in the form name=value"

#: cmd/build.py:465
msgid "-A option argument must be in the form name=value"
msgstr "-A option argument must be in the form name=value"

#: builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "The dummy builder generates no files."

#: builders/linkcheck.py:60
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr "Look for any errors in the above output or in %(outdir)s/output.txt"

#: builders/linkcheck.py:137
#, python-format
msgid "broken link: %s (%s)"
msgstr "broken link: %s (%s)"

#: builders/linkcheck.py:526
#, python-format
msgid "Anchor '%s' not found"
msgstr "Anchor '%s' not found"

#: builders/linkcheck.py:726
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr "Failed to compile regex in linkcheck_allowed_redirects: %r %s"

#: builders/singlehtml.py:36
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "The HTML page is in %(outdir)s."

#: builders/singlehtml.py:168
msgid "assembling single document"
msgstr "assembling single document"

#: builders/latex/__init__.py:349 builders/manpage.py:59
#: builders/singlehtml.py:173 builders/texinfo.py:120
msgid "writing"
msgstr "writing"

#: builders/singlehtml.py:186
msgid "writing additional files"
msgstr "writing additional files"

#: builders/manpage.py:39
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "The manual pages are in %(outdir)s."

#: builders/manpage.py:47
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr "no \"man_pages\" config value found; no manual pages will be written"

#: builders/manpage.py:76
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "\"man_pages\" config value references unknown document %s"

#: builders/text.py:34
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "The text files are in %(outdir)s."

#: builders/html/__init__.py:1213 builders/text.py:81 builders/xml.py:97
#, python-format
msgid "error writing file %s: %s"
msgstr "error writing file %s: %s"

#: builders/xml.py:38
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "The XML files are in %(outdir)s."

#: builders/xml.py:110
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "The pseudo-XML files are in %(outdir)s."

#: builders/texinfo.py:47
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "The Texinfo files are in %(outdir)s."

#: builders/texinfo.py:49
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr "\nRun 'make' in that directory to run these through makeinfo\n(use 'make info' here to do that automatically)."

#: builders/texinfo.py:78
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "no \"texinfo_documents\" config value found; no documents will be written"

#: builders/texinfo.py:90
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "\"texinfo_documents\" config value references unknown document %s"

#: builders/latex/__init__.py:327 builders/texinfo.py:114
#, python-format
msgid "processing %s"
msgstr "processing %s"

#: builders/latex/__init__.py:407 builders/texinfo.py:173
msgid "resolving references..."
msgstr "resolving references..."

#: builders/latex/__init__.py:418 builders/texinfo.py:183
msgid " (in "
msgstr " (in "

#: builders/_epub_base.py:421 builders/html/__init__.py:757
#: builders/latex/__init__.py:485 builders/texinfo.py:201
msgid "copying images... "
msgstr "copying images... "

#: builders/_epub_base.py:443 builders/latex/__init__.py:500
#: builders/texinfo.py:218
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "cannot copy image file %r: %s"

#: builders/texinfo.py:225
msgid "copying Texinfo support files"
msgstr "copying Texinfo support files"

#: builders/texinfo.py:233
#, python-format
msgid "error writing file Makefile: %s"
msgstr "error writing file Makefile: %s"

#: builders/gettext.py:230
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "The message catalogues are in %(outdir)s."

#: builders/__init__.py:371 builders/gettext.py:251
#, python-format
msgid "building [%s]: "
msgstr "building [%s]: "

#: builders/gettext.py:252
#, python-format
msgid "targets for %d template files"
msgstr "targets for %d template files"

#: builders/gettext.py:257
msgid "reading templates... "
msgstr "reading templates... "

#: builders/gettext.py:292
msgid "writing message catalogs... "
msgstr "writing message catalogues... "

#: builders/__init__.py:200
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr "a suitable image for %s builder not found: %s (%s)"

#: builders/__init__.py:208
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr "a suitable image for %s builder not found: %s"

#: builders/__init__.py:231
msgid "building [mo]: "
msgstr "building [mo]: "

#: builders/__init__.py:234 builders/__init__.py:729 builders/__init__.py:761
msgid "writing output... "
msgstr "writing output... "

#: builders/__init__.py:251
#, python-format
msgid "all of %d po files"
msgstr "all of %d po files"

#: builders/__init__.py:273
#, python-format
msgid "targets for %d po files that are specified"
msgstr "targets for %d po files that are specified"

#: builders/__init__.py:285
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "targets for %d po files that are out of date"

#: builders/__init__.py:295
msgid "all source files"
msgstr "all source files"

#: builders/__init__.py:307
#, python-format
msgid "file %r given on command line does not exist, "
msgstr "file %r given on command line does not exist, "

#: builders/__init__.py:313
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "file %r given on command line is not under the source directory, ignoring"

#: builders/__init__.py:324
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr "file %r given on command line is not a valid document, ignoring"

#: builders/__init__.py:339
#, python-format
msgid "%d source files given on command line"
msgstr "%d source files given on command line"

#: builders/__init__.py:354
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "targets for %d source files that are out of date"

#: builders/__init__.py:382
msgid "looking for now-outdated files... "
msgstr "looking for now-outdated files... "

#: builders/__init__.py:386
#, python-format
msgid "%d found"
msgstr "%d found"

#: builders/__init__.py:388
msgid "none found"
msgstr "none found"

#: builders/__init__.py:395
msgid "pickling environment"
msgstr "pickling environment"

#: builders/__init__.py:402
msgid "checking consistency"
msgstr "checking consistency"

#: builders/__init__.py:406
msgid "no targets are out of date."
msgstr "no targets are out of date."

#: builders/__init__.py:446
msgid "updating environment: "
msgstr "updating environment: "

#: builders/__init__.py:471
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%s added, %s changed, %s removed"

#: builders/__init__.py:507
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches a "
"built-in exclude pattern %r. Please move your master document to a different"
" location."
msgstr ""

#: builders/__init__.py:516
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches an "
"exclude pattern specified in conf.py, %r. Please remove this pattern from "
"conf.py."
msgstr ""

#: builders/__init__.py:527
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it is not included"
" in the custom include_patterns = %r. Ensure that a pattern in "
"include_patterns matches the master document."
msgstr ""

#: builders/__init__.py:534
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s). The master document must "
"be within the source directory or a subdirectory of it."
msgstr ""

#: builders/__init__.py:553 builders/__init__.py:569
msgid "reading sources... "
msgstr "reading sources... "

#: builders/__init__.py:686
#, python-format
msgid "docnames to write: %s"
msgstr "docnames to write: %s"

#: builders/__init__.py:699
msgid "preparing documents"
msgstr "preparing documents"

#: builders/__init__.py:702
msgid "copying assets"
msgstr "copying assets"

#: builders/__init__.py:845
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr "undecodable source characters, replacing with \"?\": %r"

#: builders/epub3.py:83
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "The ePub file is in %(outdir)s."

#: builders/epub3.py:189
msgid "writing nav.xhtml file..."
msgstr "writing nav.xhtml file..."

#: builders/epub3.py:220
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"

#: builders/epub3.py:227
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "conf value \"epub_uid\" should be XML NAME for EPUB3"

#: builders/epub3.py:231
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"

#: builders/epub3.py:238
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "conf value \"epub_author\" should not be empty for EPUB3"

#: builders/epub3.py:242
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "conf value \"epub_contributor\" should not be empty for EPUB3"

#: builders/epub3.py:247
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "conf value \"epub_description\" should not be empty for EPUB3"

#: builders/epub3.py:251
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "conf value \"epub_publisher\" should not be empty for EPUB3"

#: builders/epub3.py:255
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"

#: builders/epub3.py:262
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "conf value \"epub_identifier\" should not be empty for EPUB3"

#: builders/epub3.py:265
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "conf value \"version\" should not be empty for EPUB3"

#: builders/epub3.py:279 builders/html/__init__.py:1262
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "invalid css_file: %r, ignored"

#: builders/_epub_base.py:220
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "duplicated ToC entry found: %s"

#: builders/_epub_base.py:432
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "cannot read image file %r: copying it instead"

#: builders/_epub_base.py:463
#, python-format
msgid "cannot write image file %r: %s"
msgstr "cannot write image file %r: %s"

#: builders/_epub_base.py:475
msgid "Pillow not found - copying image files"
msgstr "Pillow not found - copying image files"

#: builders/_epub_base.py:507
msgid "writing mimetype file..."
msgstr "writing mimetype file..."

#: builders/_epub_base.py:516
msgid "writing META-INF/container.xml file..."
msgstr "writing META-INF/container.xml file..."

#: builders/_epub_base.py:553
msgid "writing content.opf file..."
msgstr "writing content.opf file..."

#: builders/_epub_base.py:585
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "unknown mimetype for %s, ignoring"

#: builders/_epub_base.py:756
msgid "writing toc.ncx file..."
msgstr "writing toc.ncx file..."

#: builders/_epub_base.py:785
#, python-format
msgid "writing %s file..."
msgstr "writing %s file..."

#: builders/changes.py:33
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "The overview file is in %(outdir)s."

#: builders/changes.py:60
#, python-format
msgid "no changes in version %s."
msgstr "no changes in version %s."

#: builders/changes.py:62
msgid "writing summary file..."
msgstr "writing summary file..."

#: builders/changes.py:77
msgid "Builtins"
msgstr "Builtins"

#: builders/changes.py:79
msgid "Module level"
msgstr "Module level"

#: builders/changes.py:131
msgid "copying source files..."
msgstr "copying source files..."

#: builders/changes.py:140
#, python-format
msgid "could not read %r for changelog creation"
msgstr "could not read %r for changelog creation"

#: util/rst.py:72
#, python-format
msgid "default role %s not found"
msgstr "default role %s not found"

#: util/docfields.py:95
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr "Problem in %s domain: field is supposed to use role '%s', but that role is not in the domain."

#: util/osutil.py:130
#, python-format
msgid ""
"Aborted attempted copy from %s to %s (the destination path has existing "
"data)."
msgstr ""

#: util/nodes.py:419
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr "%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."

#: util/nodes.py:487
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr "toctree contains ref to nonexisting file %r"

#: util/nodes.py:701
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr "exception while evaluating only directive expression: %s"

#: util/fileutil.py:76
#, python-format
msgid ""
"Aborted attempted copy from rendered template %s to %s (the destination path"
" has existing data)."
msgstr ""

#: util/fileutil.py:91
#, python-format
msgid "Writing evaluated template result to %s"
msgstr ""

#: util/inventory.py:170
#, python-format
msgid "inventory <%s> contains duplicate definitions of %s"
msgstr ""

#: util/inventory.py:185
#, python-format
msgid "inventory <%s> contains multiple definitions for %s"
msgstr ""

#: util/docutils.py:283
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr "unknown directive or role name: %s:%s"

#: util/docutils.py:746
#, python-format
msgid "unknown node type: %r"
msgstr "unknown node type: %r"

#: util/display.py:83
msgid "skipped"
msgstr "skipped"

#: util/display.py:88
msgid "failed"
msgstr "failed"

#: util/i18n.py:105
#, python-format
msgid "reading error: %s, %s"
msgstr "reading error: %s, %s"

#: util/i18n.py:112
#, python-format
msgid "writing error: %s, %s"
msgstr "writing error: %s, %s"

#: util/i18n.py:141
#, python-format
msgid "locale_dir %s does not exist"
msgstr ""

#: util/i18n.py:236
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr "Invalid date format. Quote the string by single quote if you want to output it directly: %s"

#: directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr "\":file:\" option for csv-table directive now recognises an absolute path as a relative path from source directory. Please update your document."

#: directives/code.py:63
msgid "non-whitespace stripped by dedent"
msgstr "non-whitespace stripped by dedent"

#: directives/code.py:84
#, python-format
msgid "Invalid caption: %s"
msgstr "Invalid caption: %s"

#: directives/code.py:129 directives/code.py:291 directives/code.py:478
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr "line number spec is out of range(1-%d): %r"

#: directives/code.py:211
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "Cannot use both \"%s\" and \"%s\" options"

#: directives/code.py:225
#, python-format
msgid "Include file %r not found or reading it failed"
msgstr "Include file %r not found or reading it failed"

#: directives/code.py:228
#, python-format
msgid ""
"Encoding %r used for reading included file %r seems to be wrong, try giving "
"an :encoding: option"
msgstr "Encoding %r used for reading included file %r seems to be wrong, try giving an :encoding: option"

#: directives/code.py:270
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "Object named %r not found in include file %r"

#: directives/code.py:303
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr "Cannot use \"lineno-match\" with a disjoint set of \"lines\""

#: directives/code.py:308
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr "Line spec %r: no lines pulled from include file %r"

#: directives/other.py:122
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr "toctree glob pattern %r didn't match any documents"

#: directives/other.py:155 environment/adapters/toctree.py:355
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr "toctree contains reference to excluded document %r"

#: directives/other.py:158 environment/adapters/toctree.py:359
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr "toctree contains reference to nonexisting document %r"

#: directives/other.py:171
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr "duplicated entry found in toctree: %s"

#: directives/other.py:204
msgid "Section author: "
msgstr "Section author: "

#: directives/other.py:206
msgid "Module author: "
msgstr "Module author: "

#: directives/other.py:208
msgid "Code author: "
msgstr "Code author: "

#: directives/other.py:210
msgid "Author: "
msgstr "Author: "

#: directives/other.py:284
msgid ".. acks content is not a list"
msgstr ".. acks content is not a list"

#: directives/other.py:309
msgid ".. hlist content is not a list"
msgstr ".. hlist content is not a list"

#: _cli/__init__.py:73
msgid "Usage:"
msgstr ""

#: _cli/__init__.py:75
msgid "{0} [OPTIONS] <COMMAND> [<ARGS>]"
msgstr ""

#: _cli/__init__.py:78
msgid "  The Sphinx documentation generator."
msgstr ""

#: _cli/__init__.py:87
msgid "Commands:"
msgstr ""

#: _cli/__init__.py:98
msgid "Options"
msgstr ""

#: _cli/__init__.py:112 _cli/__init__.py:183
msgid "For more information, visit https://www.sphinx-doc.org/en/master/man/."
msgstr ""

#: _cli/__init__.py:172
msgid ""
"{0}: error: {1}\n"
"Run '{0} --help' for information"
msgstr ""

#: _cli/__init__.py:182
msgid "   Manage documentation with Sphinx."
msgstr ""

#: _cli/__init__.py:194
msgid "Show the version and exit."
msgstr ""

#: _cli/__init__.py:202
msgid "Show this message and exit."
msgstr ""

#: _cli/__init__.py:206
msgid "Logging"
msgstr ""

#: _cli/__init__.py:213
msgid "Increase verbosity (can be repeated)"
msgstr ""

#: _cli/__init__.py:221
msgid "Only print errors and warnings."
msgstr ""

#: _cli/__init__.py:228
msgid "No output at all"
msgstr ""

#: _cli/__init__.py:234
msgid "<command>"
msgstr ""

#: _cli/__init__.py:265
msgid "See 'sphinx --help'.\n"
msgstr ""

#: builders/html/__init__.py:478 builders/latex/__init__.py:201
#: transforms/__init__.py:133 writers/manpage.py:101 writers/texinfo.py:218
#, python-format
msgid "%b %d, %Y"
msgstr "%b %d, %Y"

#: transforms/__init__.py:143
msgid "could not calculate translation progress!"
msgstr ""

#: transforms/__init__.py:148
msgid "no translated elements!"
msgstr ""

#: transforms/__init__.py:267
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr "4 column based index found. It might be a bug of extensions you use: %r"

#: transforms/__init__.py:313
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr "Footnote [%s] is not referenced."

#: transforms/__init__.py:322
msgid "Footnote [*] is not referenced."
msgstr ""

#: transforms/__init__.py:333
msgid "Footnote [#] is not referenced."
msgstr "Footnote [#] is not referenced."

#: transforms/i18n.py:229 transforms/i18n.py:304
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr "inconsistent footnote references in translated message. original: {0}, translated: {1}"

#: transforms/i18n.py:274
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr "inconsistent references in translated message. original: {0}, translated: {1}"

#: transforms/i18n.py:324
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr "inconsistent citation references in translated message. original: {0}, translated: {1}"

#: transforms/i18n.py:346
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr "inconsistent term references in translated message. original: {0}, translated: {1}"

#: ext/linkcode.py:75 ext/viewcode.py:200
msgid "[source]"
msgstr "[source]"

#: ext/imgconverter.py:40
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr "Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n\nTraceback: %s"

#: ext/imgconverter.py:49 ext/imgconverter.py:73
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "convert exited with error:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/imgconverter.py:68
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr "convert command %r cannot be run, check the image_converter setting"

#: ext/viewcode.py:257
msgid "highlighting module code... "
msgstr "highlighting module code... "

#: ext/viewcode.py:285
msgid "[docs]"
msgstr "[docs]"

#: ext/viewcode.py:305
msgid "Module code"
msgstr "Module code"

#: ext/viewcode.py:311
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>Source code for %s</h1>"

#: ext/viewcode.py:337
msgid "Overview: module code"
msgstr "Overview: module code"

#: ext/viewcode.py:338
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>All modules for which code is available</h1>"

#: ext/coverage.py:47
#, python-format
msgid "invalid regex %r in %s"
msgstr "invalid regex %r in %s"

#: ext/coverage.py:134 ext/coverage.py:280
#, python-format
msgid "module %s could not be imported: %s"
msgstr "module %s could not be imported: %s"

#: ext/coverage.py:141
#, python-format
msgid ""
"the following modules are documented but were not specified in "
"coverage_modules: %s"
msgstr ""

#: ext/coverage.py:149
msgid ""
"the following modules are specified in coverage_modules but were not "
"documented"
msgstr ""

#: ext/coverage.py:163
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr "Testing of coverage in the sources finished, look at the results in %(outdir)spython.txt."

#: ext/coverage.py:177
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr "invalid regex %r in coverage_c_regexes"

#: ext/coverage.py:245
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr "undocumented c api: %s [%s] in file %s"

#: ext/coverage.py:429
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr "undocumented python function: %s :: %s"

#: ext/coverage.py:445
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr "undocumented python class: %s :: %s"

#: ext/coverage.py:458
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr "undocumented python method: %s :: %s :: %s"

#: ext/todo.py:71
msgid "Todo"
msgstr "Todo"

#: ext/todo.py:104
#, python-format
msgid "TODO entry found: %s"
msgstr "TODO entry found: %s"

#: ext/todo.py:163
msgid "<<original entry>>"
msgstr "<<original entry>>"

#: ext/todo.py:165
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(The <<original entry>> is located in %s, line %d.)"

#: ext/todo.py:175
msgid "original entry"
msgstr "original entry"

#: ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr "hardcoded link %r could be replaced by an extlink (try using %r instead)"

#: ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr "missing '+' or '-' in '%s' option."

#: ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr "'%s' is not a valid option."

#: ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "'%s' is not a valid pyversion option"

#: ext/doctest.py:220
msgid "invalid TestCode type"
msgstr "invalid TestCode type"

#: ext/doctest.py:281
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr "Testing of doctests in the sources finished, look at the results in %(outdir)s/output.txt."

#: ext/doctest.py:434
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr "no code/output in %s block at %s:%s"

#: ext/doctest.py:522
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr "ignoring invalid doctest code: %r"

#: ext/graphviz.py:135
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr "Graphviz directive cannot have both content and a filename argument"

#: ext/graphviz.py:145
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "External Graphviz file %r not found or reading it failed"

#: ext/graphviz.py:152
msgid "Ignoring \"graphviz\" directive without content."
msgstr "Ignoring \"graphviz\" directive without content."

#: ext/graphviz.py:268
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr ""

#: ext/graphviz.py:303
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr "dot command %r cannot be run (needed for graphviz output), check the graphviz_dot setting"

#: ext/graphviz.py:310
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dot exited with error:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/graphviz.py:313
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dot did not produce an output file:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/graphviz.py:329
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr "graphviz_output_format must be one of 'png', 'svg', but is %r"

#: ext/graphviz.py:333 ext/graphviz.py:386 ext/graphviz.py:423
#, python-format
msgid "dot code %r: %s"
msgstr "dot code %r: %s"

#: ext/graphviz.py:436 ext/graphviz.py:444
#, python-format
msgid "[graph: %s]"
msgstr "[graph: %s]"

#: ext/graphviz.py:438 ext/graphviz.py:446
msgid "[graph]"
msgstr "[graph]"

#: ext/imgmath.py:369 ext/mathjax.py:52
msgid "Link to this equation"
msgstr ""

#: ext/apidoc.py:85
#, python-format
msgid "Would create file %s."
msgstr "Would create file %s."

#: ext/apidoc.py:375
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr "\nLook recursively in <MODULE_PATH> for Python modules and packages and create\none reST file with automodule directives per package in the <OUTPUT_PATH>.\n\nThe <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\nexcluded from generation.\n\nNote: By default this script will not overwrite already created files."

#: ext/apidoc.py:392
msgid "path to module to document"
msgstr "path to module to document"

#: ext/apidoc.py:396
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr "fnmatch-style file and/or directory patterns to exclude from generation"

#: ext/apidoc.py:407
msgid "directory to place all output"
msgstr "directory to place all output"

#: ext/apidoc.py:422
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "maximum depth of submodules to show in the TOC (default: 4)"

#: ext/apidoc.py:429
msgid "overwrite existing files"
msgstr "overwrite existing files"

#: ext/apidoc.py:437
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr "follow symbolic links. Powerful when combined with collective.recipe.omelette."

#: ext/apidoc.py:446
msgid "run the script without creating files"
msgstr "run the script without creating files"

#: ext/apidoc.py:453
msgid "put documentation for each module on its own page"
msgstr "put documentation for each module on its own page"

#: ext/apidoc.py:460
msgid "include \"_private\" modules"
msgstr "include \"_private\" modules"

#: ext/apidoc.py:467
msgid "filename of table of contents (default: modules)"
msgstr "filename of table of contents (default: modules)"

#: ext/apidoc.py:474
msgid "don't create a table of contents file"
msgstr "don't create a table of contents file"

#: ext/apidoc.py:481
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr "don't create headings for the module/package packages (e.g. when the docstrings already contain them)"

#: ext/apidoc.py:492
msgid "put module documentation before submodule documentation"
msgstr "put module documentation before submodule documentation"

#: ext/apidoc.py:498
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr "interpret module paths according to PEP-0420 implicit namespaces specification"

#: ext/apidoc.py:508
msgid "file suffix (default: rst)"
msgstr "file suffix (default: rst)"

#: ext/apidoc.py:515 ext/autosummary/generate.py:838
msgid "Remove existing files in the output directory that were not generated"
msgstr ""

#: ext/apidoc.py:524
msgid "generate a full project with sphinx-quickstart"
msgstr "generate a full project with sphinx-quickstart"

#: ext/apidoc.py:531
msgid "append module_path to sys.path, used when --full is given"
msgstr "append module_path to sys.path, used when --full is given"

#: ext/apidoc.py:538
msgid "project name (default: root module name)"
msgstr "project name (default: root module name)"

#: ext/apidoc.py:545
msgid "project author(s), used when --full is given"
msgstr "project author(s), used when --full is given"

#: ext/apidoc.py:552
msgid "project version, used when --full is given"
msgstr "project version, used when --full is given"

#: ext/apidoc.py:559
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "project release, used when --full is given, defaults to --doc-version"

#: ext/apidoc.py:564
msgid "extension options"
msgstr "extension options"

#: ext/apidoc.py:638
#, python-format
msgid "%s is not a directory."
msgstr "%s is not a directory."

#: ext/apidoc.py:710 ext/autosummary/generate.py:874
#, python-format
msgid "Failed to remove %s: %s"
msgstr ""

#: ext/autosectionlabel.py:48
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr "section \"%s\" gets labeled as \"%s\""

#: domains/std/__init__.py:702 domains/std/__init__.py:808
#: ext/autosectionlabel.py:52
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "duplicate label %s, other instance in %s"

#: ext/duration.py:85
msgid ""
"====================== slowest reading durations ======================="
msgstr "====================== slowest reading durations ======================="

#: ext/imgmath.py:159
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr "LaTeX command %r cannot be run (needed for maths display), check the imgmath_latex setting"

#: ext/imgmath.py:174
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr "%s command %r cannot be run (needed for maths display), check the imgmath_%s setting"

#: ext/imgmath.py:328
#, python-format
msgid "display latex %r: %s"
msgstr "display latex %r: %s"

#: ext/imgmath.py:362
#, python-format
msgid "inline latex %r: %s"
msgstr "inline latex %r: %s"

#: writers/latex.py:1093 writers/manpage.py:262 writers/texinfo.py:660
msgid "Footnotes"
msgstr "Footnotes"

#: writers/manpage.py:308 writers/text.py:935
#, python-format
msgid "[image: %s]"
msgstr "[image: %s]"

#: writers/manpage.py:309 writers/text.py:936
msgid "[image]"
msgstr "[image]"

#: writers/html5.py:99 writers/html5.py:108
msgid "Link to this definition"
msgstr ""

#: writers/html5.py:415
#, python-format
msgid "numfig_format is not defined for %s"
msgstr "numfig_format is not defined for %s"

#: writers/html5.py:427
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr "Any IDs not assigned for %s node"

#: writers/html5.py:482
msgid "Link to this term"
msgstr ""

#: writers/html5.py:525 writers/html5.py:530
msgid "Link to this heading"
msgstr ""

#: writers/html5.py:535
msgid "Link to this table"
msgstr ""

#: writers/html5.py:549 writers/latex.py:1102
#, python-format
msgid "unsupported rubric heading level: %s"
msgstr ""

#: writers/html5.py:613
msgid "Link to this code"
msgstr ""

#: writers/html5.py:615
msgid "Link to this image"
msgstr ""

#: writers/html5.py:617
msgid "Link to this toctree"
msgstr ""

#: writers/html5.py:759
msgid "Could not obtain image size. :scale: option is ignored."
msgstr "Could not obtain image size. :scale: option is ignored."

#: builders/latex/__init__.py:208 domains/std/__init__.py:645
#: domains/std/__init__.py:657 templates/latex/latex.tex.jinja:106
#: themes/basic/genindex-single.html:22 themes/basic/genindex-single.html:48
#: themes/basic/genindex-split.html:3 themes/basic/genindex-split.html:6
#: themes/basic/genindex.html:3 themes/basic/genindex.html:26
#: themes/basic/genindex.html:59 themes/basic/layout.html:127
#: writers/texinfo.py:511
msgid "Index"
msgstr "Index"

#: writers/latex.py:746 writers/texinfo.py:642
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr "encountered title node not in section, topic, table, admonition or sidebar"

#: writers/texinfo.py:1214
msgid "caption not inside a figure."
msgstr "caption not inside a figure."

#: writers/texinfo.py:1300
#, python-format
msgid "unimplemented node type: %r"
msgstr "unimplemented node type: %r"

#: writers/latex.py:364
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr "unknown %r toplevel_sectioning for class %r"

#: builders/latex/__init__.py:226 writers/latex.py:414
#, python-format
msgid "no Babel option known for language %r"
msgstr "no Babel option known for language %r"

#: writers/latex.py:432
msgid "too large :maxdepth:, ignored."
msgstr "too large :maxdepth:, ignored."

#: writers/latex.py:593
#, python-format
msgid "template %s not found; loading from legacy %s instead"
msgstr ""

#: writers/latex.py:711
msgid "document title is not a single Text node"
msgstr "document title is not a single Text node"

#: writers/latex.py:1178
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr "both tabularcolumns and :widths: option are given. :widths: is ignored."

#: writers/latex.py:1575
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "dimension unit %s is invalid. Ignored."

#: writers/latex.py:1931
#, python-format
msgid "unknown index entry type %s found"
msgstr "unknown index entry type %s found"

#: domains/std/__init__.py:86 domains/std/__init__.py:103
#, python-format
msgid "environment variable; %s"
msgstr "environment variable; %s"

#: domains/std/__init__.py:111
#, python-format
msgid "%s; configuration value"
msgstr ""

#: domains/std/__init__.py:165
msgid "Type"
msgstr ""

#: domains/std/__init__.py:175
msgid "Default"
msgstr ""

#: domains/std/__init__.py:234
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr "Malformed option description %r, should look like \"opt\", \"-opt args\", \"--opt args\", \"/opt args\" or \"+opt args\""

#: domains/std/__init__.py:305
#, python-format
msgid "%s command line option"
msgstr "%s command line option"

#: domains/std/__init__.py:307
msgid "command line option"
msgstr "command line option"

#: domains/std/__init__.py:429
msgid "glossary term must be preceded by empty line"
msgstr "glossary term must be preceded by empty line"

#: domains/std/__init__.py:437
msgid "glossary terms must not be separated by empty lines"
msgstr "glossary terms must not be separated by empty lines"

#: domains/std/__init__.py:443 domains/std/__init__.py:456
msgid "glossary seems to be misformatted, check indentation"
msgstr "glossary seems to be misformatted, check indentation"

#: domains/std/__init__.py:601
msgid "glossary term"
msgstr "glossary term"

#: domains/std/__init__.py:602
msgid "grammar token"
msgstr "grammar token"

#: domains/std/__init__.py:603
msgid "reference label"
msgstr "reference label"

#: domains/std/__init__.py:606
msgid "environment variable"
msgstr "environment variable"

#: domains/std/__init__.py:607
msgid "program option"
msgstr "programme option"

#: domains/std/__init__.py:608
msgid "document"
msgstr "document"

#: domains/std/__init__.py:646 domains/std/__init__.py:658
msgid "Module Index"
msgstr "Module Index"

#: domains/std/__init__.py:647 domains/std/__init__.py:659
#: themes/basic/defindex.html:18
msgid "Search Page"
msgstr "Search Page"

#: domains/std/__init__.py:721
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr "duplicate %s description of %s, other instance in %s"

#: domains/std/__init__.py:926
msgid "numfig is disabled. :numref: is ignored."
msgstr "numfig is disabled. :numref: is ignored."

#: domains/std/__init__.py:934
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr "Failed to create a cross reference. Any number is not assigned: %s"

#: domains/std/__init__.py:946
#, python-format
msgid "the link has no caption: %s"
msgstr "the link has no caption: %s"

#: domains/std/__init__.py:960
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "invalid numfig_format: %s (%r)"

#: domains/std/__init__.py:963
#, python-format
msgid "invalid numfig_format: %s"
msgstr "invalid numfig_format: %s"

#: domains/std/__init__.py:1194
#, python-format
msgid "undefined label: %r"
msgstr "undefined label: %r"

#: domains/std/__init__.py:1196
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr "Failed to create a cross reference. A title or caption not found: %r"

#: domains/python/__init__.py:107 domains/python/__init__.py:244
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (in module %s)"

#: domains/python/__init__.py:167 domains/python/__init__.py:334
#: domains/python/__init__.py:385 domains/python/__init__.py:424
#, python-format
msgid "%s (in module %s)"
msgstr "%s (in module %s)"

#: domains/python/__init__.py:169
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (built-in variable)"

#: domains/python/__init__.py:194
#, python-format
msgid "%s (built-in class)"
msgstr "%s (built-in class)"

#: domains/python/__init__.py:195
#, python-format
msgid "%s (class in %s)"
msgstr "%s (class in %s)"

#: domains/python/__init__.py:249
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (%s class method)"

#: domains/python/__init__.py:251
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (%s static method)"

#: domains/python/__init__.py:389
#, python-format
msgid "%s (%s property)"
msgstr "%s (%s property)"

#: domains/python/__init__.py:428
#, python-format
msgid "%s (type alias in %s)"
msgstr ""

#: domains/python/__init__.py:557
msgid "Python Module Index"
msgstr "Python Module Index"

#: domains/python/__init__.py:558
msgid "modules"
msgstr "modules"

#: domains/python/__init__.py:607
msgid "Deprecated"
msgstr "Deprecated"

#: domains/python/__init__.py:632
msgid "exception"
msgstr "exception"

#: domains/python/__init__.py:634
msgid "class method"
msgstr "class method"

#: domains/python/__init__.py:635
msgid "static method"
msgstr "static method"

#: domains/python/__init__.py:637
msgid "property"
msgstr "property"

#: domains/python/__init__.py:638
msgid "type alias"
msgstr ""

#: domains/python/__init__.py:698
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr ""

#: domains/python/__init__.py:817
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr "more than one target found for cross-reference %r: %s"

#: domains/python/__init__.py:878
msgid " (deprecated)"
msgstr " (deprecated)"

#: domains/c/__init__.py:298 domains/cpp/__init__.py:436
#: domains/python/_object.py:164 ext/napoleon/docstring.py:786
msgid "Parameters"
msgstr "Parameters"

#: domains/python/_object.py:169
msgid "Variables"
msgstr "Variables"

#: domains/python/_object.py:173
msgid "Raises"
msgstr "Raises"

#: domains/c/__init__.py:199
#, python-format
msgid "%s (C %s)"
msgstr "%s (C %s)"

#: domains/c/__init__.py:260 domains/c/_symbol.py:510
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr "Duplicate C declaration, also defined at %s:%s.\nDeclaration is '.. c:%s:: %s'."

#: domains/c/__init__.py:301 domains/cpp/__init__.py:449
msgid "Return values"
msgstr "Return values"

#: domains/c/__init__.py:673 domains/cpp/__init__.py:855
msgid "member"
msgstr "member"

#: domains/c/__init__.py:674
msgid "variable"
msgstr "variable"

#: domains/c/__init__.py:676
msgid "macro"
msgstr "macro"

#: domains/c/__init__.py:677
msgid "struct"
msgstr "struct"

#: domains/c/__init__.py:678 domains/cpp/__init__.py:853
msgid "union"
msgstr "union"

#: domains/c/__init__.py:679 domains/cpp/__init__.py:858
msgid "enum"
msgstr "enum"

#: domains/c/__init__.py:680 domains/cpp/__init__.py:859
msgid "enumerator"
msgstr "enumerator"

#: domains/c/__init__.py:681 domains/cpp/__init__.py:856
msgid "type"
msgstr "type"

#: domains/c/__init__.py:683 domains/cpp/__init__.py:861
msgid "function parameter"
msgstr "function parameter"

#: domains/cpp/__init__.py:155
msgid "Template Parameters"
msgstr "Template Parameters"

#: domains/cpp/__init__.py:277
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: domains/cpp/__init__.py:360 domains/cpp/_symbol.py:793
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr "Duplicate C++ declaration, also defined at %s:%s.\nDeclaration is '.. cpp:%s:: %s'."

#: domains/cpp/__init__.py:857
msgid "concept"
msgstr "concept"

#: domains/cpp/__init__.py:862
msgid "template parameter"
msgstr "template parameter"

#: themes/haiku/layout.html:16
msgid "Contents"
msgstr "Contents"

#: themes/agogo/layout.html:29 themes/basic/globaltoc.html:2
#: themes/basic/localtoc.html:4 themes/scrolls/layout.html:32
msgid "Table of Contents"
msgstr "Table of Contents"

#: themes/agogo/layout.html:34 themes/basic/layout.html:130
#: themes/basic/search.html:3 themes/basic/search.html:15
msgid "Search"
msgstr "Search"

#: themes/agogo/layout.html:37 themes/basic/searchbox.html:8
#: themes/basic/searchfield.html:12
msgid "Go"
msgstr "Go"

#: themes/agogo/layout.html:81 themes/basic/sourcelink.html:7
msgid "Show Source"
msgstr "Show Source"

#: themes/classic/layout.html:12 themes/classic/static/sidebar.js.jinja:51
msgid "Collapse sidebar"
msgstr "Collapse sidebar"

#: themes/basic/layout.html:18
msgid "Navigation"
msgstr "Navigation"

#: themes/basic/layout.html:115
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Search within %(docstitle)s"

#: themes/basic/layout.html:124
msgid "About these documents"
msgstr "About these documents"

#: themes/basic/layout.html:133 themes/basic/layout.html:177
#: themes/basic/layout.html:179
msgid "Copyright"
msgstr "Copyright"

#: themes/basic/layout.html:183 themes/basic/layout.html:189
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr "&#169; %(copyright_prefix)s %(copyright)s."

#: themes/basic/layout.html:201
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Last updated on %(last_updated)s."

#: themes/basic/layout.html:204
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s."

#: themes/basic/genindex-single.html:26
#, python-format
msgid "Index &#x2013; %(key)s"
msgstr ""

#: themes/basic/genindex-single.html:54 themes/basic/genindex-split.html:16
#: themes/basic/genindex-split.html:30 themes/basic/genindex.html:65
msgid "Full index on one page"
msgstr "Full index on one page"

#: themes/basic/genindex-split.html:8
msgid "Index pages by letter"
msgstr "Index pages by letter"

#: themes/basic/genindex-split.html:17
msgid "can be huge"
msgstr "can be huge"

#: themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "Search %(docstitle)s"

#: themes/basic/sourcelink.html:4
msgid "This Page"
msgstr "This Page"

#: themes/basic/defindex.html:4
msgid "Overview"
msgstr "Overview"

#: themes/basic/defindex.html:8
msgid "Welcome! This is"
msgstr "Welcome! This is"

#: themes/basic/defindex.html:9
msgid "the documentation for"
msgstr "the documentation for"

#: themes/basic/defindex.html:10
msgid "last updated"
msgstr "last updated"

#: themes/basic/defindex.html:13
msgid "Indices and tables:"
msgstr "Indices and tables:"

#: themes/basic/defindex.html:16
msgid "Complete Table of Contents"
msgstr "Complete Table of Contents"

#: themes/basic/defindex.html:17
msgid "lists all sections and subsections"
msgstr "lists all sections and subsections"

#: themes/basic/defindex.html:19
msgid "search this documentation"
msgstr "search this documentation"

#: themes/basic/defindex.html:21
msgid "Global Module Index"
msgstr "Global Module Index"

#: themes/basic/defindex.html:22
msgid "quick access to all modules"
msgstr "quick access to all modules"

#: builders/html/__init__.py:499 themes/basic/defindex.html:23
msgid "General Index"
msgstr "General Index"

#: themes/basic/defindex.html:24
msgid "all functions, classes, terms"
msgstr "all functions, classes, terms"

#: themes/basic/searchbox.html:4
msgid "Quick search"
msgstr "Quick search"

#: themes/basic/search.html:20
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "Please activate JavaScript to enable the search\n    functionality."

#: themes/basic/search.html:28
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr "Searching for multiple words only shows matches that contain\n    all words."

#: themes/basic/search.html:35
msgid "search"
msgstr "search"

#: themes/basic/relations.html:4
msgid "Previous topic"
msgstr "Previous topic"

#: themes/basic/relations.html:6
msgid "previous chapter"
msgstr "previous chapter"

#: themes/basic/relations.html:11
msgid "Next topic"
msgstr "Next topic"

#: themes/basic/relations.html:13
msgid "next chapter"
msgstr "next chapter"

#: themes/classic/static/sidebar.js.jinja:42
msgid "Expand sidebar"
msgstr "Expand sidebar"

#: themes/basic/changes/frameset.html:5
#: themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "Changes in Version %(version)s &#8212; %(docstitle)s"

#: themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "Automatically generated list of changes in version %(version)s"

#: themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "Library changes"

#: themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "C API changes"

#: themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "Other changes"

#: themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "Hide Search Matches"

#: themes/basic/static/searchtools.js:117
msgid "Search Results"
msgstr "Search Results"

#: themes/basic/static/searchtools.js:119
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories."

#: themes/basic/static/searchtools.js:123
msgid "Search finished, found one page matching the search query."
msgid_plural ""
"Search finished, found ${resultCount} pages matching the search query."
msgstr[0] ""
msgstr[1] ""

#: themes/basic/static/searchtools.js:253
msgid "Searching"
msgstr "Searching"

#: themes/basic/static/searchtools.js:270
msgid "Preparing search..."
msgstr "Preparing search..."

#: themes/basic/static/searchtools.js:474
msgid ", in "
msgstr ", in "

#: environment/collectors/asset.py:95
#, python-format
msgid "image file not readable: %s"
msgstr "image file not readable: %s"

#: environment/collectors/asset.py:123
#, python-format
msgid "image file %s not readable: %s"
msgstr "image file %s not readable: %s"

#: environment/collectors/asset.py:160
#, python-format
msgid "download file not readable: %s"
msgstr "download file not readable: %s"

#: environment/collectors/toctree.py:258
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr "%s is already assigned section numbers (nested numbered toctree?)"

#: environment/adapters/toctree.py:318
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "circular toctree references detected, ignoring: %s <- %s"

#: environment/adapters/toctree.py:342
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "toctree contains reference to document %r that doesn't have a title: no link will be generated"

#: environment/adapters/toctree.py:357
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr "toctree contains reference to non-included document %r"

#: environment/adapters/indexentries.py:126
#, python-format
msgid "see %s"
msgstr "see %s"

#: environment/adapters/indexentries.py:136
#, python-format
msgid "see also %s"
msgstr "see also %s"

#: environment/adapters/indexentries.py:144
#, python-format
msgid "unknown index entry type %r"
msgstr "unknown index entry type %r"

#: environment/adapters/indexentries.py:273
#: templates/latex/sphinxmessages.sty.jinja:11
msgid "Symbols"
msgstr "Symbols"

#: builders/html/_build_info.py:32
msgid "failed to read broken build info file (unknown version)"
msgstr ""

#: builders/html/_build_info.py:36
msgid "failed to read broken build info file (missing config entry)"
msgstr ""

#: builders/html/_build_info.py:39
msgid "failed to read broken build info file (missing tags entry)"
msgstr ""

#: builders/html/__init__.py:113
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "The HTML pages are in %(outdir)s."

#: builders/html/__init__.py:340
#, python-format
msgid "Failed to read build info file: %r"
msgstr "Failed to read build info file: %r"

#: builders/html/__init__.py:355
msgid "build_info mismatch, copying .buildinfo to .buildinfo.bak"
msgstr ""

#: builders/html/__init__.py:358
msgid "building [html]: "
msgstr ""

#: builders/html/__init__.py:374
#, python-format
msgid ""
"template %s has been changed since the previous build, all docs will be "
"rebuilt"
msgstr ""

#: builders/html/__init__.py:499
msgid "index"
msgstr "index"

#: builders/html/__init__.py:547
#, python-format
msgid "Logo of %s"
msgstr ""

#: builders/html/__init__.py:572
msgid "next"
msgstr "next"

#: builders/html/__init__.py:581
msgid "previous"
msgstr "previous"

#: builders/html/__init__.py:678
msgid "generating indices"
msgstr "generating indices"

#: builders/html/__init__.py:693
msgid "writing additional pages"
msgstr "writing additional pages"

#: builders/html/__init__.py:772
#, python-format
msgid "cannot copy image file '%s': %s"
msgstr ""

#: builders/html/__init__.py:784
msgid "copying downloadable files... "
msgstr "copying downloadable files... "

#: builders/html/__init__.py:796
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "cannot copy downloadable file %r: %s"

#: builders/html/__init__.py:843
#, python-format
msgid "Failed to copy a file in the theme's 'static' directory: %s: %r"
msgstr ""

#: builders/html/__init__.py:861
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr "Failed to copy a file in html_static_file: %s: %r"

#: builders/html/__init__.py:896
msgid "copying static files"
msgstr "copying static files"

#: builders/html/__init__.py:912
#, python-format
msgid "cannot copy static file %r"
msgstr "cannot copy static file %r"

#: builders/html/__init__.py:917
msgid "copying extra files"
msgstr "copying extra files"

#: builders/html/__init__.py:927
#, python-format
msgid "cannot copy extra file %r"
msgstr "cannot copy extra file %r"

#: builders/html/__init__.py:933
#, python-format
msgid "Failed to write build info file: %r"
msgstr "Failed to write build info file: %r"

#: builders/html/__init__.py:982
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr "search index couldn't be loaded, but not all documents will be built: the index will be incomplete."

#: builders/html/__init__.py:1027
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr "page %s matches two patterns in html_sidebars: %r and %r"

#: builders/html/__init__.py:1188
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr "a Unicode error occurred when rendering the page %s. Please make sure all config values that contain non-ASCII content are Unicode strings."

#: builders/html/__init__.py:1197
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "An error happened in rendering the page %s.\nReason: %r"

#: builders/html/__init__.py:1229
msgid "dumping object inventory"
msgstr "dumping object inventory"

#: builders/html/__init__.py:1237
#, python-format
msgid "dumping search index in %s"
msgstr "dumping search index in %s"

#: builders/html/__init__.py:1279
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "invalid js_file: %r, ignored"

#: builders/html/__init__.py:1312
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr "Many math_renderers are registered. But no math_renderer is selected."

#: builders/html/__init__.py:1317
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "Unknown math_renderer %r is given."

#: builders/html/__init__.py:1325
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "html_extra_path entry %r does not exist"

#: builders/html/__init__.py:1332
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr "html_extra_path entry %r is placed inside outdir"

#: builders/html/__init__.py:1342
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "html_static_path entry %r does not exist"

#: builders/html/__init__.py:1349
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr "html_static_path entry %r is placed inside outdir"

#: builders/html/__init__.py:1361 builders/latex/__init__.py:507
#, python-format
msgid "logo file %r does not exist"
msgstr "logo file %r does not exist"

#: builders/html/__init__.py:1372
#, python-format
msgid "favicon file %r does not exist"
msgstr "favicon file %r does not exist"

#: builders/html/__init__.py:1384
#, python-format
msgid ""
"Values in 'html_sidebars' must be a list of strings. At least one pattern "
"has a string value: %s. Change to `html_sidebars = %r`."
msgstr ""

#: builders/html/__init__.py:1397
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr "HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in configuration options)"

#: builders/html/__init__.py:1414
#, python-format
msgid "%s %s documentation"
msgstr "%s %s documentation"

#: builders/latex/transforms.py:118
msgid "Failed to get a docname!"
msgstr "Failed to get a docname!"

#: builders/latex/transforms.py:119
#, python-format
msgid "Failed to get a docname for source %r!"
msgstr ""

#: builders/latex/transforms.py:485
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr "No footnote was found for given reference node %r"

#: builders/latex/__init__.py:117
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "The LaTeX files are in %(outdir)s."

#: builders/latex/__init__.py:119
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr "\nRun 'make' in that directory to run these through (pdf)latex\n(use `make latexpdf' here to do that automatically)."

#: builders/latex/__init__.py:157
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "no \"latex_documents\" config value found; no documents will be written"

#: builders/latex/__init__.py:169
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "\"latex_documents\" config value references unknown document %s"

#: builders/latex/__init__.py:211 templates/latex/latex.tex.jinja:91
msgid "Release"
msgstr "Release"

#: builders/latex/__init__.py:432
msgid "copying TeX support files"
msgstr "copying TeX support files"

#: builders/latex/__init__.py:469
msgid "copying additional files"
msgstr "copying additional files"

#: builders/latex/__init__.py:543
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr "Unknown configure key: latex_elements[%r], ignored."

#: builders/latex/__init__.py:551
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr "Unknown theme option: latex_theme_options[%r], ignored."

#: builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr "%r doesn't have \"theme\" setting"

#: builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr "%r doesn't have \"%s\" setting"

#: _cli/util/errors.py:124
msgid "Exception occurred, starting debugger:"
msgstr ""

#: _cli/util/errors.py:133
msgid "reStructuredText markup error:"
msgstr ""

#: _cli/util/errors.py:168
msgid "The full traceback has been saved in:"
msgstr ""

#: _cli/util/errors.py:172
msgid ""
"To report this error to the developers, please open an issue at "
"<https://github.com/sphinx-doc/sphinx/issues/>. Thanks!"
msgstr ""

#: transforms/post_transforms/__init__.py:124
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr "Could not determine the fallback text for the cross-reference. Might be a bug."

#: transforms/post_transforms/__init__.py:184
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr "more than one target found for 'any' cross-reference %r: could be %s"

#: transforms/post_transforms/__init__.py:250
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr "%s:%s reference target not found: %s"

#: transforms/post_transforms/__init__.py:256
#, python-format
msgid "%r reference target not found: %s"
msgstr "%r reference target not found: %s"

#: transforms/post_transforms/images.py:77
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr "Could not fetch remote image: %s [%s]"

#: transforms/post_transforms/images.py:94
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr "Could not fetch remote image: %s [%d]"

#: transforms/post_transforms/images.py:141
#, python-format
msgid "Unknown image format: %s..."
msgstr "Unknown image format: %s..."

#: ext/napoleon/docstring.py:707
msgid "Example"
msgstr "Example"

#: ext/napoleon/docstring.py:708
msgid "Examples"
msgstr "Examples"

#: ext/napoleon/__init__.py:344 ext/napoleon/docstring.py:752
msgid "Keyword Arguments"
msgstr "Keyword Arguments"

#: ext/napoleon/docstring.py:768
msgid "Notes"
msgstr "Notes"

#: ext/napoleon/docstring.py:777
msgid "Other Parameters"
msgstr "Other Parameters"

#: ext/napoleon/docstring.py:813
msgid "Receives"
msgstr "Receives"

#: ext/napoleon/docstring.py:817
msgid "References"
msgstr "References"

#: ext/napoleon/docstring.py:849
msgid "Warns"
msgstr "Warns"

#: ext/napoleon/docstring.py:853
msgid "Yields"
msgstr "Yields"

#: ext/napoleon/docstring.py:1015
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr "invalid value set (missing closing brace): %s"

#: ext/napoleon/docstring.py:1022
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr "invalid value set (missing opening brace): %s"

#: ext/napoleon/docstring.py:1029
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr "malformed string literal (missing closing quote): %s"

#: ext/napoleon/docstring.py:1036
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr "malformed string literal (missing opening quote): %s"

#: ext/autosummary/__init__.py:255
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr "autosummary references excluded document %r. Ignored."

#: ext/autosummary/__init__.py:257
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr "autosummary: stub file not found %r. Check your autosummary_generate setting."

#: ext/autosummary/__init__.py:276
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr "A captioned autosummary requires :toctree: option. ignored."

#: ext/autosummary/__init__.py:329
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "autosummary: failed to import %s.\nPossible hints:\n%s"

#: ext/autosummary/__init__.py:343
#, python-format
msgid "failed to parse name %s"
msgstr "failed to parse name %s"

#: ext/autosummary/__init__.py:348
#, python-format
msgid "failed to import object %s"
msgstr "failed to import object %s"

#: ext/autosummary/__init__.py:647
#, python-format
msgid ""
"Summarised items should not include the current module. Replace %r with %r."
msgstr ""

#: ext/autosummary/__init__.py:818
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr "autosummary_generate: file not found: %s"

#: ext/autosummary/__init__.py:826
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr ""

#: ext/autosummary/generate.py:214 ext/autosummary/generate.py:390
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr "autosummary: failed to determine %r to be documented, the following exception was raised:\n%s"

#: ext/autosummary/generate.py:525
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr "[autosummary] generating autosummary for: %s"

#: ext/autosummary/generate.py:529
#, python-format
msgid "[autosummary] writing to %s"
msgstr "[autosummary] writing to %s"

#: ext/autosummary/generate.py:571
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "[autosummary] failed to import %s.\nPossible hints:\n%s"

#: ext/autosummary/generate.py:766
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr "\nGenerate ReStructuredText using autosummary directives.\n\nsphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\nthe reStructuredText files from the autosummary directives contained in the\ngiven input files.\n\nThe format of the autosummary directive is documented in the\n``sphinx.ext.autosummary`` Python module and can be read using::\n\n  pydoc sphinx.ext.autosummary\n"

#: ext/autosummary/generate.py:788
msgid "source files to generate rST files for"
msgstr "source files to generate rST files for"

#: ext/autosummary/generate.py:796
msgid "directory to place all output in"
msgstr "directory to place all output in"

#: ext/autosummary/generate.py:804
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "default suffix for files (default: %(default)s)"

#: ext/autosummary/generate.py:812
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr "custom template directory (default: %(default)s)"

#: ext/autosummary/generate.py:820
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr "document imported members (default: %(default)s)"

#: ext/autosummary/generate.py:828
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr "document exactly the members in module __all__ attribute. (default: %(default)s)"

#: ext/intersphinx/_resolve.py:47
#, python-format
msgid "(in %s v%s)"
msgstr "(in %s v%s)"

#: ext/intersphinx/_resolve.py:49
#, python-format
msgid "(in %s)"
msgstr "(in %s)"

#: ext/intersphinx/_resolve.py:103
#, python-format
msgid "inventory '%s': duplicate matches found for %s:%s"
msgstr ""

#: ext/intersphinx/_resolve.py:113
#, python-format
msgid "inventory '%s': multiple matches found for %s:%s"
msgstr ""

#: ext/intersphinx/_resolve.py:359
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:367
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:378
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:585
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr "external %s:%s reference target not found: %s"

#: ext/intersphinx/_load.py:59
#, python-format
msgid ""
"Invalid intersphinx project identifier `%r` in intersphinx_mapping. Project "
"identifiers must be non-empty strings."
msgstr ""

#: ext/intersphinx/_load.py:70
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Expected a two-element tuple "
"or list."
msgstr ""

#: ext/intersphinx/_load.py:81
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Values must be a (target URI,"
" inventory locations) pair."
msgstr ""

#: ext/intersphinx/_load.py:92
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique non-empty strings."
msgstr ""

#: ext/intersphinx/_load.py:101
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique (other instance in intersphinx_mapping[%r])."
msgstr ""

#: ext/intersphinx/_load.py:120
#, python-format
msgid ""
"Invalid inventory location value `%r` in intersphinx_mapping[%r][1]. "
"Inventory locations must be non-empty strings or None."
msgstr ""

#: ext/intersphinx/_load.py:131
msgid "Invalid `intersphinx_mapping` configuration (1 error)."
msgstr ""

#: ext/intersphinx/_load.py:134
#, python-format
msgid "Invalid `intersphinx_mapping` configuration (%s errors)."
msgstr ""

#: ext/intersphinx/_load.py:155
msgid "An invalid intersphinx_mapping entry was added after normalisation."
msgstr ""

#: ext/intersphinx/_load.py:240
#, python-format
msgid "loading intersphinx inventory '%s' from %s ..."
msgstr ""

#: ext/intersphinx/_load.py:265
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr "encountered some issues with some of the inventories, but they had working alternatives:"

#: ext/intersphinx/_load.py:275
msgid "failed to reach any of the inventories with the following issues:"
msgstr "failed to reach any of the inventories with the following issues:"

#: ext/intersphinx/_load.py:319
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr "intersphinx inventory has moved: %s -> %s"

#: ext/autodoc/type_comment.py:132
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr "Failed to update signature for %r: parameter not found: %s"

#: ext/autodoc/type_comment.py:135
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr "Failed to parse type_comment for %r: %s"

#: ext/autodoc/__init__.py:141
#, python-format
msgid "invalid value for member-order option: %s"
msgstr "invalid value for member-order option: %s"

#: ext/autodoc/__init__.py:149
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr "invalid value for class-doc-from option: %s"

#: ext/autodoc/__init__.py:408
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr "invalid signature for auto%s (%r)"

#: ext/autodoc/__init__.py:525
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr "error while formatting arguments for %s: %s"

#: ext/autodoc/__init__.py:795
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr "autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n%s"

#: ext/autodoc/__init__.py:890
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr "don't know which module to import for autodocumenting %r (try placing a \"module\" or \"currentmodule\" directive in the document, or giving an explicit module name)"

#: ext/autodoc/__init__.py:934
#, python-format
msgid "A mocked object is detected: %r"
msgstr "A mocked object is detected: %r"

#: ext/autodoc/__init__.py:953
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr "error while formatting signature for %s: %s"

#: ext/autodoc/__init__.py:1016
msgid "\"::\" in automodule name doesn't make sense"
msgstr "\"::\" in automodule name doesn't make sense"

#: ext/autodoc/__init__.py:1023
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr "signature arguments or return annotation given for automodule %s"

#: ext/autodoc/__init__.py:1036
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr "__all__ should be a list of strings, not %r (in module %s) -- ignoring __all__"

#: ext/autodoc/__init__.py:1102
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr "missing attribute mentioned in :members: option: module %s, attribute %s"

#: ext/autodoc/__init__.py:1325 ext/autodoc/__init__.py:1402
#: ext/autodoc/__init__.py:2810
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr "Failed to get a function signature for %s: %s"

#: ext/autodoc/__init__.py:1616
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr "Failed to get a constructor signature for %s: %s"

#: ext/autodoc/__init__.py:1743
#, python-format
msgid "Bases: %s"
msgstr "Bases: %s"

#: ext/autodoc/__init__.py:1757
#, python-format
msgid "missing attribute %s in object %s"
msgstr "missing attribute %s in object %s"

#: ext/autodoc/__init__.py:1838 ext/autodoc/__init__.py:1875
#: ext/autodoc/__init__.py:1970
#, python-format
msgid "alias of %s"
msgstr "alias of %s"

#: ext/autodoc/__init__.py:1858
#, python-format
msgid "alias of TypeVar(%s)"
msgstr "alias of TypeVar(%s)"

#: ext/autodoc/__init__.py:2198 ext/autodoc/__init__.py:2298
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr "Failed to get a method signature for %s: %s"

#: ext/autodoc/__init__.py:2429
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr "Invalid __slots__ found on %s. Ignored."

#: ext/autodoc/preserve_defaults.py:190
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr "Failed to parse a default argument value for %r: %s"

#: templates/latex/longtable.tex.jinja:52
#: templates/latex/sphinxmessages.sty.jinja:8
msgid "continued from previous page"
msgstr "continued from previous page"

#: templates/latex/longtable.tex.jinja:63
#: templates/latex/sphinxmessages.sty.jinja:9
msgid "continues on next page"
msgstr "continues on next page"

#: templates/latex/sphinxmessages.sty.jinja:10
msgid "Non-alphabetical"
msgstr "Non-alphabetical"

#: templates/latex/sphinxmessages.sty.jinja:12
msgid "Numbers"
msgstr "Numbers"

#: templates/latex/sphinxmessages.sty.jinja:13
msgid "page"
msgstr "page"
