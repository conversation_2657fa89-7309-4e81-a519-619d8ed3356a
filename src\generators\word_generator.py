"""
Word文档生成器

负责生成Word文档，整合所有转换后的内容。
"""

from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from docx import Document
from docx.shared import Inches, Pt, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

from ..core.exceptions import WordGenerationError
from ..utils.logger import get_logger
from ..utils.error_handler import handle_errors, log_performance
from ..utils.config_manager import ConfigManager

logger = get_logger(__name__)


class WordGenerator:
    """
    Word文档生成器
    
    负责创建Word文档并应用样式和格式。
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化Word文档生成器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or ConfigManager()
        self.config = self.config_manager.get_config()
        self.word_config = self.config.word_generation
        
        # 初始化子模块
        self._init_submodules()
        
        # 当前文档
        self.document = None
        self.current_styles = {}
    
    def _init_submodules(self) -> None:
        """初始化子模块"""
        from .structure_builder import StructureBuilder
        from .style_formatter import StyleFormatter
        from .content_assembler import ContentAssembler
        
        self.structure_builder = StructureBuilder(self.config_manager)
        self.style_formatter = StyleFormatter(self.config_manager)
        self.content_assembler = ContentAssembler(self.config_manager)
        
        logger.debug("Word生成器子模块初始化完成")
    
    @handle_errors(context="生成Word文档")
    @log_performance
    def generate_document(
        self,
        latex_document: Dict[str, Any],
        formula_results: List[Any],
        output_path: Union[str, Path],
        template: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成Word文档
        
        Args:
            latex_document: 解析后的LaTeX文档
            formula_results: 公式转换结果
            output_path: 输出文件路径
            template: 样式模板名称
            
        Returns:
            生成结果字典
        """
        output_path = Path(output_path)
        template = template or "default"
        
        logger.info(f"开始生成Word文档: {output_path}")
        
        try:
            # 1. 创建新文档
            self.document = Document()
            
            # 2. 应用样式模板
            self._apply_template(template)
            
            # 3. 设置文档属性
            self._set_document_properties(latex_document)
            
            # 4. 构建文档结构
            self._build_document_structure(latex_document, formula_results)
            
            # 5. 保存文档
            self._save_document(output_path)
            
            result = {
                'success': True,
                'output_file': str(output_path),
                'document_info': {
                    'pages': self._estimate_page_count(),
                    'paragraphs': len(self.document.paragraphs),
                    'sections': len(latex_document.get('sections', [])),
                    'formulas': len(formula_results)
                },
                'template_used': template
            }
            
            logger.info("Word文档生成完成")
            return result
            
        except Exception as e:
            raise WordGenerationError(
                f"Word文档生成失败: {e}",
                document_section="document_generation"
            )
    
    def _apply_template(self, template_name: str) -> None:
        """应用样式模板"""
        logger.debug(f"应用样式模板: {template_name}")
        
        # 获取样式模板
        style_templates = self.config_manager.get_style_templates()
        template = style_templates.get(template_name, style_templates.get('default', {}))
        
        if not template:
            logger.warning(f"样式模板 {template_name} 不存在，使用默认样式")
            return
        
        # 应用页面设置
        self._apply_page_setup(template.get('page_setup', {}))
        
        # 创建自定义样式
        self._create_custom_styles(template)
        
        self.current_styles = template
    
    def _apply_page_setup(self, page_setup: Dict[str, Any]) -> None:
        """应用页面设置"""
        if not page_setup:
            return
        
        section = self.document.sections[0]
        
        # 页边距
        margins = page_setup.get('margins', {})
        if margins:
            section.top_margin = Cm(margins.get('top', 2.5))
            section.bottom_margin = Cm(margins.get('bottom', 2.5))
            section.left_margin = Cm(margins.get('left', 2.5))
            section.right_margin = Cm(margins.get('right', 2.5))
        
        # 页眉页脚边距
        if 'header_margin' in page_setup:
            section.header_distance = Cm(page_setup['header_margin'])
        if 'footer_margin' in page_setup:
            section.footer_distance = Cm(page_setup['footer_margin'])
    
    def _create_custom_styles(self, template: Dict[str, Any]) -> None:
        """创建自定义样式"""
        styles = self.document.styles
        
        # 创建段落样式
        paragraph_styles = template.get('paragraph_styles', {})
        for style_name, style_config in paragraph_styles.items():
            try:
                # 检查样式是否已存在
                style_id = f"Custom_{style_name}"
                if style_id not in [s.name for s in styles]:
                    style = styles.add_style(style_id, WD_STYLE_TYPE.PARAGRAPH)
                    self._configure_paragraph_style(style, style_config)
            except Exception as e:
                logger.warning(f"创建段落样式 {style_name} 失败: {e}")
        
        # 创建标题样式
        heading_styles = template.get('heading_styles', {})
        for style_name, style_config in heading_styles.items():
            try:
                style_id = f"Custom_{style_name}"
                if style_id not in [s.name for s in styles]:
                    style = styles.add_style(style_id, WD_STYLE_TYPE.PARAGRAPH)
                    self._configure_paragraph_style(style, style_config)
            except Exception as e:
                logger.warning(f"创建标题样式 {style_name} 失败: {e}")
    
    def _configure_paragraph_style(self, style, config: Dict[str, Any]) -> None:
        """配置段落样式"""
        paragraph_format = style.paragraph_format
        font = style.font
        
        # 字体设置
        if 'font_family' in config:
            font.name = config['font_family']
        if 'font_size' in config:
            font.size = Pt(config['font_size'])
        if config.get('bold', False):
            font.bold = True
        if config.get('italic', False):
            font.italic = True
        
        # 段落格式
        if 'alignment' in config:
            alignment_map = {
                'left': WD_ALIGN_PARAGRAPH.LEFT,
                'center': WD_ALIGN_PARAGRAPH.CENTER,
                'right': WD_ALIGN_PARAGRAPH.RIGHT,
                'justify': WD_ALIGN_PARAGRAPH.JUSTIFY
            }
            paragraph_format.alignment = alignment_map.get(config['alignment'])
        
        if 'line_spacing' in config:
            paragraph_format.line_spacing_rule = WD_LINE_SPACING.MULTIPLE
            paragraph_format.line_spacing = config['line_spacing']
        
        if 'space_before' in config:
            paragraph_format.space_before = Pt(config['space_before'])
        if 'space_after' in config:
            paragraph_format.space_after = Pt(config['space_after'])
        
        if 'left_indent' in config:
            paragraph_format.left_indent = Cm(config['left_indent'])
        if 'right_indent' in config:
            paragraph_format.right_indent = Cm(config['right_indent'])
        if 'first_line_indent' in config:
            paragraph_format.first_line_indent = Cm(config['first_line_indent'])
    
    def _set_document_properties(self, latex_document: Dict[str, Any]) -> None:
        """设置文档属性"""
        core_props = self.document.core_properties
        
        if latex_document.get('title'):
            core_props.title = latex_document['title']
        if latex_document.get('author'):
            core_props.author = latex_document['author']
        
        # 设置其他属性
        core_props.subject = "LaTeX到Word转换文档"
        core_props.keywords = "LaTeX, Word, 数学公式, 转换"
        core_props.comments = "由LaTeX到Word转换系统生成"
    
    def _build_document_structure(
        self,
        latex_document: Dict[str, Any],
        formula_results: List[Any]
    ) -> None:
        """构建文档结构"""
        logger.debug("开始构建文档结构")
        
        # 添加标题
        self._add_title(latex_document)
        
        # 添加作者信息
        self._add_author_info(latex_document)
        
        # 添加目录（如果需要）
        # self._add_table_of_contents()
        
        # 添加正文内容
        self._add_content(latex_document, formula_results)
    
    def _add_title(self, latex_document: Dict[str, Any]) -> None:
        """添加标题"""
        title = latex_document.get('title')
        if title:
            # 清理LaTeX格式
            clean_title = self._clean_latex_text(title)
            
            title_paragraph = self.document.add_heading(clean_title, level=0)
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    def _add_author_info(self, latex_document: Dict[str, Any]) -> None:
        """添加作者信息"""
        author = latex_document.get('author')
        date = latex_document.get('date')
        
        if author:
            clean_author = self._clean_latex_text(author)
            author_paragraph = self.document.add_paragraph(clean_author)
            author_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        if date and date != r'\today':
            clean_date = self._clean_latex_text(date)
            date_paragraph = self.document.add_paragraph(clean_date)
            date_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        self.document.add_paragraph()
    
    def _add_content(
        self,
        latex_document: Dict[str, Any],
        formula_results: List[Any]
    ) -> None:
        """添加正文内容"""
        sections = latex_document.get('sections', [])
        
        # 创建公式映射
        formula_map = {i: result for i, result in enumerate(formula_results)}
        
        for section in sections:
            self._add_section(section, formula_map)
    
    def _add_section(self, section: Dict[str, Any], formula_map: Dict[int, Any]) -> None:
        """添加章节"""
        # 添加章节标题
        title = section.get('title', '')
        level = section.get('level', 1)
        
        # 映射LaTeX章节级别到Word标题级别
        word_level = min(level, 9)  # Word最多支持9级标题
        
        clean_title = self._clean_latex_text(title)
        heading = self.document.add_heading(clean_title, level=word_level)
        
        # 添加章节内容
        content = section.get('content', '')
        if content:
            self._add_paragraph_content(content, formula_map)
        
        # 递归添加子章节
        subsections = section.get('subsections', [])
        for subsection in subsections:
            self._add_section(subsection, formula_map)
    
    def _add_paragraph_content(self, content: str, formula_map: Dict[int, Any]) -> None:
        """添加段落内容"""
        # 简化版本：直接添加文本段落
        # 在完整实现中，这里需要解析内联公式、格式化文本等
        
        paragraphs = content.split('\n\n')
        for para_text in paragraphs:
            para_text = para_text.strip()
            if para_text:
                clean_text = self._clean_latex_text(para_text)
                if clean_text:
                    self.document.add_paragraph(clean_text)
    
    def _clean_latex_text(self, text: str) -> str:
        """清理LaTeX文本"""
        if not text:
            return ""
        
        # 移除常见的LaTeX命令
        import re
        
        # 移除简单的格式化命令
        text = re.sub(r'\\textbf\{([^}]+)\}', r'\1', text)  # 粗体
        text = re.sub(r'\\textit\{([^}]+)\}', r'\1', text)  # 斜体
        text = re.sub(r'\\emph\{([^}]+)\}', r'\1', text)    # 强调
        
        # 移除其他常见命令
        text = re.sub(r'\\[a-zA-Z]+\{([^}]*)\}', r'\1', text)
        text = re.sub(r'\\[a-zA-Z]+', '', text)
        
        # 清理多余的空白
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _save_document(self, output_path: Path) -> None:
        """保存文档"""
        try:
            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存文档
            self.document.save(str(output_path))
            
            logger.info(f"文档已保存到: {output_path}")
            
        except Exception as e:
            raise WordGenerationError(
                f"文档保存失败: {e}",
                document_section="document_save"
            )
    
    def _estimate_page_count(self) -> int:
        """估算页数"""
        # 简单的页数估算
        paragraph_count = len(self.document.paragraphs)
        estimated_pages = max(1, paragraph_count // 25)  # 假设每页25段
        return estimated_pages
    
    def add_formula(self, formula_result: Any, is_inline: bool = True) -> None:
        """添加公式到文档"""
        # 这里将集成MathType
        # 目前先添加占位符
        if formula_result.success:
            placeholder = f"[公式: {formula_result.original_content[:30]}...]"
        else:
            placeholder = f"[公式转换失败: {formula_result.error_message}]"
        
        if is_inline:
            # 内联公式
            paragraph = self.document.paragraphs[-1] if self.document.paragraphs else self.document.add_paragraph()
            run = paragraph.add_run(placeholder)
            run.italic = True
        else:
            # 块级公式
            formula_paragraph = self.document.add_paragraph(placeholder)
            formula_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    def get_document_stats(self) -> Dict[str, Any]:
        """获取文档统计信息"""
        if not self.document:
            return {}
        
        return {
            'paragraphs': len(self.document.paragraphs),
            'sections': len(self.document.sections),
            'tables': len(self.document.tables),
            'estimated_pages': self._estimate_page_count(),
            'styles_used': len(self.current_styles)
        }
