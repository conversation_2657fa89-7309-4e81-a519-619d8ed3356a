# Translations template for Sphinx.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <AUTHOR> <EMAIL>, 2013
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2011
# tomo, 2018
# <PERSON><PERSON><PERSON>, 2017
# KaKkouo, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011
# sutefu7, 2019-2020
# <AUTHOR> <EMAIL>, 2017
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016-2017,2019,2024
# <PERSON><PERSON> <<EMAIL>>, 2016-2017,2019,2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020-2023
# tomo, 2019
# <AUTHOR> <EMAIL>, 2014
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2008
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-10-10 15:47+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: Takayuki SHIMIZUKAWA <<EMAIL>>, 2016-2017,2019,2024\n"
"Language-Team: Japanese (http://app.transifex.com/sphinx-doc/sphinx-1/language/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: events.py:77
#, python-format
msgid "Event %r already present"
msgstr "イベント %r はすでに登録されています"

#: events.py:370
#, python-format
msgid "Unknown event name: %s"
msgstr "不明なイベント名: %s"

#: events.py:416
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr "イベント %r のハンドラ %r で例外が発生しました。"

#: application.py:186
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "ソースディレクトリが存在しません (%s)"

#: application.py:190
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr "出力先ディレクトリ (%s) はディレクトリではありません"

#: application.py:194
msgid "Source directory and destination directory cannot be identical"
msgstr "出力先ディレクトリにはソースディレクトリと異なるディレクトリを指定してください"

#: application.py:224
#, python-format
msgid "Running Sphinx v%s"
msgstr "Sphinx v%s を実行中"

#: application.py:246
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "このプロジェクトはSphinx v%s以降のバージョンでなければビルドできません。"

#: application.py:262
msgid "making output directory"
msgstr "出力先ディレクトリを作成しています"

#: application.py:267 registry.py:450
#, python-format
msgid "while setting up extension %s:"
msgstr "拡張機能のセットアップ中 %s:"

#: application.py:273
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "conf.pyにある'setup'はPythonのcallableではありません。定義を修正してcallableである関数にしてください。これはconf.pyがSphinx拡張として動作するのに必要です。"

#: application.py:308
#, python-format
msgid "loading translations [%s]... "
msgstr "翻訳カタログをロードしています [%s]... "

#: application.py:325 util/display.py:90
msgid "done"
msgstr "完了"

#: application.py:327
msgid "not available for built-in messages"
msgstr "翻訳が用意されていません"

#: application.py:341
msgid "loading pickled environment"
msgstr "保存された環境データを読み込み中"

#: application.py:349
#, python-format
msgid "failed: %s"
msgstr "失敗: %s"

#: application.py:362
msgid "No builder selected, using default: html"
msgstr "ビルダーが選択されていないので、デフォルトの html を使用します"

#: application.py:394
msgid "build finished with problems."
msgstr ""

#: application.py:396
msgid "build succeeded."
msgstr ""

#: application.py:400
msgid ""
"build finished with problems, 1 warning (with warnings treated as errors)."
msgstr ""

#: application.py:403
msgid "build finished with problems, 1 warning."
msgstr ""

#: application.py:405
msgid "build succeeded, 1 warning."
msgstr ""

#: application.py:410
#, python-format
msgid ""
"build finished with problems, %s warnings (with warnings treated as errors)."
msgstr ""

#: application.py:413
#, python-format
msgid "build finished with problems, %s warnings."
msgstr ""

#: application.py:415
#, python-format
msgid "build succeeded, %s warnings."
msgstr ""

#: application.py:964
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "nodeクラス %r は既に登録されています。visitor関数は上書きされます"

#: application.py:1043
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr "ディレクティブ %r は既に登録されています。ディレクティブは上書きされます"

#: application.py:1065 application.py:1090
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr "ロール %r は既に登録されています。ロールは上書きされます"

#: application.py:1640
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "拡張 %s は並列読み込みが可能かどうかを宣言していないため、おそらく並列読み込みに対応していないでしょう。拡張の実装者に連絡して、明示してもらってください。"

#: application.py:1644
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "%s拡張は並列読み込みに対して安全ではありません"

#: application.py:1647
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "拡張 %s は並列書き込みが可能かどうかを宣言していないため、おそらく並列書き込みに対応していないでしょう。拡張の実装者に連絡して、明示してもらってください。"

#: application.py:1651
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "%s拡張は並列書き込みに対して安全ではありません"

#: application.py:1659 application.py:1663
#, python-format
msgid "doing serial %s"
msgstr "直列で %sします"

#: roles.py:205
#, python-format
msgid "Common Vulnerabilities and Exposures; CVE %s"
msgstr ""

#: roles.py:228
#, python-format
msgid "invalid CVE number %s"
msgstr ""

#: roles.py:249
#, python-format
msgid "Common Weakness Enumeration; CWE %s"
msgstr ""

#: roles.py:272
#, python-format
msgid "invalid CWE number %s"
msgstr ""

#: roles.py:293
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "Python Enhancement Proposals; PEP %s"

#: roles.py:316
#, python-format
msgid "invalid PEP number %s"
msgstr "無効なPEP番号 %s"

#: roles.py:354
#, python-format
msgid "invalid RFC number %s"
msgstr "無効なRFC番号 %s"

#: registry.py:142
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "ビルダークラス %s には\"name\"属性がありません"

#: registry.py:144
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "ビルダー %r (モジュール %s) がすでに登録されています"

#: registry.py:157
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "ビルダー名 %s は登録されておらず、entry pointにもありません"

#: registry.py:164
#, python-format
msgid "Builder name %s not registered"
msgstr "ビルダー名 %s は登録されていません"

#: registry.py:171
#, python-format
msgid "domain %s already registered"
msgstr "ドメイン %s はすでに登録されています"

#: registry.py:194 registry.py:207 registry.py:218
#, python-format
msgid "domain %s not yet registered"
msgstr "ドメイン %s はまだ登録されていません"

#: registry.py:198
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "ディレクティブ %r は既に%sドメインに登録されています"

#: registry.py:210
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "ロール %r は既にドメイン%sに登録されています"

#: registry.py:221
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "インデックス %r はすでに%sドメインに登録されています"

#: registry.py:252
#, python-format
msgid "The %r object_type is already registered"
msgstr "object_type %r はすでに登録されています"

#: registry.py:278
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "classref_type %r はすでに登録されています"

#: registry.py:285
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r はすでに登録されています"

#: registry.py:294
#, python-format
msgid "source_parser for %r is already registered"
msgstr "%r のsource_parserはすでに登録されています"

#: registry.py:302
#, python-format
msgid "Source parser for %s not registered"
msgstr "%s のsource_parserは登録されていません"

#: registry.py:318
#, python-format
msgid "Translator for %r already exists"
msgstr "%r のTranslatorはすでに登録されています"

#: registry.py:334
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "add_node() のキーワード引数は (visit, depart) の形式で関数をタプルで指定してください: %r=%r"

#: registry.py:417
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r はすでに登録されています"

#: registry.py:429
#, python-format
msgid "math renderer %s is already registered"
msgstr "math renderer %s はすでに登録されています"

#: registry.py:444
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "拡張 %r はSphinxのバージョン%sでSphinxに統合されています。この拡張は無視されます。"

#: registry.py:455
msgid "Original exception:\n"
msgstr "元の例外:\n"

#: registry.py:456
#, python-format
msgid "Could not import extension %s"
msgstr "拡張 %s をimportできません"

#: registry.py:461
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "拡張 %r には setup() 関数がありません。これは本当にSphinx拡張ですか？"

#: registry.py:470
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "このプロジェクトで使われている拡張 %s はSphinx v%s 以降が必要なため、現在のバージョンではビルドできません。"

#: registry.py:478
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "拡張 %r のsetup()関数が、対応していないオブジェクトを返しました。Noneまたはメタデータ辞書を返してください"

#: registry.py:512
#, python-format
msgid "`None` is not a valid filetype for %r."
msgstr ""

#: project.py:71
#, python-format
msgid ""
"multiple files found for the document \"%s\": %s\n"
"Use %r for the build."
msgstr ""

#: project.py:87
#, python-format
msgid "Ignored unreadable document %r."
msgstr ""

#: highlighting.py:168
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr "Pygments に %r というlexerがありません"

#: highlighting.py:202
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr ""

#: extension.py:55
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "needs_extensions設定で %s 拡張が要求されていますが、その拡張がありません。"

#: extension.py:76
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "このプロジェクトは拡張 %s の %s 以降のバージョンが必要なため、現在のバージョン(%s)ではビルドできません。"

#: theming.py:121
#, python-format
msgid ""
"Theme configuration sections other than [theme] and [options] are not "
"supported (tried to get a value from %r)."
msgstr ""

#: theming.py:127
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "設定 %s.%s がテーマ設定にありません"

#: theming.py:142
#, python-format
msgid "unsupported theme option %r given"
msgstr "サポートされていないテーマオプション %r が指定されました"

#: theming.py:215
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "テーマパス上のファイル %r は正しいzipファイルではないか、テーマを含んでいません"

#: theming.py:236
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr ""

#: theming.py:276
#, python-format
msgid "The %r theme has circular inheritance"
msgstr ""

#: theming.py:283
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr ""

#: theming.py:290
#, python-format
msgid "The %r theme has too many ancestors"
msgstr ""

#: theming.py:318
#, python-format
msgid "no theme configuration file found in %r"
msgstr ""

#: theming.py:346 theming.py:399
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr ""

#: theming.py:350
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr ""

#: theming.py:354 theming.py:402
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr ""

#: theming.py:358
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr ""

#: theming.py:377
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr ""

#: config.py:314
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "conf.py が設定ディレクトリに存在しません (%s)"

#: config.py:323
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr "無効な設定値が見つかりました: 'language = None' 。設定を有効な言語コードに更新してください。 'en' （英語）にフォールバックしています。"

#: config.py:346
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "設定値の辞書 %r は上書きないため無視されました (%r を使って個別に設定してください)"

#: config.py:355
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "%r は設定値 %r の正しい値ではないため無視されました"

#: config.py:361
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "%r は正しい型ではないため無視されました"

#: config.py:382
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "不明な設定値 %r による上書きは無視されました"

#: config.py:435
#, python-format
msgid "No such config value: %r"
msgstr ""

#: config.py:458
#, python-format
msgid "Config value %r already present"
msgstr "設定値 %r は既に登録済みです"

#: config.py:494
#, python-format
msgid ""
"cannot cache unpickable configuration value: %r (because it contains a "
"function, class, or module object)"
msgstr ""

#: config.py:531
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "設定ファイルに文法エラーが見つかりました: %s\n"

#: config.py:534
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "設定ファイル（あるいはインポートしたどれかのモジュール）がsys.exit()を呼びました"

#: config.py:541
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "設定ファイルにプログラム上のエラーがあります:\n\n%s"

#: config.py:564
#, python-format
msgid "Failed to convert %r to a set or tuple"
msgstr ""

#: config.py:585 config.py:590
#, python-format
msgid "Converting `source_suffix = %r` to `source_suffix = %r`."
msgstr ""

#: config.py:593
#, python-format
msgid ""
"The config value `source_suffix' expects a dictionary, a string, or a list "
"of strings. Got `%r' instead (type %s)."
msgstr "設定値 `source_suffix' に `%r' （%s型）が指定されましたが、文字列、文字列のリスト、辞書、のいずれかを指定してください。"

#: config.py:612
#, python-format
msgid "Section %s"
msgstr "%s 章"

#: config.py:613
#, python-format
msgid "Fig. %s"
msgstr "図 %s"

#: config.py:614
#, python-format
msgid "Table %s"
msgstr "表 %s"

#: config.py:615
#, python-format
msgid "Listing %s"
msgstr "リスト %s"

#: config.py:722
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr " 設定値 `{name}` に `{current}` が指定されましたが、  {candidates} のいずれかを指定してください。"

#: config.py:746
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "設定値 `{name}' に `{current.__name__}' 型が指定されていますが、 {permitted} 型を指定してください。"

#: config.py:759
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "設定値 `{name}' に `{current.__name__}' 型が指定されています。デフォルト値は `{default.__name__}' です。"

#: config.py:770
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "primary_domain %r が見つかりません。無視します。"

#: config.py:782
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr "v2.0以降、Sphinxはデフォルトで \"index \" をroot_docとして使用しています。conf.pyに \"root_doc = 'contents'\" を追加してください。"

#: domains/rst.py:127 domains/rst.py:184
#, python-format
msgid "%s (directive)"
msgstr "%s (ディレクティブ)"

#: domains/rst.py:185 domains/rst.py:189
#, python-format
msgid ":%s: (directive option)"
msgstr ":%s: (ディレクティブオプション)"

#: domains/rst.py:213
#, python-format
msgid "%s (role)"
msgstr "%s (ロール)"

#: domains/rst.py:223
msgid "directive"
msgstr "ディレクティブ"

#: domains/rst.py:224
msgid "directive-option"
msgstr "ディレクティブオプション"

#: domains/rst.py:225
msgid "role"
msgstr "ロール"

#: domains/rst.py:247
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr "%s の記述 %s はすでに %s で使われています"

#: domains/javascript.py:165
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (組み込み関数)"

#: domains/javascript.py:166 domains/python/__init__.py:253
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (%s のメソッド)"

#: domains/javascript.py:168
#, python-format
msgid "%s() (class)"
msgstr "%s() (クラス)"

#: domains/javascript.py:170
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (グローバル変数または定数)"

#: domains/javascript.py:172 domains/python/__init__.py:338
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (%s の属性)"

#: domains/javascript.py:255
msgid "Arguments"
msgstr "引数"

#: domains/cpp/__init__.py:442 domains/javascript.py:258
msgid "Throws"
msgstr "例外"

#: domains/c/__init__.py:304 domains/cpp/__init__.py:453
#: domains/javascript.py:261 domains/python/_object.py:176
msgid "Returns"
msgstr "戻り値"

#: domains/c/__init__.py:306 domains/javascript.py:263
#: domains/python/_object.py:178
msgid "Return type"
msgstr "戻り値の型"

#: domains/javascript.py:334
#, python-format
msgid "%s (module)"
msgstr "%s (モジュール)"

#: domains/c/__init__.py:675 domains/cpp/__init__.py:854
#: domains/javascript.py:371 domains/python/__init__.py:629
msgid "function"
msgstr "の関数"

#: domains/javascript.py:372 domains/python/__init__.py:633
msgid "method"
msgstr "メソッド"

#: domains/cpp/__init__.py:852 domains/javascript.py:373
#: domains/python/__init__.py:631
msgid "class"
msgstr "クラス"

#: domains/javascript.py:374 domains/python/__init__.py:630
msgid "data"
msgstr "データ"

#: domains/javascript.py:375 domains/python/__init__.py:636
msgid "attribute"
msgstr "の属性"

#: domains/javascript.py:376 domains/python/__init__.py:639
msgid "module"
msgstr "モジュール"

#: domains/javascript.py:407
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr "%s の記述 %s はすでに %s で %s が使われています"

#: domains/changeset.py:25
#, python-format
msgid "Added in version %s"
msgstr ""

#: domains/changeset.py:26
#, python-format
msgid "Changed in version %s"
msgstr "バージョン %s で変更"

#: domains/changeset.py:27
#, python-format
msgid "Deprecated since version %s"
msgstr "バージョン %s で非推奨"

#: domains/changeset.py:28
#, python-format
msgid "Removed in version %s"
msgstr ""

#: domains/__init__.py:299
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: domains/citation.py:73
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr "引用 %s はすでに %s で使われています"

#: domains/citation.py:84
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "引用 [%s] は参照されていません。"

#: domains/math.py:63
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "数式 %s のラベルはすでに %s で使われています"

#: domains/math.py:119 writers/latex.py:2479
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "無効な math_eqref_format: %r"

#: environment/__init__.py:86
msgid "new config"
msgstr "新しい設定"

#: environment/__init__.py:87
msgid "config changed"
msgstr "変更された設定"

#: environment/__init__.py:88
msgid "extensions changed"
msgstr "変更された拡張"

#: environment/__init__.py:249
msgid "build environment version not current"
msgstr "ビルド環境のバージョンが最新ではありません"

#: environment/__init__.py:251
msgid "source directory has changed"
msgstr "ソースディレクトリが変更されました"

#: environment/__init__.py:311
#, python-format
msgid "The configuration has changed (1 option: %r)"
msgstr ""

#: environment/__init__.py:316
#, python-format
msgid "The configuration has changed (%d options: %s)"
msgstr ""

#: environment/__init__.py:322
#, python-format
msgid "The configuration has changed (%d options: %s, ...)"
msgstr ""

#: environment/__init__.py:364
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "この環境は選択したビルダーと互換性がありません。別の doctree ディレクトリーを選択してください。"

#: environment/__init__.py:473
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "%s のドキュメントをスキャンできませんでした: %r "

#: environment/__init__.py:622
#, python-format
msgid "Domain %r is not registered"
msgstr "ドメイン %r はまだ登録されていません"

#: environment/__init__.py:773
msgid "document isn't included in any toctree"
msgstr "ドキュメントはどの toctree にも含まれていません"

#: environment/__init__.py:806
msgid "self referenced toctree found. Ignored."
msgstr "自己参照している toctree が見つかりました。無視します。"

#: environment/__init__.py:835
#, python-format
msgid "document is referenced in multiple toctrees: %s, selecting: %s <- %s"
msgstr ""

#: locale/__init__.py:229
msgid "Attention"
msgstr "注意"

#: locale/__init__.py:230
msgid "Caution"
msgstr "注意"

#: locale/__init__.py:231
msgid "Danger"
msgstr "危険"

#: locale/__init__.py:232
msgid "Error"
msgstr "エラー"

#: locale/__init__.py:233
msgid "Hint"
msgstr "ヒント"

#: locale/__init__.py:234
msgid "Important"
msgstr "重要"

#: locale/__init__.py:235
msgid "Note"
msgstr "注釈"

#: locale/__init__.py:236
msgid "See also"
msgstr "参考"

#: locale/__init__.py:237
msgid "Tip"
msgstr "Tip"

#: locale/__init__.py:238
msgid "Warning"
msgstr "警告"

#: cmd/quickstart.py:43
msgid "automatically insert docstrings from modules"
msgstr "モジュールから自動的に docstring を挿入する"

#: cmd/quickstart.py:44
msgid "automatically test code snippets in doctest blocks"
msgstr "doctest ブロック内のコードスニペットを自動的にテストする"

#: cmd/quickstart.py:45
msgid "link between Sphinx documentation of different projects"
msgstr "異なるプロジェクトのSphinxドキュメント間のリンク"

#: cmd/quickstart.py:46
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr "ビルド時に表示または非表示にできる \"todo\" エントリを書く"

#: cmd/quickstart.py:47
msgid "checks for documentation coverage"
msgstr "ドキュメントの適用範囲を確認する"

#: cmd/quickstart.py:48
msgid "include math, rendered as PNG or SVG images"
msgstr "PNG または SVG 画像としてレンダリングされた数学を含む"

#: cmd/quickstart.py:49
msgid "include math, rendered in the browser by MathJax"
msgstr "MathJax によってブラウザにレンダリングされた数学を含む"

#: cmd/quickstart.py:50
msgid "conditional inclusion of content based on config values"
msgstr "設定値に基づくコンテンツの条件付き包含"

#: cmd/quickstart.py:51
msgid "include links to the source code of documented Python objects"
msgstr "文書化された Python オブジェクトのソースコードへのリンクを含める"

#: cmd/quickstart.py:52
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr "GitHub ページにドキュメントを公開するための .nojekyll ファイルを作成する"

#: cmd/quickstart.py:94
msgid "Please enter a valid path name."
msgstr "有効なパス名を入力してください。"

#: cmd/quickstart.py:110
msgid "Please enter some text."
msgstr "何か入力してください。"

#: cmd/quickstart.py:117
#, python-format
msgid "Please enter one of %s."
msgstr "%sのいずれかを入力してください。 "

#: cmd/quickstart.py:125
msgid "Please enter either 'y' or 'n'."
msgstr "'y' または 'n' を入力してください。"

#: cmd/quickstart.py:131
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "ファイルの拡張子を入力してください。例:  '.rst' または '.txt'。"

#: cmd/quickstart.py:213
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "Sphinx %s クイックスタートユーティリティへようこそ。"

#: cmd/quickstart.py:217
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr "以下の設定値を入力してください（Enter キーのみ押した場合、\nかっこで囲まれた値をデフォルト値として受け入れます）。"

#: cmd/quickstart.py:225
#, python-format
msgid "Selected root path: %s"
msgstr "選択されたルートパス: %s"

#: cmd/quickstart.py:228
msgid "Enter the root path for documentation."
msgstr "ドキュメントのルートパスを入力してください。"

#: cmd/quickstart.py:229
msgid "Root path for the documentation"
msgstr "ドキュメントのルートパス"

#: cmd/quickstart.py:237
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "エラー：選択されたルートパスに既存の conf.py が見つかりました。"

#: cmd/quickstart.py:243
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "sphinx-quickstart は、既存の Sphinx プロジェクトを上書きしません。"

#: cmd/quickstart.py:246
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "新しいルートパスを入力してください（または Enter を押すことで終了します）。"

#: cmd/quickstart.py:256
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr "Sphinx 出力用のビルドディレクトリを配置する方法は2つあります。\nルートパス内にある \"_build\" ディレクトリを使うか、\nルートパス内に \"source\" と \"build\" ディレクトリを分ける方法です。"

#: cmd/quickstart.py:263
msgid "Separate source and build directories (y/n)"
msgstr "ソースディレクトリとビルドディレクトリを分ける（y / n）"

#: cmd/quickstart.py:269
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr "プロジェクトのルートディレクトリに 2つ以上のディレクトリが作成されます。\nカスタマイズしたHTMLテンプレート用の\"_templates\"ディレクトリと、カスタマイズしたスタイルシート等を置く\"_static\"ディレクトリがあります。\nこれらのディレクトリは \"_\" で始まっていますが、別の文字(\".\"など)で始まるように指定できます。"

#: cmd/quickstart.py:275
msgid "Name prefix for templates and static dir"
msgstr "テンプレートと静的ディレクトリの名前プレフィックス"

#: cmd/quickstart.py:280
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "プロジェクト名は、ビルドされたドキュメントのいくつかの場所にあります。"

#: cmd/quickstart.py:284
msgid "Project name"
msgstr "プロジェクト名"

#: cmd/quickstart.py:286
msgid "Author name(s)"
msgstr "著者名（複数可）"

#: cmd/quickstart.py:291
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr "Sphinx には、ソフトウェアに対して \"バージョン\" と \"リリース\" という概念が\nあります。各バージョンは複数のリリースを持つことができます。\n例えば、Python だとバージョンが 2.5 や 3.0 のように分かれているように、\nリリースも 2.5.1 や 3.0a1 のように分けて持つことができます。もしこのような多重構成が必要ない場合は、\n両方を同じ値に設定するだけです。"

#: cmd/quickstart.py:299
msgid "Project version"
msgstr "プロジェクトのバージョン"

#: cmd/quickstart.py:301
msgid "Project release"
msgstr "プロジェクトのリリース"

#: cmd/quickstart.py:306
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr "ドキュメントを英語以外の言語で書く場合は、\n 言語コードで言語を選択できます。Sphinx は生成したテキストをその言語に翻訳します。\n\nサポートされているコードのリストについては、\nhttps://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language を参照してください。"

#: cmd/quickstart.py:315
msgid "Project language"
msgstr "プロジェクトの言語"

#: cmd/quickstart.py:322
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr "ソースファイルのファイル名の拡張子。一般的には、\".txt\"または\".rst \"のどちらかです。この拡張子を持つファイルだけがドキュメントとみなされます。"

#: cmd/quickstart.py:327
msgid "Source file suffix"
msgstr "ソース・ファイルサフィックス"

#: cmd/quickstart.py:332
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr "1つのドキュメントは、\"コンテンツツリー\"の最上位ノードと\n見なされるという点で特別です。つまり、それはドキュメントにおける階層構造のルートである\nということです。通常、これは \"index\" ですが、\n\"index\" ドキュメントがカスタムテンプレートの場合、これを別のファイル名に設定することもできます。"

#: cmd/quickstart.py:340
msgid "Name of your master document (without suffix)"
msgstr "マスター文書の名前（拡張子を除く）"

#: cmd/quickstart.py:350
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr "エラー：マスタファイル %s は、選択されたルートパス上で既に存在します。"

#: cmd/quickstart.py:357
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "sphinx-quickstart は既存のファイルを上書きしません。"

#: cmd/quickstart.py:360
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr "新しいファイル名を入力するか、既存のファイルの名前を変更してEnterキーを押してください。"

#: cmd/quickstart.py:369
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "次の Sphinx 拡張機能のうちどれを有効にするかを指定します。"

#: cmd/quickstart.py:379
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr "注：imgmath と mathjax を同時に有効にすることはできません。 imgmath は未選択になります。"

#: cmd/quickstart.py:389
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr "Makefile と Windows コマンドファイルは生成することができるので、\n後は実行するだけです。例えば、直接 sphinx-build を実行する代わりに `make html` を\n実行します。"

#: cmd/quickstart.py:395
msgid "Create Makefile? (y/n)"
msgstr "Makefile を作成しますか？ （y/n）"

#: cmd/quickstart.py:399
msgid "Create Windows command file? (y/n)"
msgstr "Windows コマンドファイルを作成しますか？（y/n）"

#: cmd/quickstart.py:451 ext/apidoc.py:92
#, python-format
msgid "Creating file %s."
msgstr "ファイル %s を作成しています。"

#: cmd/quickstart.py:456 ext/apidoc.py:89
#, python-format
msgid "File %s already exists, skipping."
msgstr "ファイル %s は既に存在しますのでスキップします。"

#: cmd/quickstart.py:499
msgid "Finished: An initial directory structure has been created."
msgstr "終了：初期ディレクトリ構造が作成されました。"

#: cmd/quickstart.py:502
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr "マスターファイル %s を作成して\n他のドキュメントソースファイルを作成します。"

#: cmd/quickstart.py:510
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr "次のように Makefile を使ってドキュメントを作成します。\n make builder"

#: cmd/quickstart.py:513
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr "次のように、ドキュメントを構築するには sphinx-build コマンドを使用してください。\n sphinx-build -b builder %s %s"

#: cmd/quickstart.py:520
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr "\"builder\" はサポートされているビルダーの 1 つです。 例: html, latex, または linkcheck。"

#: cmd/quickstart.py:555
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr "\nSphinx プロジェクトに必要なファイルを生成します。\n\nsphinx-quickstart は、いくつかの質問であなたの\nプロジェクトを生成するためのディレクトリと、sphinx-build と一緒に使える\nサンプルのMakefileを作成してくれるインタラクティブなツールです。\n"

#: cmd/build.py:153 cmd/quickstart.py:565 ext/apidoc.py:374
#: ext/autosummary/generate.py:765
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr "詳しくは、<https://www.sphinx-doc.org/>を見てください。"

#: cmd/quickstart.py:575
msgid "quiet mode"
msgstr "Quiet モード"

#: cmd/quickstart.py:585
msgid "project root"
msgstr "project root"

#: cmd/quickstart.py:588
msgid "Structure options"
msgstr "構成オプション"

#: cmd/quickstart.py:594
msgid "if specified, separate source and build dirs"
msgstr "記述した場合、ソースとビルドのディレクトリを分割します。"

#: cmd/quickstart.py:600
msgid "if specified, create build dir under source dir"
msgstr "指定された場合、ソースディレクトリの下にビルドディレクトリを作成します。"

#: cmd/quickstart.py:606
msgid "replacement for dot in _templates etc."
msgstr "_templates などのドットの置き換え"

#: cmd/quickstart.py:609
msgid "Project basic options"
msgstr "プロジェクトの基本オプション"

#: cmd/quickstart.py:611
msgid "project name"
msgstr "プロジェクト名"

#: cmd/quickstart.py:614
msgid "author names"
msgstr "著者名"

#: cmd/quickstart.py:621
msgid "version of project"
msgstr "プロジェクトのバージョン"

#: cmd/quickstart.py:628
msgid "release of project"
msgstr "プロジェクトのリリース"

#: cmd/quickstart.py:635
msgid "document language"
msgstr "ドキュメント言語"

#: cmd/quickstart.py:638
msgid "source file suffix"
msgstr "ソース・ファイルサフィックス"

#: cmd/quickstart.py:641
msgid "master document name"
msgstr "マスタードキュメント名"

#: cmd/quickstart.py:644
msgid "use epub"
msgstr "epubを利用する"

#: cmd/quickstart.py:647
msgid "Extension options"
msgstr "拡張オプション"

#: cmd/quickstart.py:654 ext/apidoc.py:578
#, python-format
msgid "enable %s extension"
msgstr "%s 拡張を有効にする"

#: cmd/quickstart.py:661 ext/apidoc.py:570
msgid "enable arbitrary extensions"
msgstr "任意の拡張を有効にする"

#: cmd/quickstart.py:664
msgid "Makefile and Batchfile creation"
msgstr "Makefileとbatファイルの生成オプション"

#: cmd/quickstart.py:670
msgid "create makefile"
msgstr "makefileを作成する"

#: cmd/quickstart.py:676
msgid "do not create makefile"
msgstr "makefileを作成しない"

#: cmd/quickstart.py:683
msgid "create batchfile"
msgstr "batファイルを作成する"

#: cmd/quickstart.py:689
msgid "do not create batchfile"
msgstr "batファイルを作成しない"

#: cmd/quickstart.py:698
msgid "use make-mode for Makefile/make.bat"
msgstr "Makefile / make.bat 向けに make-mode を使う"

#: cmd/quickstart.py:701 ext/apidoc.py:581
msgid "Project templating"
msgstr "プロジェクトテンプレート"

#: cmd/quickstart.py:707 ext/apidoc.py:587
msgid "template directory for template files"
msgstr "テンプレートファイルのテンプレートディレクトリ"

#: cmd/quickstart.py:714
msgid "define a template variable"
msgstr "テンプレート変数の定義"

#: cmd/quickstart.py:749
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr "\"quiet\" が指定されていますが、 \"project\" または \"author\" のいずれも指定されていません。"

#: cmd/quickstart.py:768
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr "エラー：指定されたパスはディレクトリではないか、または sphinx ファイルが既に存在します。"

#: cmd/quickstart.py:775
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr "sphinx-quickstart は空のディレクトリにのみ生成します。新しいルートパスを指定してください。"

#: cmd/quickstart.py:793
#, python-format
msgid "Invalid template variable: %s"
msgstr "無効なテンプレート変数: %s"

#: cmd/build.py:49
msgid "Exception occurred while building, starting debugger:"
msgstr "ビルド中に例外が発生しました。デバッガを起動します:"

#: _cli/util/errors.py:129 cmd/build.py:65
msgid "Interrupted!"
msgstr "割り込まれました！"

#: cmd/build.py:67
msgid "reST markup error:"
msgstr "reST マークアップエラー:"

#: _cli/util/errors.py:143 cmd/build.py:73
msgid "Encoding error:"
msgstr "エンコードエラー:"

#: cmd/build.py:78 cmd/build.py:108
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr "完全なトレースバックを%sに保存しました。問題を開発者に報告するときに添付してください。"

#: _cli/util/errors.py:148 cmd/build.py:90
msgid "Recursion error:"
msgstr "再起呼び出しエラー:"

#: _cli/util/errors.py:152 cmd/build.py:94
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr "これは、非常に大きなソースファイルや深くネストされたソースファイルで発生する可能性があります。conf.py で、Python のデフォルトの再帰回数制限である 1000 を、例えば次のように慎重に増やすことができます: "

#: _cli/util/errors.py:165 cmd/build.py:103
msgid "Exception occurred:"
msgstr "例外が発生しました"

#: _cli/util/errors.py:178 cmd/build.py:117
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr "次期バージョンでのエラーメッセージ改善のために、ユーザーエラーの場合にも報告してください。"

#: cmd/build.py:124
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr "バグ報告はこちらにお願いします <https://github.com/sphinx-doc/sphinx/issues> 。ご協力ありがとうございます！"

#: cmd/build.py:144
msgid "job number should be a positive number"
msgstr "ジョブ番号は正数でなければなりません"

#: cmd/build.py:154
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr "\nソースファイルからドキュメントを生成します。\n\nsphinx-build は、SOURCEDIR 内のファイルをもとにドキュメントを生成し、\nOUTPUTDIR 内に配置します。またコンフィグ\n設定用に SOURCEDIR 内から\n 'conf.py' を探します。'sphinx-quickstart' ツールを使うと\n 'conf.py' を含むテンプレートファイルを生成することができます。\n\nsphinx-build は、さまざまな形式のドキュメントを作成することができます。フォーマットは、\nコマンドラインでビルダー名を指定して選択します。デフォルトは\nHTML です。ビルダーはドキュメント化処理に関連した他のタスクも実行できます。\n\nデフォルトでは、古いものはすべてビルドされています。個別にファイル名を指定することで、\n選択したファイルのみ出力することもできます。\n"

#: cmd/build.py:180
msgid "path to documentation source files"
msgstr "ドキュメントソースファイルへのパス"

#: cmd/build.py:183
msgid "path to output directory"
msgstr "出力先ディレクトリへのパス"

#: cmd/build.py:188
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr ""

#: cmd/build.py:194
msgid "general options"
msgstr "一般的なオプション"

#: cmd/build.py:201
msgid "builder to use (default: 'html')"
msgstr ""

#: cmd/build.py:210
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: cmd/build.py:220
msgid "write all files (default: only write new and changed files)"
msgstr "すべてのファイルに書き込む（デフォルト: 新規ファイルまたは変更されたファイルのみ）"

#: cmd/build.py:227
msgid "don't use a saved environment, always read all files"
msgstr "保存された環境は使わず、常に全てのファイルを読み込む"

#: cmd/build.py:230
msgid "path options"
msgstr ""

#: cmd/build.py:236
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr ""

#: cmd/build.py:246
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr ""

#: cmd/build.py:257
msgid "use no configuration file, only use settings from -D options"
msgstr ""

#: cmd/build.py:266
msgid "override a setting in configuration file"
msgstr "設定ファイルの設定を上書きする"

#: cmd/build.py:275
msgid "pass a value into HTML templates"
msgstr "HTMLテンプレートに値を渡す"

#: cmd/build.py:284
msgid "define tag: include \"only\" blocks with TAG"
msgstr "定義タグ: TAG ブロック\"のみ\"含む"

#: cmd/build.py:291
msgid "nitpicky mode: warn about all missing references"
msgstr ""

#: cmd/build.py:294
msgid "console output options"
msgstr "コンソール出力オプション"

#: cmd/build.py:301
msgid "increase verbosity (can be repeated)"
msgstr "精度の増加（繰り返し可能）"

#: cmd/build.py:308 ext/apidoc.py:413
msgid "no output on stdout, just warnings on stderr"
msgstr "標準出力には出力せず、標準エラー出力に警告を出すのみ"

#: cmd/build.py:315
msgid "no output at all, not even warnings"
msgstr "何も出力せず、警告もしない"

#: cmd/build.py:323
msgid "do emit colored output (default: auto-detect)"
msgstr "色分けで出力する（デフォルト：自動検出）"

#: cmd/build.py:331
msgid "do not emit colored output (default: auto-detect)"
msgstr "色分けの出力をしない（デフォルト：自動検出）"

#: cmd/build.py:334
msgid "warning control options"
msgstr ""

#: cmd/build.py:340
msgid "write warnings (and errors) to given file"
msgstr "指定ファイルに警告（およびエラー）を書き込む"

#: cmd/build.py:347
msgid "turn warnings into errors"
msgstr "警告をエラーとして扱う"

#: cmd/build.py:355
msgid "show full traceback on exception"
msgstr "例外時にフルトレースバックを表示する"

#: cmd/build.py:358
msgid "run Pdb on exception"
msgstr "例外が発生したときにPdbを実行する"

#: cmd/build.py:364
msgid "raise an exception on warnings"
msgstr ""

#: cmd/build.py:407
msgid "cannot combine -a option and filenames"
msgstr "-aオプションとファイル名を組み合わせることはできません"

#: cmd/build.py:439
#, python-format
msgid "cannot open warning file %r: %s"
msgstr "警告ファイル %r を開けません: %s"

#: cmd/build.py:458
msgid "-D option argument must be in the form name=value"
msgstr "-Dオプション引数は name = value の形式でなければなりません"

#: cmd/build.py:465
msgid "-A option argument must be in the form name=value"
msgstr "-Aオプション引数は name = value の形式でなければなりません"

#: builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "dummyビルダーはファイルを出力しません"

#: builders/linkcheck.py:60
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr "上記の出力結果、または %(outdir)s /output.txt を見てエラーを確認してください"

#: builders/linkcheck.py:137
#, python-format
msgid "broken link: %s (%s)"
msgstr "リンクが切れています: %s (%s)"

#: builders/linkcheck.py:526
#, python-format
msgid "Anchor '%s' not found"
msgstr "アンカー '%s' が見つかりません"

#: builders/linkcheck.py:726
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr "linkcheck_allowed_redirects 内の正規表現のコンパイルに失敗しました: %r %s"

#: builders/singlehtml.py:36
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "HTML ページは%(outdir)sにあります。"

#: builders/singlehtml.py:168
msgid "assembling single document"
msgstr "ドキュメントを1ページにまとめています"

#: builders/latex/__init__.py:349 builders/manpage.py:59
#: builders/singlehtml.py:173 builders/texinfo.py:120
msgid "writing"
msgstr "書き込み中"

#: builders/singlehtml.py:186
msgid "writing additional files"
msgstr "追加のファイルを出力"

#: builders/manpage.py:39
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "マニュアルページは %(outdir)s にあります。"

#: builders/manpage.py:47
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr "設定値 \"man_pages\" が見つかりません。マニュアルページは書かれません"

#: builders/manpage.py:76
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "設定値 \"man_pages\" が不明なドキュメント %s を参照しています"

#: builders/text.py:34
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "テキストファイルは%(outdir)sにあります。"

#: builders/html/__init__.py:1213 builders/text.py:81 builders/xml.py:97
#, python-format
msgid "error writing file %s: %s"
msgstr "ファイル書き込みエラー %s: %s"

#: builders/xml.py:38
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "XMLファイルは%(outdir)sにあります。"

#: builders/xml.py:110
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "pseudo-XMLファイルは%(outdir)sにあります。"

#: builders/texinfo.py:47
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "Texinfoファイルは%(outdir)sにあります。"

#: builders/texinfo.py:49
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr "\nmakeinfo コマンドで処理するため、そのディレクトリで 'make' を実行してください。\n（これを自動的に行うには、ここで 'make info' を使用してください）。"

#: builders/texinfo.py:78
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "設定値 \"texinfo_documents\" が見つかりません。ドキュメントは書き込まれません"

#: builders/texinfo.py:90
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "設定値 \"texinfo_documents\" は、不明なドキュメント %s を参照しています"

#: builders/latex/__init__.py:327 builders/texinfo.py:114
#, python-format
msgid "processing %s"
msgstr "処理中 %s"

#: builders/latex/__init__.py:407 builders/texinfo.py:173
msgid "resolving references..."
msgstr "参照を解決しています..."

#: builders/latex/__init__.py:418 builders/texinfo.py:183
msgid " (in "
msgstr " (in "

#: builders/_epub_base.py:421 builders/html/__init__.py:757
#: builders/latex/__init__.py:485 builders/texinfo.py:201
msgid "copying images... "
msgstr "画像をコピー中... "

#: builders/_epub_base.py:443 builders/latex/__init__.py:500
#: builders/texinfo.py:218
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "画像ファイル %r をコピーできません: %s"

#: builders/texinfo.py:225
msgid "copying Texinfo support files"
msgstr "Texinfo 関連ファイルをコピーしています"

#: builders/texinfo.py:233
#, python-format
msgid "error writing file Makefile: %s"
msgstr "Makefile の書き込みエラー: %s"

#: builders/gettext.py:230
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "メッセージカタログは%(outdir)sにあります。"

#: builders/__init__.py:371 builders/gettext.py:251
#, python-format
msgid "building [%s]: "
msgstr "ビルド中 [%s]: "

#: builders/gettext.py:252
#, python-format
msgid "targets for %d template files"
msgstr "指定された %d 件のテンプレートファイル"

#: builders/gettext.py:257
msgid "reading templates... "
msgstr "テンプレートの読み込み中..."

#: builders/gettext.py:292
msgid "writing message catalogs... "
msgstr "メッセージカタログを出力中... "

#: builders/__init__.py:200
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr "%sビルダー向けの画像形式が見つかりません: %s (%s)"

#: builders/__init__.py:208
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr "%sビルダー向けの画像形式が見つかりません: %s"

#: builders/__init__.py:231
msgid "building [mo]: "
msgstr "ビルド中 [mo]: "

#: builders/__init__.py:234 builders/__init__.py:729 builders/__init__.py:761
msgid "writing output... "
msgstr "出力中..."

#: builders/__init__.py:251
#, python-format
msgid "all of %d po files"
msgstr "全%d件のpoファイル"

#: builders/__init__.py:273
#, python-format
msgid "targets for %d po files that are specified"
msgstr "指定された %d 件のpoファイル"

#: builders/__init__.py:285
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "更新された %d 件のpoファイル"

#: builders/__init__.py:295
msgid "all source files"
msgstr "全てのソースファイル"

#: builders/__init__.py:307
#, python-format
msgid "file %r given on command line does not exist, "
msgstr "コマンドラインに指定されたファイル %r がないため, "

#: builders/__init__.py:313
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "コマンドラインに指定されたファイル %r はソースディレクトリ以下にないため無視されます"

#: builders/__init__.py:324
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr ""

#: builders/__init__.py:339
#, python-format
msgid "%d source files given on command line"
msgstr "コマンドラインで指定された%d件のソースファイル"

#: builders/__init__.py:354
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "更新された %d 件のソースファイル"

#: builders/__init__.py:382
msgid "looking for now-outdated files... "
msgstr "更新されたファイルを探しています... "

#: builders/__init__.py:386
#, python-format
msgid "%d found"
msgstr "%d 件見つかりました"

#: builders/__init__.py:388
msgid "none found"
msgstr "見つかりませんでした"

#: builders/__init__.py:395
msgid "pickling environment"
msgstr "環境データを保存中"

#: builders/__init__.py:402
msgid "checking consistency"
msgstr "整合性をチェック中"

#: builders/__init__.py:406
msgid "no targets are out of date."
msgstr "更新が必要な対象はありませんでした"

#: builders/__init__.py:446
msgid "updating environment: "
msgstr "環境データを更新中"

#: builders/__init__.py:471
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%s 件追加, %s 件更新, %s 件削除"

#: builders/__init__.py:507
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches a "
"built-in exclude pattern %r. Please move your master document to a different"
" location."
msgstr ""

#: builders/__init__.py:516
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches an "
"exclude pattern specified in conf.py, %r. Please remove this pattern from "
"conf.py."
msgstr ""

#: builders/__init__.py:527
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it is not included"
" in the custom include_patterns = %r. Ensure that a pattern in "
"include_patterns matches the master document."
msgstr ""

#: builders/__init__.py:534
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s). The master document must "
"be within the source directory or a subdirectory of it."
msgstr ""

#: builders/__init__.py:553 builders/__init__.py:569
msgid "reading sources... "
msgstr "ソースを読み込み中..."

#: builders/__init__.py:686
#, python-format
msgid "docnames to write: %s"
msgstr "書き込むdocname: %s"

#: builders/__init__.py:699
msgid "preparing documents"
msgstr "ドキュメントの出力準備中"

#: builders/__init__.py:702
msgid "copying assets"
msgstr ""

#: builders/__init__.py:845
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr "デコードできないソース文字です。\"?\" に置き換えます: %r"

#: builders/epub3.py:83
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "ePubファイルは%(outdir)sにあります。"

#: builders/epub3.py:189
msgid "writing nav.xhtml file..."
msgstr "nav.xhtml を書き込み中..."

#: builders/epub3.py:220
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "EPUB3出力では設定値 \"epub_language\" (あるいは \"language\") の指定が必要です"

#: builders/epub3.py:227
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "EPUB3では設定値  \"epub_uid\" はXML NAMEにするべきです"

#: builders/epub3.py:231
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "EPUB3出力では設定値 \"epub_title\" (あるいは \"html_title\") の指定が必要です"

#: builders/epub3.py:238
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "EPUB3出力では設定値 \"epub_author\" の指定が必要です"

#: builders/epub3.py:242
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "EPUB3出力では設定値 \"epub_contributor\" が必要です"

#: builders/epub3.py:247
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "EPUB3出力では設定値 \"epub_description\" が必要です"

#: builders/epub3.py:251
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "EPUB3出力では設定値 \"epub_publisher\" の指定が必要です"

#: builders/epub3.py:255
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "EPUB3出力では設定値 \"epub_copyright\" (あるいは \"copyright\") の指定が必要です"

#: builders/epub3.py:262
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "EPUB3出力では設定値 \"epub_identifier\" の指定が必要です"

#: builders/epub3.py:265
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "EPUB3出力では設定値 \"version\" が必要です"

#: builders/epub3.py:279 builders/html/__init__.py:1262
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "無効な css_file %r は無視されました"

#: builders/_epub_base.py:220
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "Tocエントリーが重複しています: %s"

#: builders/_epub_base.py:432
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "画像ファイル %r をPILで読み込めないため、そのままコピーします"

#: builders/_epub_base.py:463
#, python-format
msgid "cannot write image file %r: %s"
msgstr "画像ファイル %r を書き込めません: %s"

#: builders/_epub_base.py:475
msgid "Pillow not found - copying image files"
msgstr "Pillowがインストールされていません。代わりに画像をコピーします"

#: builders/_epub_base.py:507
msgid "writing mimetype file..."
msgstr "mimetype を書き込み中..."

#: builders/_epub_base.py:516
msgid "writing META-INF/container.xml file..."
msgstr "META-INF/container.xml を書き込み中..."

#: builders/_epub_base.py:553
msgid "writing content.opf file..."
msgstr "content.opf を書き込み中..."

#: builders/_epub_base.py:585
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "不明なmimetype %sのため無視します"

#: builders/_epub_base.py:756
msgid "writing toc.ncx file..."
msgstr "tox.ncx を書き込み中..."

#: builders/_epub_base.py:785
#, python-format
msgid "writing %s file..."
msgstr "ファイル %s を書き込み中..."

#: builders/changes.py:33
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "ファイルは%(outdir)sにあります"

#: builders/changes.py:60
#, python-format
msgid "no changes in version %s."
msgstr "バージョン %s での変更はありません"

#: builders/changes.py:62
msgid "writing summary file..."
msgstr "概要ファイルを書き出し中..."

#: builders/changes.py:77
msgid "Builtins"
msgstr "組み込み"

#: builders/changes.py:79
msgid "Module level"
msgstr "モジュールレベル"

#: builders/changes.py:131
msgid "copying source files..."
msgstr "ソースファイルをコピー中..."

#: builders/changes.py:140
#, python-format
msgid "could not read %r for changelog creation"
msgstr "Changelog作成中に %r を読み込めませんでした"

#: util/rst.py:72
#, python-format
msgid "default role %s not found"
msgstr "デフォルトのロール %s が見つかりません"

#: util/docfields.py:95
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr ""

#: util/osutil.py:130
#, python-format
msgid ""
"Aborted attempted copy from %s to %s (the destination path has existing "
"data)."
msgstr ""

#: util/nodes.py:419
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr ""

#: util/nodes.py:487
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr "toctree に存在しないファイルへの参照が含まれています %r"

#: util/nodes.py:701
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr "only ディレクティブの条件式の評価中に例外が発生しました: %s"

#: util/fileutil.py:76
#, python-format
msgid ""
"Aborted attempted copy from rendered template %s to %s (the destination path"
" has existing data)."
msgstr ""

#: util/fileutil.py:91
#, python-format
msgid "Writing evaluated template result to %s"
msgstr ""

#: util/inventory.py:170
#, python-format
msgid "inventory <%s> contains duplicate definitions of %s"
msgstr ""

#: util/inventory.py:185
#, python-format
msgid "inventory <%s> contains multiple definitions for %s"
msgstr ""

#: util/docutils.py:283
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr "不明なディレクティブまたはロール名: %s:%s"

#: util/docutils.py:746
#, python-format
msgid "unknown node type: %r"
msgstr "不明なノードタイプ: %r"

#: util/display.py:83
msgid "skipped"
msgstr "スキップしました"

#: util/display.py:88
msgid "failed"
msgstr "失敗しました"

#: util/i18n.py:105
#, python-format
msgid "reading error: %s, %s"
msgstr "読み取りエラー: %s, %s"

#: util/i18n.py:112
#, python-format
msgid "writing error: %s, %s"
msgstr "書き込みエラー: %s, %s"

#: util/i18n.py:141
#, python-format
msgid "locale_dir %s does not exist"
msgstr ""

#: util/i18n.py:236
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr "日付形式が無効です。直接出力したい場合は、文字列を一重引用符で囲みます: %s"

#: directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr "csv-table ディレクティブの \":file:\" オプションは、絶対パスをソースディレクトリからの相対パスとして認識するようになりました。ドキュメントを更新してください。"

#: directives/code.py:63
msgid "non-whitespace stripped by dedent"
msgstr "デデントによる空白の除去"

#: directives/code.py:84
#, python-format
msgid "Invalid caption: %s"
msgstr "不正な caption です: %s"

#: directives/code.py:129 directives/code.py:291 directives/code.py:478
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr "行番号の指定が範囲外です (1-%d): %r"

#: directives/code.py:211
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "\"%s\" と \"%s\" のオプションは同時に使用できません"

#: directives/code.py:225
#, python-format
msgid "Include file %r not found or reading it failed"
msgstr "インクルードファイル %r が見つからないか読み込めません"

#: directives/code.py:228
#, python-format
msgid ""
"Encoding %r used for reading included file %r seems to be wrong, try giving "
"an :encoding: option"
msgstr "エンコーディング %r はインクルードファイル %r の読み込みに適さないようです。:encoding: オプションを追加してみてください"

#: directives/code.py:270
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "%r という名前のオブジェクトがインクルードファイル %r 内に見つかりません"

#: directives/code.py:303
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr " \"lineno-match\" は不連続な \"lines\" に対して使用できません"

#: directives/code.py:308
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr "指定された %r に一致する行がインクルードファイル %r にありませんでした"

#: directives/other.py:122
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr "toctree グローブ・パターン %r はどのドキュメントにもマッチしませんでした。"

#: directives/other.py:155 environment/adapters/toctree.py:355
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr "toctree に除外したドキュメントへの参照が含まれています %r"

#: directives/other.py:158 environment/adapters/toctree.py:359
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr "toctree に存在しないドキュメントへの参照が含まれています %r"

#: directives/other.py:171
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr "toctree で重複したエントリが見つかりました: %s"

#: directives/other.py:204
msgid "Section author: "
msgstr "この節の作者: "

#: directives/other.py:206
msgid "Module author: "
msgstr "モジュールの作者: "

#: directives/other.py:208
msgid "Code author: "
msgstr "コードの作者: "

#: directives/other.py:210
msgid "Author: "
msgstr "作者: "

#: directives/other.py:284
msgid ".. acks content is not a list"
msgstr ""

#: directives/other.py:309
msgid ".. hlist content is not a list"
msgstr ""

#: _cli/__init__.py:73
msgid "Usage:"
msgstr ""

#: _cli/__init__.py:75
msgid "{0} [OPTIONS] <COMMAND> [<ARGS>]"
msgstr ""

#: _cli/__init__.py:78
msgid "  The Sphinx documentation generator."
msgstr ""

#: _cli/__init__.py:87
msgid "Commands:"
msgstr ""

#: _cli/__init__.py:98
msgid "Options"
msgstr "オプション"

#: _cli/__init__.py:112 _cli/__init__.py:183
msgid "For more information, visit https://www.sphinx-doc.org/en/master/man/."
msgstr ""

#: _cli/__init__.py:172
msgid ""
"{0}: error: {1}\n"
"Run '{0} --help' for information"
msgstr ""

#: _cli/__init__.py:182
msgid "   Manage documentation with Sphinx."
msgstr ""

#: _cli/__init__.py:194
msgid "Show the version and exit."
msgstr ""

#: _cli/__init__.py:202
msgid "Show this message and exit."
msgstr ""

#: _cli/__init__.py:206
msgid "Logging"
msgstr ""

#: _cli/__init__.py:213
msgid "Increase verbosity (can be repeated)"
msgstr ""

#: _cli/__init__.py:221
msgid "Only print errors and warnings."
msgstr ""

#: _cli/__init__.py:228
msgid "No output at all"
msgstr ""

#: _cli/__init__.py:234
msgid "<command>"
msgstr ""

#: _cli/__init__.py:265
msgid "See 'sphinx --help'.\n"
msgstr ""

#: builders/html/__init__.py:478 builders/latex/__init__.py:201
#: transforms/__init__.py:133 writers/manpage.py:101 writers/texinfo.py:218
#, python-format
msgid "%b %d, %Y"
msgstr "%Y年%m月%d日"

#: transforms/__init__.py:143
msgid "could not calculate translation progress!"
msgstr ""

#: transforms/__init__.py:148
msgid "no translated elements!"
msgstr ""

#: transforms/__init__.py:267
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr "4列ベースのインデックスが見つかりました。あなたが使っている拡張子のバグかもしれません: %r"

#: transforms/__init__.py:313
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr "Footnote [%s] は参照されていません。"

#: transforms/__init__.py:322
msgid "Footnote [*] is not referenced."
msgstr ""

#: transforms/__init__.py:333
msgid "Footnote [#] is not referenced."
msgstr "Footnote [#] は参照されていません。"

#: transforms/i18n.py:229 transforms/i18n.py:304
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr "翻訳されたメッセージの footnote 参照が矛盾しています。原文: {0}、翻訳: {1}"

#: transforms/i18n.py:274
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr "翻訳されたメッセージの参照が矛盾しています。原文: {0}、翻訳: {1}"

#: transforms/i18n.py:324
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr "翻訳されたメッセージの引用参照が矛盾しています。原文: {0}、翻訳: {1}"

#: transforms/i18n.py:346
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr "翻訳されたメッセージの用語参照が矛盾しています。原文: {0}、翻訳: {1}"

#: ext/linkcode.py:75 ext/viewcode.py:200
msgid "[source]"
msgstr "[ソース]"

#: ext/imgconverter.py:40
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr ""

#: ext/imgconverter.py:49 ext/imgconverter.py:73
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "変換処理はエラー終了しました:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/imgconverter.py:68
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr "convert コマンド %r は実行できません。image_converter の設定を確認してください"

#: ext/viewcode.py:257
msgid "highlighting module code... "
msgstr "モジュールコードをハイライトしています..."

#: ext/viewcode.py:285
msgid "[docs]"
msgstr "[ドキュメント]"

#: ext/viewcode.py:305
msgid "Module code"
msgstr "モジュールコード"

#: ext/viewcode.py:311
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>%s のソースコード</h1>"

#: ext/viewcode.py:337
msgid "Overview: module code"
msgstr "概要: モジュールコード"

#: ext/viewcode.py:338
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>全モジュールのうち、コードを読めるもの</h1>"

#: ext/coverage.py:47
#, python-format
msgid "invalid regex %r in %s"
msgstr "無効な正規表現 %r が %s 内に見つかりました"

#: ext/coverage.py:134 ext/coverage.py:280
#, python-format
msgid "module %s could not be imported: %s"
msgstr "モジュール %s をインポートできませんでした: %s"

#: ext/coverage.py:141
#, python-format
msgid ""
"the following modules are documented but were not specified in "
"coverage_modules: %s"
msgstr ""

#: ext/coverage.py:149
msgid ""
"the following modules are specified in coverage_modules but were not "
"documented"
msgstr ""

#: ext/coverage.py:163
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr "ソース内のカバレッジのテストが終了したら、%(outdir)spython.txt の結果を確認してください。"

#: ext/coverage.py:177
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr "coverage_c_regexes 内に無効な正規表現 %r があります"

#: ext/coverage.py:245
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr ""

#: ext/coverage.py:429
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr ""

#: ext/coverage.py:445
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr ""

#: ext/coverage.py:458
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr ""

#: ext/todo.py:71
msgid "Todo"
msgstr "課題"

#: ext/todo.py:104
#, python-format
msgid "TODO entry found: %s"
msgstr "TODO エントリーが見つかりました: %s"

#: ext/todo.py:163
msgid "<<original entry>>"
msgstr "<<original entry>>"

#: ext/todo.py:165
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(<<original entry>> は、 %s の %d 行目です)"

#: ext/todo.py:175
msgid "original entry"
msgstr "元のエントリ"

#: ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr "ハードコードされたリンク %r は 拡張リンクに置き換えられる可能性があります (代わりに %r を使用してみてください)。"

#: ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr "'%s' オプション内に '+' または '-' が不足しています"

#: ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr "'%s' は正しいオプションではありません"

#: ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "'%s' は正しい pyversion オプションではありません"

#: ext/doctest.py:220
msgid "invalid TestCode type"
msgstr "無効な TestCode タイプ"

#: ext/doctest.py:281
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr "ソース内の doctests のテストが終了したら、%(outdir)s/output.txt の結果を確認してください。"

#: ext/doctest.py:434
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr "%sブロックにあるコード/出力 が %s にありません: %s"

#: ext/doctest.py:522
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr "無効な doctest コードは無視されます: %r"

#: ext/graphviz.py:135
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr "Graphviz ディレクティブはコンテンツとファイル名の両方の引数を持つことは出来ません"

#: ext/graphviz.py:145
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "外部の Graphviz ファイル %r が見つからないか読み込めません"

#: ext/graphviz.py:152
msgid "Ignoring \"graphviz\" directive without content."
msgstr "コンテンツのない \"graphviz\" ディレクティブを無視します"

#: ext/graphviz.py:268
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr ""

#: ext/graphviz.py:303
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr "dot コマンド %r は実行できません (graphviz 出力のために必要です)。graphviz_dot の設定を確認してください"

#: ext/graphviz.py:310
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dot はエラー終了しました:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/graphviz.py:313
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dotは出力ファイルを生成しませんでした:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/graphviz.py:329
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr "graphviz_output_format は %r ではなく 'png' か 'svg' でなければなりません"

#: ext/graphviz.py:333 ext/graphviz.py:386 ext/graphviz.py:423
#, python-format
msgid "dot code %r: %s"
msgstr "dot コード %r: %s"

#: ext/graphviz.py:436 ext/graphviz.py:444
#, python-format
msgid "[graph: %s]"
msgstr "[グラフ: %s]"

#: ext/graphviz.py:438 ext/graphviz.py:446
msgid "[graph]"
msgstr "[グラフ]"

#: ext/imgmath.py:369 ext/mathjax.py:52
msgid "Link to this equation"
msgstr ""

#: ext/apidoc.py:85
#, python-format
msgid "Would create file %s."
msgstr "ファイル %s を作成したものとします。"

#: ext/apidoc.py:375
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr "\n<MODULE_PATH> 内を再帰的に調べてPython のモジュールとパッケージ、\n後は1つのreST ファイルを <OUTPUT_PATH> 内にあるパッケージ毎の automodule ディレクティブに作成します。\n\n<EXCLUDE_PATTERN> は、ファイル、またはディレクトリ、または両方のパターンを\n生成処理から除外することができます。\n\n注：デフォルトでは、このスクリプトはすでに作成されているファイルを上書きしません。"

#: ext/apidoc.py:392
msgid "path to module to document"
msgstr "ドキュメントへのモジュールパス"

#: ext/apidoc.py:396
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr "生成処理から除外するための、ファイル、ディレクトリ、または両方のパターンを記した fnmatch-style 形式"

#: ext/apidoc.py:407
msgid "directory to place all output"
msgstr "すべての生成データを配置するディレクトリ"

#: ext/apidoc.py:422
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "目次に表示するサブモジュールの最大深度 (デフォルト: 4)"

#: ext/apidoc.py:429
msgid "overwrite existing files"
msgstr "存在するファイルは上書きする"

#: ext/apidoc.py:437
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr "シンボリックリンクをたどります。collective.recipe.omeletteと組み合わせると強力です。"

#: ext/apidoc.py:446
msgid "run the script without creating files"
msgstr "ファイルを作成せずにスクリプトを実行する"

#: ext/apidoc.py:453
msgid "put documentation for each module on its own page"
msgstr "各モジュールのドキュメントをそれぞれのページに配置する"

#: ext/apidoc.py:460
msgid "include \"_private\" modules"
msgstr "\"_private\" モジュールを含めます。"

#: ext/apidoc.py:467
msgid "filename of table of contents (default: modules)"
msgstr "目次のファイル名 (デフォルト: モジュール)"

#: ext/apidoc.py:474
msgid "don't create a table of contents file"
msgstr "目次ファイルを生成しない"

#: ext/apidoc.py:481
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr "module/package パッケージの見出しを生成しない (例: docstring にすでにそれらが含まれている場合など)"

#: ext/apidoc.py:492
msgid "put module documentation before submodule documentation"
msgstr "サブモジュールのドキュメントの前に、モジュールのドキュメントを置く"

#: ext/apidoc.py:498
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr "PEP-0420 暗黙の名前空間の指定に従って、モジュールパスを解釈する"

#: ext/apidoc.py:508
msgid "file suffix (default: rst)"
msgstr "ファイルの拡張子 (デフォルト: rst)"

#: ext/apidoc.py:515 ext/autosummary/generate.py:838
msgid "Remove existing files in the output directory that were not generated"
msgstr ""

#: ext/apidoc.py:524
msgid "generate a full project with sphinx-quickstart"
msgstr "sphinx-quickstart を使って完全なプロジェクトを生成する"

#: ext/apidoc.py:531
msgid "append module_path to sys.path, used when --full is given"
msgstr "module_pathを sys.path に追加します。--full が与えられたときに使用されます。"

#: ext/apidoc.py:538
msgid "project name (default: root module name)"
msgstr "プロジェクト名 (デフォルト: ルートモジュール名)"

#: ext/apidoc.py:545
msgid "project author(s), used when --full is given"
msgstr "プロジェクト著者名(複数可)。--full が与えられたときに使用されます。"

#: ext/apidoc.py:552
msgid "project version, used when --full is given"
msgstr "プロジェクトバージョン。--full が与えられたときに使用されます。"

#: ext/apidoc.py:559
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "プロジェクトのリリースバージョン。--full が与えられたときに使用されます。デフォルトは --doc-version"

#: ext/apidoc.py:564
msgid "extension options"
msgstr "拡張オプション"

#: ext/apidoc.py:638
#, python-format
msgid "%s is not a directory."
msgstr "%s はディレクトリではありません。"

#: ext/apidoc.py:710 ext/autosummary/generate.py:874
#, python-format
msgid "Failed to remove %s: %s"
msgstr ""

#: ext/autosectionlabel.py:48
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr ""

#: domains/std/__init__.py:702 domains/std/__init__.py:808
#: ext/autosectionlabel.py:52
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "ラベル %s はすでに %s で使われています"

#: ext/duration.py:85
msgid ""
"====================== slowest reading durations ======================="
msgstr "====================== 最も遅い読み取り時間 ======================="

#: ext/imgmath.py:159
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr "LaTeX コマンド %r を実行できません (数式表示のために必要です)。imgmath_latex の設定を確認してください"

#: ext/imgmath.py:174
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr "%s コマンド %r を実行できません (数式表示のために必要です)。imgmath_%s の設定を確認してください"

#: ext/imgmath.py:328
#, python-format
msgid "display latex %r: %s"
msgstr "latex の表示 %r: %s"

#: ext/imgmath.py:362
#, python-format
msgid "inline latex %r: %s"
msgstr "latex のインライン表示 %r: %s"

#: writers/latex.py:1093 writers/manpage.py:262 writers/texinfo.py:660
msgid "Footnotes"
msgstr "注記"

#: writers/manpage.py:308 writers/text.py:935
#, python-format
msgid "[image: %s]"
msgstr "[画像: %s]"

#: writers/manpage.py:309 writers/text.py:936
msgid "[image]"
msgstr "[画像]"

#: writers/html5.py:99 writers/html5.py:108
msgid "Link to this definition"
msgstr ""

#: writers/html5.py:415
#, python-format
msgid "numfig_format is not defined for %s"
msgstr "%s に numfig_format は定義されていません"

#: writers/html5.py:427
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr "いくつかの ID が %s ノードに割り当てられていません"

#: writers/html5.py:482
msgid "Link to this term"
msgstr ""

#: writers/html5.py:525 writers/html5.py:530
msgid "Link to this heading"
msgstr ""

#: writers/html5.py:535
msgid "Link to this table"
msgstr ""

#: writers/html5.py:549 writers/latex.py:1102
#, python-format
msgid "unsupported rubric heading level: %s"
msgstr ""

#: writers/html5.py:613
msgid "Link to this code"
msgstr ""

#: writers/html5.py:615
msgid "Link to this image"
msgstr ""

#: writers/html5.py:617
msgid "Link to this toctree"
msgstr ""

#: writers/html5.py:759
msgid "Could not obtain image size. :scale: option is ignored."
msgstr "画像サイズを取得できませんでした。:scale: オプションは無視されます。"

#: builders/latex/__init__.py:208 domains/std/__init__.py:645
#: domains/std/__init__.py:657 templates/latex/latex.tex.jinja:106
#: themes/basic/genindex-single.html:22 themes/basic/genindex-single.html:48
#: themes/basic/genindex-split.html:3 themes/basic/genindex-split.html:6
#: themes/basic/genindex.html:3 themes/basic/genindex.html:26
#: themes/basic/genindex.html:59 themes/basic/layout.html:127
#: writers/texinfo.py:511
msgid "Index"
msgstr "索引"

#: writers/latex.py:746 writers/texinfo.py:642
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr "セクション、トピック、表、訓戒またはサイドバーにないタイトルノードが見つかりました。"

#: writers/texinfo.py:1214
msgid "caption not inside a figure."
msgstr "キャプションは図の中にはありません。"

#: writers/texinfo.py:1300
#, python-format
msgid "unimplemented node type: %r"
msgstr "未実装のノードタイプ: %r"

#: writers/latex.py:364
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr "不明なクラス %r の toplevel_sectioning %r"

#: builders/latex/__init__.py:226 writers/latex.py:414
#, python-format
msgid "no Babel option known for language %r"
msgstr "%r 言語向けの 既知の Babel オプションはありません"

#: writers/latex.py:432
msgid "too large :maxdepth:, ignored."
msgstr ":maxdepth: が大きすぎるので無視されます。"

#: writers/latex.py:593
#, python-format
msgid "template %s not found; loading from legacy %s instead"
msgstr ""

#: writers/latex.py:711
msgid "document title is not a single Text node"
msgstr "ドキュメントのタイトルは、単一の Text ノードではありません"

#: writers/latex.py:1178
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr "tabularcolumns と :widths: オプションの両方が設定されています。:widths: は無視されます。"

#: writers/latex.py:1575
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "ディメンション単位 %s が無効です。無視されます。"

#: writers/latex.py:1931
#, python-format
msgid "unknown index entry type %s found"
msgstr "不明なインデックスエントリタイプ %s が見つかりました"

#: domains/std/__init__.py:86 domains/std/__init__.py:103
#, python-format
msgid "environment variable; %s"
msgstr "環境変数; %s"

#: domains/std/__init__.py:111
#, python-format
msgid "%s; configuration value"
msgstr ""

#: domains/std/__init__.py:165
msgid "Type"
msgstr ""

#: domains/std/__init__.py:175
msgid "Default"
msgstr ""

#: domains/std/__init__.py:234
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr "不正なオプションの説明 %r は、\"opt\"、\"-opt args\"、\"--opt args\"、\"/opt args\" または \"+opt args\" のようになります。"

#: domains/std/__init__.py:305
#, python-format
msgid "%s command line option"
msgstr "%s コマンドラインオプション"

#: domains/std/__init__.py:307
msgid "command line option"
msgstr "コマンドラインオプション"

#: domains/std/__init__.py:429
msgid "glossary term must be preceded by empty line"
msgstr "用語集の前に空行が必要です"

#: domains/std/__init__.py:437
msgid "glossary terms must not be separated by empty lines"
msgstr "用語集の用語は空行で区切ってはいけません"

#: domains/std/__init__.py:443 domains/std/__init__.py:456
msgid "glossary seems to be misformatted, check indentation"
msgstr "用語集のフォーマットが間違っているようです。インデントを確認してください"

#: domains/std/__init__.py:601
msgid "glossary term"
msgstr "用語集の項目"

#: domains/std/__init__.py:602
msgid "grammar token"
msgstr "文法トークン"

#: domains/std/__init__.py:603
msgid "reference label"
msgstr "参照ラベル"

#: domains/std/__init__.py:606
msgid "environment variable"
msgstr "環境変数"

#: domains/std/__init__.py:607
msgid "program option"
msgstr "プログラムオプション"

#: domains/std/__init__.py:608
msgid "document"
msgstr "document"

#: domains/std/__init__.py:646 domains/std/__init__.py:658
msgid "Module Index"
msgstr "モジュール索引"

#: domains/std/__init__.py:647 domains/std/__init__.py:659
#: themes/basic/defindex.html:18
msgid "Search Page"
msgstr "検索ページ"

#: domains/std/__init__.py:721
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr "%s の記述 %s はすでに %s で使われています"

#: domains/std/__init__.py:926
msgid "numfig is disabled. :numref: is ignored."
msgstr "numfig は無効です。:numref: は無視されます。"

#: domains/std/__init__.py:934
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr "クロスリファレンスの作成に失敗しました。番号が割り当てられていません: %s"

#: domains/std/__init__.py:946
#, python-format
msgid "the link has no caption: %s"
msgstr "リンクにキャプションがありません: %s"

#: domains/std/__init__.py:960
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "無効な numfig_format: %s (%r)"

#: domains/std/__init__.py:963
#, python-format
msgid "invalid numfig_format: %s"
msgstr "無効な numfig_format: %s"

#: domains/std/__init__.py:1194
#, python-format
msgid "undefined label: %r"
msgstr ""

#: domains/std/__init__.py:1196
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr ""

#: domains/python/__init__.py:107 domains/python/__init__.py:244
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (%s モジュール)"

#: domains/python/__init__.py:167 domains/python/__init__.py:334
#: domains/python/__init__.py:385 domains/python/__init__.py:424
#, python-format
msgid "%s (in module %s)"
msgstr "%s (%s モジュール)"

#: domains/python/__init__.py:169
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (組み込み変数)"

#: domains/python/__init__.py:194
#, python-format
msgid "%s (built-in class)"
msgstr "%s (組み込みクラス)"

#: domains/python/__init__.py:195
#, python-format
msgid "%s (class in %s)"
msgstr "%s (%s のクラス)"

#: domains/python/__init__.py:249
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (%s のクラスメソッド)"

#: domains/python/__init__.py:251
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (%s の静的メソッド)"

#: domains/python/__init__.py:389
#, python-format
msgid "%s (%s property)"
msgstr "%s (%s のプロパティ)"

#: domains/python/__init__.py:428
#, python-format
msgid "%s (type alias in %s)"
msgstr ""

#: domains/python/__init__.py:557
msgid "Python Module Index"
msgstr "Pythonモジュール索引"

#: domains/python/__init__.py:558
msgid "modules"
msgstr "モジュール"

#: domains/python/__init__.py:607
msgid "Deprecated"
msgstr "非推奨"

#: domains/python/__init__.py:632
msgid "exception"
msgstr "例外"

#: domains/python/__init__.py:634
msgid "class method"
msgstr "クラスメソッド"

#: domains/python/__init__.py:635
msgid "static method"
msgstr "の静的メソッド"

#: domains/python/__init__.py:637
msgid "property"
msgstr "プロパティ"

#: domains/python/__init__.py:638
msgid "type alias"
msgstr ""

#: domains/python/__init__.py:698
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr ""

#: domains/python/__init__.py:817
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr "相互参照 %r に複数のターゲットが見つかりました: %s"

#: domains/python/__init__.py:878
msgid " (deprecated)"
msgstr " (非推奨)"

#: domains/c/__init__.py:298 domains/cpp/__init__.py:436
#: domains/python/_object.py:164 ext/napoleon/docstring.py:786
msgid "Parameters"
msgstr "パラメータ"

#: domains/python/_object.py:169
msgid "Variables"
msgstr "変数"

#: domains/python/_object.py:173
msgid "Raises"
msgstr "例外"

#: domains/c/__init__.py:199
#, python-format
msgid "%s (C %s)"
msgstr "%s (C %s)"

#: domains/c/__init__.py:260 domains/c/_symbol.py:510
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr ""

#: domains/c/__init__.py:301 domains/cpp/__init__.py:449
msgid "Return values"
msgstr "戻り値"

#: domains/c/__init__.py:673 domains/cpp/__init__.py:855
msgid "member"
msgstr "のメンバ変数"

#: domains/c/__init__.py:674
msgid "variable"
msgstr "変数"

#: domains/c/__init__.py:676
msgid "macro"
msgstr "のマクロ"

#: domains/c/__init__.py:677
msgid "struct"
msgstr "struct"

#: domains/c/__init__.py:678 domains/cpp/__init__.py:853
msgid "union"
msgstr "union"

#: domains/c/__init__.py:679 domains/cpp/__init__.py:858
msgid "enum"
msgstr "列挙型"

#: domains/c/__init__.py:680 domains/cpp/__init__.py:859
msgid "enumerator"
msgstr "enumerator"

#: domains/c/__init__.py:681 domains/cpp/__init__.py:856
msgid "type"
msgstr "のデータ型"

#: domains/c/__init__.py:683 domains/cpp/__init__.py:861
msgid "function parameter"
msgstr "関数パラメータ"

#: domains/cpp/__init__.py:155
msgid "Template Parameters"
msgstr "テンプレートパラメータ"

#: domains/cpp/__init__.py:277
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: domains/cpp/__init__.py:360 domains/cpp/_symbol.py:793
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr ""

#: domains/cpp/__init__.py:857
msgid "concept"
msgstr "コンセプト"

#: domains/cpp/__init__.py:862
msgid "template parameter"
msgstr "テンプレート・パラメータ"

#: themes/haiku/layout.html:16
msgid "Contents"
msgstr "コンテンツ"

#: themes/agogo/layout.html:29 themes/basic/globaltoc.html:2
#: themes/basic/localtoc.html:4 themes/scrolls/layout.html:32
msgid "Table of Contents"
msgstr "目次"

#: themes/agogo/layout.html:34 themes/basic/layout.html:130
#: themes/basic/search.html:3 themes/basic/search.html:15
msgid "Search"
msgstr "検索"

#: themes/agogo/layout.html:37 themes/basic/searchbox.html:8
#: themes/basic/searchfield.html:12
msgid "Go"
msgstr "検索"

#: themes/agogo/layout.html:81 themes/basic/sourcelink.html:7
msgid "Show Source"
msgstr "ソースコードを表示"

#: themes/classic/layout.html:12 themes/classic/static/sidebar.js.jinja:51
msgid "Collapse sidebar"
msgstr "サイドバーをたたむ"

#: themes/basic/layout.html:18
msgid "Navigation"
msgstr "ナビゲーション"

#: themes/basic/layout.html:115
#, python-format
msgid "Search within %(docstitle)s"
msgstr "%(docstitle)s 内を検索"

#: themes/basic/layout.html:124
msgid "About these documents"
msgstr "このドキュメントについて"

#: themes/basic/layout.html:133 themes/basic/layout.html:177
#: themes/basic/layout.html:179
msgid "Copyright"
msgstr "著作権"

#: themes/basic/layout.html:183 themes/basic/layout.html:189
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr ""

#: themes/basic/layout.html:201
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "最終更新: %(last_updated)s"

#: themes/basic/layout.html:204
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr ""

#: themes/basic/genindex-single.html:26
#, python-format
msgid "Index &#x2013; %(key)s"
msgstr ""

#: themes/basic/genindex-single.html:54 themes/basic/genindex-split.html:16
#: themes/basic/genindex-split.html:30 themes/basic/genindex.html:65
msgid "Full index on one page"
msgstr "総索引"

#: themes/basic/genindex-split.html:8
msgid "Index pages by letter"
msgstr "頭文字別索引"

#: themes/basic/genindex-split.html:17
msgid "can be huge"
msgstr "大きい場合があるので注意"

#: themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "%(docstitle)s 内を検索"

#: themes/basic/sourcelink.html:4
msgid "This Page"
msgstr "このページ"

#: themes/basic/defindex.html:4
msgid "Overview"
msgstr "概要"

#: themes/basic/defindex.html:8
msgid "Welcome! This is"
msgstr "Welcome! This is"

#: themes/basic/defindex.html:9
msgid "the documentation for"
msgstr "the documentation for"

#: themes/basic/defindex.html:10
msgid "last updated"
msgstr "最終更新"

#: themes/basic/defindex.html:13
msgid "Indices and tables:"
msgstr "索引と表一覧:"

#: themes/basic/defindex.html:16
msgid "Complete Table of Contents"
msgstr "総合目次"

#: themes/basic/defindex.html:17
msgid "lists all sections and subsections"
msgstr "章／節一覧"

#: themes/basic/defindex.html:19
msgid "search this documentation"
msgstr "ドキュメントを検索"

#: themes/basic/defindex.html:21
msgid "Global Module Index"
msgstr "モジュール総索引"

#: themes/basic/defindex.html:22
msgid "quick access to all modules"
msgstr "全モジュール早見表"

#: builders/html/__init__.py:499 themes/basic/defindex.html:23
msgid "General Index"
msgstr "総合索引"

#: themes/basic/defindex.html:24
msgid "all functions, classes, terms"
msgstr "関数、クラスおよび用語総覧"

#: themes/basic/searchbox.html:4
msgid "Quick search"
msgstr "クイック検索"

#: themes/basic/search.html:20
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "検索機能を使うには JavaScript を有効にしてください。"

#: themes/basic/search.html:28
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr "複数の単語を検索すると、次を含む一致のみが表示されます\n     すべての用語。"

#: themes/basic/search.html:35
msgid "search"
msgstr "検索"

#: themes/basic/relations.html:4
msgid "Previous topic"
msgstr "前のトピックへ"

#: themes/basic/relations.html:6
msgid "previous chapter"
msgstr "前の章へ"

#: themes/basic/relations.html:11
msgid "Next topic"
msgstr "次のトピックへ"

#: themes/basic/relations.html:13
msgid "next chapter"
msgstr "次の章へ"

#: themes/classic/static/sidebar.js.jinja:42
msgid "Expand sidebar"
msgstr "サイドバーを展開"

#: themes/basic/changes/frameset.html:5
#: themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "バージョン %(version)s の変更点 &#8212; %(docstitle)s"

#: themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "バージョン %(version)s の変更点（このリストは自動生成されています）"

#: themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "ライブラリに関する変更"

#: themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "C API に関する変更"

#: themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "その他の変更"

#: themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "検索結果を隠す"

#: themes/basic/static/searchtools.js:117
msgid "Search Results"
msgstr "検索結果"

#: themes/basic/static/searchtools.js:119
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "検索した文字列はどの文書にも見つかりませんでした。すべての単語が正確に記述されているか、あるいは、十分なカテゴリーが選択されているか確認してください。"

#: themes/basic/static/searchtools.js:123
msgid "Search finished, found one page matching the search query."
msgid_plural ""
"Search finished, found ${resultCount} pages matching the search query."
msgstr[0] ""

#: themes/basic/static/searchtools.js:253
msgid "Searching"
msgstr "検索中"

#: themes/basic/static/searchtools.js:270
msgid "Preparing search..."
msgstr "検索を準備しています..."

#: themes/basic/static/searchtools.js:474
msgid ", in "
msgstr ", in "

#: environment/collectors/asset.py:95
#, python-format
msgid "image file not readable: %s"
msgstr "画像ファイルが読み込めません: %s"

#: environment/collectors/asset.py:123
#, python-format
msgid "image file %s not readable: %s"
msgstr "画像ファイル %s が読み込めません: %s"

#: environment/collectors/asset.py:160
#, python-format
msgid "download file not readable: %s"
msgstr "ダウンロードファイルが読み込めません: %s"

#: environment/collectors/toctree.py:258
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr "%s はすでにセクション番号が割り当てられています (入れ子になった番号の toctree ?)"

#: environment/adapters/toctree.py:318
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "循環参照している toctree が検出されましたので無視します: %s <- %s"

#: environment/adapters/toctree.py:342
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "toctree にはタイトルのないドキュメント %r への参照が含まれています: リンクは生成されません"

#: environment/adapters/toctree.py:357
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr ""

#: environment/adapters/indexentries.py:126
#, python-format
msgid "see %s"
msgstr "%sを参照"

#: environment/adapters/indexentries.py:136
#, python-format
msgid "see also %s"
msgstr "%sも参照"

#: environment/adapters/indexentries.py:144
#, python-format
msgid "unknown index entry type %r"
msgstr "不明なインデックスエントリタイプ %r"

#: environment/adapters/indexentries.py:273
#: templates/latex/sphinxmessages.sty.jinja:11
msgid "Symbols"
msgstr "記号"

#: builders/html/_build_info.py:32
msgid "failed to read broken build info file (unknown version)"
msgstr ""

#: builders/html/_build_info.py:36
msgid "failed to read broken build info file (missing config entry)"
msgstr ""

#: builders/html/_build_info.py:39
msgid "failed to read broken build info file (missing tags entry)"
msgstr ""

#: builders/html/__init__.py:113
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "HTMLページは%(outdir)sにあります。"

#: builders/html/__init__.py:340
#, python-format
msgid "Failed to read build info file: %r"
msgstr "build info ファイルの読み込みに失敗しました: %r"

#: builders/html/__init__.py:355
msgid "build_info mismatch, copying .buildinfo to .buildinfo.bak"
msgstr ""

#: builders/html/__init__.py:358
msgid "building [html]: "
msgstr ""

#: builders/html/__init__.py:374
#, python-format
msgid ""
"template %s has been changed since the previous build, all docs will be "
"rebuilt"
msgstr ""

#: builders/html/__init__.py:499
msgid "index"
msgstr "索引"

#: builders/html/__init__.py:547
#, python-format
msgid "Logo of %s"
msgstr ""

#: builders/html/__init__.py:572
msgid "next"
msgstr "次へ"

#: builders/html/__init__.py:581
msgid "previous"
msgstr "前へ"

#: builders/html/__init__.py:678
msgid "generating indices"
msgstr "索引を生成中"

#: builders/html/__init__.py:693
msgid "writing additional pages"
msgstr "追加のページを出力中"

#: builders/html/__init__.py:772
#, python-format
msgid "cannot copy image file '%s': %s"
msgstr ""

#: builders/html/__init__.py:784
msgid "copying downloadable files... "
msgstr "ダウンロードファイルをコピー中..."

#: builders/html/__init__.py:796
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "ダウンロードファイル %r をコピーできません: %s"

#: builders/html/__init__.py:843
#, python-format
msgid "Failed to copy a file in the theme's 'static' directory: %s: %r"
msgstr ""

#: builders/html/__init__.py:861
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr "html_static_file 内のファイルのコピーに失敗しました: %s: %r"

#: builders/html/__init__.py:896
msgid "copying static files"
msgstr "静的ファイルをコピー中"

#: builders/html/__init__.py:912
#, python-format
msgid "cannot copy static file %r"
msgstr "静的ファイル %r をコピーできません"

#: builders/html/__init__.py:917
msgid "copying extra files"
msgstr "extraファイルをコピー中"

#: builders/html/__init__.py:927
#, python-format
msgid "cannot copy extra file %r"
msgstr "extraファイル %r をコピーできませんでした"

#: builders/html/__init__.py:933
#, python-format
msgid "Failed to write build info file: %r"
msgstr "build info ファイル %r の出力に失敗しました"

#: builders/html/__init__.py:982
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr "検索インデックスを読み込めず、ドキュメントビルドの一部が不完全です。"

#: builders/html/__init__.py:1027
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr "ページ %s がhtml_sidebarsの複数のパターンに一致しました: %r と %r"

#: builders/html/__init__.py:1188
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr "ページ%sの読み込み中にUnicodeエラーが発生しました。非アスキー文字を含む設定値は全てUnicode文字列にしてください。"

#: builders/html/__init__.py:1197
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "%sページのレンダリング中にエラーが発生しました。\n理由: %r "

#: builders/html/__init__.py:1229
msgid "dumping object inventory"
msgstr "オブジェクト インベントリを出力"

#: builders/html/__init__.py:1237
#, python-format
msgid "dumping search index in %s"
msgstr "%s の検索インデックスを出力"

#: builders/html/__init__.py:1279
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "無効な js_file %r は無視されました"

#: builders/html/__init__.py:1312
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr "複数の math_renderer が登録されています。しかし math_renderer は選択されていません。"

#: builders/html/__init__.py:1317
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "不明な math_renderer %r が指定されました。"

#: builders/html/__init__.py:1325
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "html_extra_path %r が見つかりません"

#: builders/html/__init__.py:1332
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr "html_extra_path %r がoutdir内に配置されます"

#: builders/html/__init__.py:1342
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "html_static_path %r が見つかりません"

#: builders/html/__init__.py:1349
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr "html_static_path %r がoutdir内に配置されます"

#: builders/html/__init__.py:1361 builders/latex/__init__.py:507
#, python-format
msgid "logo file %r does not exist"
msgstr "ロゴファイル %r がありません"

#: builders/html/__init__.py:1372
#, python-format
msgid "favicon file %r does not exist"
msgstr "favicon ファイル %r がありません"

#: builders/html/__init__.py:1384
#, python-format
msgid ""
"Values in 'html_sidebars' must be a list of strings. At least one pattern "
"has a string value: %s. Change to `html_sidebars = %r`."
msgstr ""

#: builders/html/__init__.py:1397
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr ""

#: builders/html/__init__.py:1414
#, python-format
msgid "%s %s documentation"
msgstr "%s %s ドキュメント"

#: builders/latex/transforms.py:118
msgid "Failed to get a docname!"
msgstr ""

#: builders/latex/transforms.py:119
#, python-format
msgid "Failed to get a docname for source %r!"
msgstr ""

#: builders/latex/transforms.py:485
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr ""

#: builders/latex/__init__.py:117
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "LaTeXファイルは%(outdir)sにあります。"

#: builders/latex/__init__.py:119
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr "\n(pdf)latex コマンドで処理するために、そのディレクトリで 'make' を実行してください。\n（これを自動的に行うには、ここで 'make latexpdf' を使用してください）。"

#: builders/latex/__init__.py:157
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "設定値 \"latex_documents\" が見つかりません。ドキュメントは書き込まれません"

#: builders/latex/__init__.py:169
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "設定値 \"latex_documents\" は、不明なドキュメント %s を参照しています"

#: builders/latex/__init__.py:211 templates/latex/latex.tex.jinja:91
msgid "Release"
msgstr "リリース"

#: builders/latex/__init__.py:432
msgid "copying TeX support files"
msgstr "TeX 関連ファイルをコピーしています"

#: builders/latex/__init__.py:469
msgid "copying additional files"
msgstr "追加のファイルをコピーしています"

#: builders/latex/__init__.py:543
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr "不明な設定値 latex_elements[%r] は無視されました。"

#: builders/latex/__init__.py:551
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr "不明なテーマオプション latex_theme_options[%r] は無視されました。"

#: builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr "%r に \"theme\" 設定がありません"

#: builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr "%r に \"%s\" 設定がありません"

#: _cli/util/errors.py:124
msgid "Exception occurred, starting debugger:"
msgstr ""

#: _cli/util/errors.py:133
msgid "reStructuredText markup error:"
msgstr ""

#: _cli/util/errors.py:168
msgid "The full traceback has been saved in:"
msgstr ""

#: _cli/util/errors.py:172
msgid ""
"To report this error to the developers, please open an issue at "
"<https://github.com/sphinx-doc/sphinx/issues/>. Thanks!"
msgstr ""

#: transforms/post_transforms/__init__.py:124
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr "相互参照用のフォールバックテキストを決定できませんでした。バグかもしれません。"

#: transforms/post_transforms/__init__.py:184
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr "'any' クロスリファレンス %r のターゲットが1つ以上みつかりました。 %s に参照を設定します。"

#: transforms/post_transforms/__init__.py:250
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr "%s:%s 参照先が見つかりません: %s"

#: transforms/post_transforms/__init__.py:256
#, python-format
msgid "%r reference target not found: %s"
msgstr "%r 参照先が見つかりません: %s"

#: transforms/post_transforms/images.py:77
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr "リモート画像を取得できませんでした: %s [%s]"

#: transforms/post_transforms/images.py:94
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr "リモート画像を取得できませんでした: %s [%d]"

#: transforms/post_transforms/images.py:141
#, python-format
msgid "Unknown image format: %s..."
msgstr "不明な画像フォーマット: %s..."

#: ext/napoleon/docstring.py:707
msgid "Example"
msgstr "サンプル"

#: ext/napoleon/docstring.py:708
msgid "Examples"
msgstr "サンプル"

#: ext/napoleon/__init__.py:344 ext/napoleon/docstring.py:752
msgid "Keyword Arguments"
msgstr "キーワード引数"

#: ext/napoleon/docstring.py:768
msgid "Notes"
msgstr "メモ"

#: ext/napoleon/docstring.py:777
msgid "Other Parameters"
msgstr "その他のパラメータ"

#: ext/napoleon/docstring.py:813
msgid "Receives"
msgstr "受け取る"

#: ext/napoleon/docstring.py:817
msgid "References"
msgstr "参照"

#: ext/napoleon/docstring.py:849
msgid "Warns"
msgstr "警告"

#: ext/napoleon/docstring.py:853
msgid "Yields"
msgstr "列挙"

#: ext/napoleon/docstring.py:1015
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr "無効な値セット (終了括弧がありません): %s"

#: ext/napoleon/docstring.py:1022
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr "無効な値セット (開始括弧がありません): %s"

#: ext/napoleon/docstring.py:1029
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr "不正な文字列リテラル (終了引用符がありません): %s"

#: ext/napoleon/docstring.py:1036
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr "不正な文字列リテラル (開始引用符がありません): %s"

#: ext/autosummary/__init__.py:255
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr "autosummary は除外したドキュメント %r を参照しています。無視されます。"

#: ext/autosummary/__init__.py:257
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr "autosummary: stubファイルが見つかりません%r。autosummary_generate設定を確認してください。"

#: ext/autosummary/__init__.py:276
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr "キャプション付きオートサマリーには :toctree: オプションが必要です。"

#: ext/autosummary/__init__.py:329
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: ext/autosummary/__init__.py:343
#, python-format
msgid "failed to parse name %s"
msgstr "%sの名前を解析できませんでした "

#: ext/autosummary/__init__.py:348
#, python-format
msgid "failed to import object %s"
msgstr "%sオブジェクトをインポートできませんでした "

#: ext/autosummary/__init__.py:647
#, python-format
msgid ""
"Summarised items should not include the current module. Replace %r with %r."
msgstr ""

#: ext/autosummary/__init__.py:818
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr "autosummary_generate: ファイルが見つかりません: %s"

#: ext/autosummary/__init__.py:826
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr ""

#: ext/autosummary/generate.py:214 ext/autosummary/generate.py:390
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr "autosummary: ドキュメント化する %r の決定に失敗しました。次の例外が発生しました:\n%s"

#: ext/autosummary/generate.py:525
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr "[autosummary] %s の autosummary を生成中"

#: ext/autosummary/generate.py:529
#, python-format
msgid "[autosummary] writing to %s"
msgstr "[autosummary] %s に書き込み中"

#: ext/autosummary/generate.py:571
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: ext/autosummary/generate.py:766
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr "\nautosummary ディレクティブを使って ReStructuredText を生成します。\n\nsphinx-autogen は sphinx.ext.autosummary.generate のフロントエンドです。\n入力されたファイルを含む autosummary ディレクティブから reStructuredText ファイルを\n生成します。\n\nautosummary ディレクティブのフォーマットは\n``sphinx.ext.autosummary`` に記載されています。Pythonモジュールと :: を使って読むことができます。\n\npydoc sphinx.ext.autosummary\n"

#: ext/autosummary/generate.py:788
msgid "source files to generate rST files for"
msgstr "rST ファイルを生成するためのソースファイル"

#: ext/autosummary/generate.py:796
msgid "directory to place all output in"
msgstr "すべての生成データを配置するディレクトリ"

#: ext/autosummary/generate.py:804
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "ファイルのデフォルト拡張子 (デフォルト: %(default)s)"

#: ext/autosummary/generate.py:812
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr "カスタムテンプレートディレクトリ (デフォルト: %(default)s)"

#: ext/autosummary/generate.py:820
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr "インポートしたメンバーのドキュメント (デフォルト: %(default)s)"

#: ext/autosummary/generate.py:828
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr "モジュール __all__ 属性に含まれるメンバーのみを対象としたドキュメントを作成します。(デフォルト: %(default)s)"

#: ext/intersphinx/_resolve.py:47
#, python-format
msgid "(in %s v%s)"
msgstr "(in %s v%s)"

#: ext/intersphinx/_resolve.py:49
#, python-format
msgid "(in %s)"
msgstr "(in %s)"

#: ext/intersphinx/_resolve.py:103
#, python-format
msgid "inventory '%s': duplicate matches found for %s:%s"
msgstr ""

#: ext/intersphinx/_resolve.py:113
#, python-format
msgid "inventory '%s': multiple matches found for %s:%s"
msgstr ""

#: ext/intersphinx/_resolve.py:359
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:367
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:378
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:585
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr ""

#: ext/intersphinx/_load.py:59
#, python-format
msgid ""
"Invalid intersphinx project identifier `%r` in intersphinx_mapping. Project "
"identifiers must be non-empty strings."
msgstr ""

#: ext/intersphinx/_load.py:70
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Expected a two-element tuple "
"or list."
msgstr ""

#: ext/intersphinx/_load.py:81
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Values must be a (target URI,"
" inventory locations) pair."
msgstr ""

#: ext/intersphinx/_load.py:92
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique non-empty strings."
msgstr ""

#: ext/intersphinx/_load.py:101
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique (other instance in intersphinx_mapping[%r])."
msgstr ""

#: ext/intersphinx/_load.py:120
#, python-format
msgid ""
"Invalid inventory location value `%r` in intersphinx_mapping[%r][1]. "
"Inventory locations must be non-empty strings or None."
msgstr ""

#: ext/intersphinx/_load.py:131
msgid "Invalid `intersphinx_mapping` configuration (1 error)."
msgstr ""

#: ext/intersphinx/_load.py:134
#, python-format
msgid "Invalid `intersphinx_mapping` configuration (%s errors)."
msgstr ""

#: ext/intersphinx/_load.py:155
msgid "An invalid intersphinx_mapping entry was added after normalisation."
msgstr ""

#: ext/intersphinx/_load.py:240
#, python-format
msgid "loading intersphinx inventory '%s' from %s ..."
msgstr ""

#: ext/intersphinx/_load.py:265
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr "いくつかのインベントリでいくつかの問題に遭遇しましたが、代替手段を持っていました:"

#: ext/intersphinx/_load.py:275
msgid "failed to reach any of the inventories with the following issues:"
msgstr "以下の問題があるため、いくつかのインベントリは到達できませんでした:"

#: ext/intersphinx/_load.py:319
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr "intersphinx インベントリは移動しました: %s -> %s"

#: ext/autodoc/type_comment.py:132
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr "%r のシグネチャの更新に失敗しました: パラメータが見つかりません: %s。"

#: ext/autodoc/type_comment.py:135
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr "%rのtype_commentを解析できませんでした: %s"

#: ext/autodoc/__init__.py:141
#, python-format
msgid "invalid value for member-order option: %s"
msgstr "member-order オプションに無効な値があります: %s"

#: ext/autodoc/__init__.py:149
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr "class-doc-from オプションに無効な値があります: %s"

#: ext/autodoc/__init__.py:408
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr "auto%s (%r) の署名が無効です"

#: ext/autodoc/__init__.py:525
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr "%sの引数のフォーマット中にエラーが発生しました: %s "

#: ext/autodoc/__init__.py:795
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: ext/autodoc/__init__.py:890
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr "ドキュメントの自動生成 %r のためにどのモジュールをインポートするのか分かりません (ドキュメントに \"module\"または \"currentmodule\"ディレクティブを配置するか、明示的なモジュール名を指定してください)"

#: ext/autodoc/__init__.py:934
#, python-format
msgid "A mocked object is detected: %r"
msgstr "モックオブジェクトが検出されました: %r"

#: ext/autodoc/__init__.py:953
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr "%s のシグネチャをフォーマット中にエラーが発生しました: %s"

#: ext/autodoc/__init__.py:1016
msgid "\"::\" in automodule name doesn't make sense"
msgstr "automodule 名の \"::\" は意味がありません"

#: ext/autodoc/__init__.py:1023
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr "automodule に与えられた署名引数、または戻り値となるアノテーション %s"

#: ext/autodoc/__init__.py:1036
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr "__all__ は文字列のリストでなければなりません。%r (%s モジュールの中) ではないです -- ignoring __all__"

#: ext/autodoc/__init__.py:1102
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr "members: オプションで指定された属性がありません: モジュール %s、属性 %s"

#: ext/autodoc/__init__.py:1325 ext/autodoc/__init__.py:1402
#: ext/autodoc/__init__.py:2810
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr "%s の関数シグネチャの取得に失敗しました: %s"

#: ext/autodoc/__init__.py:1616
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr "%s のコンストラクタ署名の取得に失敗しました: %s"

#: ext/autodoc/__init__.py:1743
#, python-format
msgid "Bases: %s"
msgstr "ベースクラス: %s"

#: ext/autodoc/__init__.py:1757
#, python-format
msgid "missing attribute %s in object %s"
msgstr "オブジェクト %s に属性 %s がありません"

#: ext/autodoc/__init__.py:1838 ext/autodoc/__init__.py:1875
#: ext/autodoc/__init__.py:1970
#, python-format
msgid "alias of %s"
msgstr "%sの別名です。"

#: ext/autodoc/__init__.py:1858
#, python-format
msgid "alias of TypeVar(%s)"
msgstr "TypeVar(%s)のエイリアスです。"

#: ext/autodoc/__init__.py:2198 ext/autodoc/__init__.py:2298
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr "%s のメソッド・シグネチャの取得に失敗しました: %s"

#: ext/autodoc/__init__.py:2429
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr "無効な __slots__ が %s で見つかりました。無視されました。"

#: ext/autodoc/preserve_defaults.py:190
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr "%r の既定の引数値の解析に失敗しました: %s。"

#: templates/latex/longtable.tex.jinja:52
#: templates/latex/sphinxmessages.sty.jinja:8
msgid "continued from previous page"
msgstr "前のページからの続き"

#: templates/latex/longtable.tex.jinja:63
#: templates/latex/sphinxmessages.sty.jinja:9
msgid "continues on next page"
msgstr "次のページに続く"

#: templates/latex/sphinxmessages.sty.jinja:10
msgid "Non-alphabetical"
msgstr "アルファベット以外"

#: templates/latex/sphinxmessages.sty.jinja:12
msgid "Numbers"
msgstr "番号"

#: templates/latex/sphinxmessages.sty.jinja:13
msgid "page"
msgstr "ページ"
