"""
公式分类器

负责分析和分类数学公式的复杂度和特征。
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import get_logger
from ..utils.error_handler import handle_errors

logger = get_logger(__name__)


class ComplexityLevel(Enum):
    """复杂度级别"""
    SIMPLE = "simple"       # 简单公式
    MEDIUM = "medium"       # 中等复杂度
    COMPLEX = "complex"     # 复杂公式
    VERY_COMPLEX = "very_complex"  # 非常复杂


@dataclass
class FormulaFeatures:
    """公式特征"""
    length: int                          # 公式长度
    element_count: int                   # 元素数量
    nesting_depth: int                   # 嵌套深度
    has_fractions: bool = False          # 是否包含分数
    has_roots: bool = False              # 是否包含根号
    has_integrals: bool = False          # 是否包含积分
    has_sums: bool = False               # 是否包含求和
    has_matrices: bool = False           # 是否包含矩阵
    has_limits: bool = False             # 是否包含极限
    has_subscripts: bool = False         # 是否包含下标
    has_superscripts: bool = False       # 是否包含上标
    has_special_functions: bool = False  # 是否包含特殊函数
    greek_letter_count: int = 0          # 希腊字母数量
    operator_count: int = 0              # 运算符数量
    bracket_pairs: int = 0               # 括号对数


class FormulaClassifier:
    """
    公式分类器
    
    分析数学公式的复杂度和特征，为转换策略提供依据。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化公式分类器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 编译正则表达式
        self._compile_patterns()
        
        # 复杂度权重配置
        self.complexity_weights = {
            'length': 0.1,
            'element_count': 0.2,
            'nesting_depth': 0.3,
            'fractions': 2.0,
            'roots': 1.5,
            'integrals': 3.0,
            'sums': 2.5,
            'matrices': 4.0,
            'limits': 2.0,
            'subscripts': 0.5,
            'superscripts': 0.5,
            'special_functions': 1.0,
            'greek_letters': 0.2,
            'operators': 0.1,
            'brackets': 0.3
        }
    
    def _compile_patterns(self) -> None:
        """编译正则表达式模式"""
        
        # 分数
        self.fraction_pattern = re.compile(r'\\frac\{[^}]*\}\{[^}]*\}')
        
        # 根号
        self.root_pattern = re.compile(r'\\sqrt(?:\[[^\]]*\])?\{[^}]*\}')
        
        # 积分
        self.integral_pattern = re.compile(r'\\(?:int|iint|iiint|oint)')
        
        # 求和
        self.sum_pattern = re.compile(r'\\(?:sum|prod|coprod)')
        
        # 矩阵
        self.matrix_pattern = re.compile(r'\\begin\{[bBvV]?matrix\}.*?\\end\{[bBvV]?matrix\}', re.DOTALL)
        
        # 极限
        self.limit_pattern = re.compile(r'\\lim')
        
        # 上下标
        self.subscript_pattern = re.compile(r'_(?:\{[^}]*\}|[^{}\s])')
        self.superscript_pattern = re.compile(r'\^(?:\{[^}]*\}|[^{}\s])')
        
        # 希腊字母
        self.greek_pattern = re.compile(
            r'\\(?:alpha|beta|gamma|delta|epsilon|varepsilon|zeta|eta|theta|vartheta|'
            r'iota|kappa|lambda|mu|nu|xi|pi|varpi|rho|varrho|sigma|varsigma|tau|'
            r'upsilon|phi|varphi|chi|psi|omega|Gamma|Delta|Theta|Lambda|Xi|Pi|'
            r'Sigma|Upsilon|Phi|Psi|Omega)(?![a-zA-Z])'
        )
        
        # 运算符
        self.operator_pattern = re.compile(
            r'\\(?:pm|mp|times|div|cdot|ast|star|circ|bullet|leq|geq|neq|approx|'
            r'equiv|sim|simeq|cong|propto|parallel|perp|in|notin|subset|supset|'
            r'subseteq|supseteq|cup|cap|emptyset|varnothing)'
        )
        
        # 特殊函数
        self.special_function_pattern = re.compile(
            r'\\(?:sin|cos|tan|cot|sec|csc|arcsin|arccos|arctan|sinh|cosh|tanh|'
            r'log|ln|lg|exp|max|min|sup|inf|det|gcd|lcm)(?![a-zA-Z])'
        )
        
        # 括号
        self.bracket_pattern = re.compile(r'[\{\}\(\)\[\]]')
    
    @handle_errors(context="分类公式复杂度")
    def classify_complexity(self, formula_content: str) -> str:
        """
        分类公式复杂度
        
        Args:
            formula_content: 公式内容
            
        Returns:
            复杂度级别字符串
        """
        logger.debug(f"开始分析公式复杂度: {formula_content[:50]}...")
        
        # 提取公式特征
        features = self.extract_features(formula_content)
        
        # 计算复杂度分数
        complexity_score = self._calculate_complexity_score(features)
        
        # 确定复杂度级别
        complexity_level = self._determine_complexity_level(complexity_score)
        
        logger.debug(f"公式复杂度分析完成: {complexity_level.value} (分数: {complexity_score:.2f})")
        
        return complexity_level.value
    
    def extract_features(self, formula_content: str) -> FormulaFeatures:
        """
        提取公式特征
        
        Args:
            formula_content: 公式内容
            
        Returns:
            公式特征对象
        """
        features = FormulaFeatures(
            length=len(formula_content),
            element_count=self._count_elements(formula_content),
            nesting_depth=self._calculate_nesting_depth(formula_content)
        )
        
        # 检查各种特征
        features.has_fractions = bool(self.fraction_pattern.search(formula_content))
        features.has_roots = bool(self.root_pattern.search(formula_content))
        features.has_integrals = bool(self.integral_pattern.search(formula_content))
        features.has_sums = bool(self.sum_pattern.search(formula_content))
        features.has_matrices = bool(self.matrix_pattern.search(formula_content))
        features.has_limits = bool(self.limit_pattern.search(formula_content))
        features.has_subscripts = bool(self.subscript_pattern.search(formula_content))
        features.has_superscripts = bool(self.superscript_pattern.search(formula_content))
        features.has_special_functions = bool(self.special_function_pattern.search(formula_content))
        
        # 计数特征
        features.greek_letter_count = len(self.greek_pattern.findall(formula_content))
        features.operator_count = len(self.operator_pattern.findall(formula_content))
        features.bracket_pairs = self._count_bracket_pairs(formula_content)
        
        return features
    
    def _count_elements(self, content: str) -> int:
        """计算公式元素数量"""
        # 简化的元素计数：统计LaTeX命令数量
        command_pattern = re.compile(r'\\[a-zA-Z]+')
        return len(command_pattern.findall(content))
    
    def _calculate_nesting_depth(self, content: str) -> int:
        """计算嵌套深度"""
        max_depth = 0
        current_depth = 0
        
        for char in content:
            if char == '{':
                current_depth += 1
                max_depth = max(max_depth, current_depth)
            elif char == '}':
                current_depth = max(0, current_depth - 1)
        
        return max_depth
    
    def _count_bracket_pairs(self, content: str) -> int:
        """计算括号对数"""
        brackets = self.bracket_pattern.findall(content)
        
        # 简化计算：假设括号都是成对的
        brace_count = brackets.count('{') + brackets.count('}')
        paren_count = brackets.count('(') + brackets.count(')')
        square_count = brackets.count('[') + brackets.count(']')
        
        return (brace_count + paren_count + square_count) // 2
    
    def _calculate_complexity_score(self, features: FormulaFeatures) -> float:
        """计算复杂度分数"""
        score = 0.0
        
        # 基础分数
        score += features.length * self.complexity_weights['length']
        score += features.element_count * self.complexity_weights['element_count']
        score += features.nesting_depth * self.complexity_weights['nesting_depth']
        
        # 特征分数
        if features.has_fractions:
            score += self.complexity_weights['fractions']
        if features.has_roots:
            score += self.complexity_weights['roots']
        if features.has_integrals:
            score += self.complexity_weights['integrals']
        if features.has_sums:
            score += self.complexity_weights['sums']
        if features.has_matrices:
            score += self.complexity_weights['matrices']
        if features.has_limits:
            score += self.complexity_weights['limits']
        if features.has_subscripts:
            score += self.complexity_weights['subscripts']
        if features.has_superscripts:
            score += self.complexity_weights['superscripts']
        if features.has_special_functions:
            score += self.complexity_weights['special_functions']
        
        # 计数分数
        score += features.greek_letter_count * self.complexity_weights['greek_letters']
        score += features.operator_count * self.complexity_weights['operators']
        score += features.bracket_pairs * self.complexity_weights['brackets']
        
        return score
    
    def _determine_complexity_level(self, score: float) -> ComplexityLevel:
        """确定复杂度级别"""
        if score < 2.0:
            return ComplexityLevel.SIMPLE
        elif score < 5.0:
            return ComplexityLevel.MEDIUM
        elif score < 10.0:
            return ComplexityLevel.COMPLEX
        else:
            return ComplexityLevel.VERY_COMPLEX
    
    def classify_formula_type(self, formula_content: str) -> str:
        """
        分类公式类型
        
        Args:
            formula_content: 公式内容
            
        Returns:
            公式类型字符串
        """
        features = self.extract_features(formula_content)
        
        # 根据特征确定公式类型
        if features.has_matrices:
            return "matrix"
        elif features.has_integrals:
            return "integral"
        elif features.has_sums:
            return "summation"
        elif features.has_limits:
            return "limit"
        elif features.has_fractions and features.has_roots:
            return "mixed_expression"
        elif features.has_fractions:
            return "fraction"
        elif features.has_roots:
            return "radical"
        elif features.has_superscripts or features.has_subscripts:
            return "power_expression"
        elif features.has_special_functions:
            return "function_expression"
        else:
            return "simple_expression"
    
    def get_conversion_recommendations(self, formula_content: str) -> Dict[str, Any]:
        """
        获取转换建议
        
        Args:
            formula_content: 公式内容
            
        Returns:
            转换建议字典
        """
        features = self.extract_features(formula_content)
        complexity = self.classify_complexity(formula_content)
        formula_type = self.classify_formula_type(formula_content)
        
        recommendations = {
            'complexity': complexity,
            'formula_type': formula_type,
            'features': features,
            'conversion_method': self._recommend_conversion_method(complexity, features),
            'special_handling': self._get_special_handling_notes(features),
            'estimated_processing_time': self._estimate_processing_time(complexity, features)
        }
        
        return recommendations
    
    def _recommend_conversion_method(self, complexity: str, features: FormulaFeatures) -> str:
        """推荐转换方法"""
        if complexity == "simple":
            return "direct_mapping"
        elif complexity == "medium":
            if features.has_matrices or features.has_integrals:
                return "mathtype_api"
            else:
                return "template_based"
        else:
            return "mathtype_api"
    
    def _get_special_handling_notes(self, features: FormulaFeatures) -> List[str]:
        """获取特殊处理注意事项"""
        notes = []
        
        if features.has_matrices:
            notes.append("需要特殊处理矩阵格式")
        if features.has_integrals:
            notes.append("需要处理积分上下限")
        if features.has_sums:
            notes.append("需要处理求和上下限")
        if features.nesting_depth > 5:
            notes.append("嵌套层次较深，需要仔细处理")
        if features.bracket_pairs > 10:
            notes.append("括号较多，需要确保匹配正确")
        
        return notes
    
    def _estimate_processing_time(self, complexity: str, features: FormulaFeatures) -> float:
        """估算处理时间（秒）"""
        base_time = {
            "simple": 0.1,
            "medium": 0.5,
            "complex": 2.0,
            "very_complex": 5.0
        }.get(complexity, 1.0)
        
        # 根据特征调整时间
        if features.has_matrices:
            base_time *= 2.0
        if features.has_integrals or features.has_sums:
            base_time *= 1.5
        if features.nesting_depth > 5:
            base_time *= 1.3
        
        return base_time
