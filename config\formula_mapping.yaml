# LaTeX公式到MathType的映射配置

# 基本数学符号映射
symbols:
  # 希腊字母
  "\\alpha": "α"
  "\\beta": "β"
  "\\gamma": "γ"
  "\\delta": "δ"
  "\\epsilon": "ε"
  "\\varepsilon": "ε"
  "\\zeta": "ζ"
  "\\eta": "η"
  "\\theta": "θ"
  "\\vartheta": "ϑ"
  "\\iota": "ι"
  "\\kappa": "κ"
  "\\lambda": "λ"
  "\\mu": "μ"
  "\\nu": "ν"
  "\\xi": "ξ"
  "\\pi": "π"
  "\\varpi": "ϖ"
  "\\rho": "ρ"
  "\\varrho": "ϱ"
  "\\sigma": "σ"
  "\\varsigma": "ς"
  "\\tau": "τ"
  "\\upsilon": "υ"
  "\\phi": "φ"
  "\\varphi": "ϕ"
  "\\chi": "χ"
  "\\psi": "ψ"
  "\\omega": "ω"
  
  # 大写希腊字母
  "\\Gamma": "Γ"
  "\\Delta": "Δ"
  "\\Theta": "Θ"
  "\\Lambda": "Λ"
  "\\Xi": "Ξ"
  "\\Pi": "Π"
  "\\Sigma": "Σ"
  "\\Upsilon": "Υ"
  "\\Phi": "Φ"
  "\\Psi": "Ψ"
  "\\Omega": "Ω"
  
  # 数学运算符
  "\\pm": "±"
  "\\mp": "∓"
  "\\times": "×"
  "\\div": "÷"
  "\\cdot": "·"
  "\\ast": "∗"
  "\\star": "⋆"
  "\\circ": "∘"
  "\\bullet": "∙"
  
  # 关系符号
  "\\leq": "≤"
  "\\geq": "≥"
  "\\neq": "≠"
  "\\approx": "≈"
  "\\equiv": "≡"
  "\\sim": "∼"
  "\\simeq": "≃"
  "\\cong": "≅"
  "\\propto": "∝"
  "\\parallel": "∥"
  "\\perp": "⊥"
  
  # 集合符号
  "\\in": "∈"
  "\\notin": "∉"
  "\\subset": "⊂"
  "\\supset": "⊃"
  "\\subseteq": "⊆"
  "\\supseteq": "⊇"
  "\\cup": "∪"
  "\\cap": "∩"
  "\\emptyset": "∅"
  "\\varnothing": "∅"
  
  # 逻辑符号
  "\\land": "∧"
  "\\lor": "∨"
  "\\lnot": "¬"
  "\\neg": "¬"
  "\\forall": "∀"
  "\\exists": "∃"
  "\\nexists": "∄"
  
  # 箭头符号
  "\\leftarrow": "←"
  "\\rightarrow": "→"
  "\\leftrightarrow": "↔"
  "\\Leftarrow": "⇐"
  "\\Rightarrow": "⇒"
  "\\Leftrightarrow": "⇔"
  "\\uparrow": "↑"
  "\\downarrow": "↓"
  "\\updownarrow": "↕"
  
  # 微积分符号
  "\\partial": "∂"
  "\\nabla": "∇"
  "\\infty": "∞"
  "\\aleph": "ℵ"
  
  # 其他符号
  "\\hbar": "ℏ"
  "\\ell": "ℓ"
  "\\wp": "℘"
  "\\Re": "ℜ"
  "\\Im": "ℑ"
  "\\angle": "∠"
  "\\triangle": "△"
  "\\square": "□"
  "\\diamond": "◊"

# 函数映射
functions:
  # 三角函数
  "\\sin": "sin"
  "\\cos": "cos"
  "\\tan": "tan"
  "\\cot": "cot"
  "\\sec": "sec"
  "\\csc": "csc"
  "\\arcsin": "arcsin"
  "\\arccos": "arccos"
  "\\arctan": "arctan"
  "\\sinh": "sinh"
  "\\cosh": "cosh"
  "\\tanh": "tanh"
  
  # 对数函数
  "\\log": "log"
  "\\ln": "ln"
  "\\lg": "lg"
  "\\exp": "exp"
  
  # 其他函数
  "\\max": "max"
  "\\min": "min"
  "\\sup": "sup"
  "\\inf": "inf"
  "\\lim": "lim"
  "\\det": "det"
  "\\gcd": "gcd"
  "\\lcm": "lcm"

# 环境映射
environments:
  # 矩阵环境
  "matrix": "matrix"
  "pmatrix": "pmatrix"
  "bmatrix": "bmatrix"
  "Bmatrix": "Bmatrix"
  "vmatrix": "vmatrix"
  "Vmatrix": "Vmatrix"
  
  # 对齐环境
  "align": "align"
  "align*": "align"
  "aligned": "aligned"
  "eqnarray": "eqnarray"
  "eqnarray*": "eqnarray"
  
  # 分段函数
  "cases": "cases"
  "dcases": "dcases"
  
  # 数组环境
  "array": "array"
  "tabular": "tabular"

# 特殊命令映射
commands:
  # 分数
  "\\frac": "frac"
  "\\dfrac": "dfrac"
  "\\tfrac": "tfrac"
  "\\cfrac": "cfrac"
  
  # 根号
  "\\sqrt": "sqrt"
  
  # 上下标
  "^": "superscript"
  "_": "subscript"
  
  # 积分
  "\\int": "int"
  "\\iint": "iint"
  "\\iiint": "iiint"
  "\\oint": "oint"
  
  # 求和与乘积
  "\\sum": "sum"
  "\\prod": "prod"
  "\\coprod": "coprod"
  
  # 极限
  "\\lim": "lim"
  "\\limsup": "limsup"
  "\\liminf": "liminf"
  
  # 大型运算符
  "\\bigcup": "bigcup"
  "\\bigcap": "bigcap"
  "\\bigvee": "bigvee"
  "\\bigwedge": "bigwedge"
  
  # 括号
  "\\left": "left"
  "\\right": "right"
  "\\big": "big"
  "\\Big": "Big"
  "\\bigg": "bigg"
  "\\Bigg": "Bigg"
  
  # 重音符号
  "\\hat": "hat"
  "\\widehat": "widehat"
  "\\tilde": "tilde"
  "\\widetilde": "widetilde"
  "\\bar": "bar"
  "\\overline": "overline"
  "\\underline": "underline"
  "\\vec": "vec"
  "\\overrightarrow": "overrightarrow"
  "\\overleftarrow": "overleftarrow"
  
  # 空格
  "\\,": "thinspace"
  "\\:": "medspace"
  "\\;": "thickspace"
  "\\quad": "quad"
  "\\qquad": "qquad"

# 自定义命令映射（用户可以添加自己的映射）
custom_commands:
  "\\dd": "d"      # 微分符号
  "\\ee": "e"      # 自然常数
  "\\ii": "i"      # 虚数单位
  "\\jj": "j"      # 虚数单位（工程）
