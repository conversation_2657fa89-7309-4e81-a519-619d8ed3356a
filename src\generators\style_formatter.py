"""
样式格式化器

负责应用Word文档的样式和格式。
"""

from typing import Dict, List, Any, Optional
from docx import Document
from docx.shared import Inches, Pt, Cm, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
from docx.enum.style import WD_STYLE_TYPE
from docx.enum.dml import MSO_THEME_COLOR_INDEX

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager

logger = get_logger(__name__)


class StyleFormatter:
    """
    样式格式化器
    
    负责应用Word文档的样式和格式设置。
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化样式格式化器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or ConfigManager()
        self.config = self.config_manager.get_config()
        self.style_templates = self.config_manager.get_style_templates()
    
    def apply_document_styles(
        self,
        document: Document,
        template_name: str = "default"
    ) -> None:
        """
        应用文档样式
        
        Args:
            document: Word文档对象
            template_name: 样式模板名称
        """
        logger.debug(f"应用文档样式: {template_name}")
        
        template = self.style_templates.get(template_name, {})
        if not template:
            logger.warning(f"样式模板 {template_name} 不存在")
            return
        
        # 应用页面设置
        self._apply_page_settings(document, template.get('page_setup', {}))
        
        # 创建自定义样式
        self._create_paragraph_styles(document, template.get('paragraph_styles', {}))
        self._create_heading_styles(document, template.get('heading_styles', {}))
        self._create_list_styles(document, template.get('list_styles', {}))
    
    def _apply_page_settings(
        self,
        document: Document,
        page_setup: Dict[str, Any]
    ) -> None:
        """应用页面设置"""
        if not page_setup:
            return
        
        section = document.sections[0]
        
        # 页边距
        margins = page_setup.get('margins', {})
        if margins:
            section.top_margin = Cm(margins.get('top', 2.5))
            section.bottom_margin = Cm(margins.get('bottom', 2.5))
            section.left_margin = Cm(margins.get('left', 2.5))
            section.right_margin = Cm(margins.get('right', 2.5))
        
        # 页眉页脚距离
        if 'header_margin' in page_setup:
            section.header_distance = Cm(page_setup['header_margin'])
        if 'footer_margin' in page_setup:
            section.footer_distance = Cm(page_setup['footer_margin'])
        
        logger.debug("页面设置已应用")
    
    def _create_paragraph_styles(
        self,
        document: Document,
        paragraph_styles: Dict[str, Any]
    ) -> None:
        """创建段落样式"""
        styles = document.styles
        
        for style_name, style_config in paragraph_styles.items():
            try:
                style_id = f"Custom_{style_name}"
                
                # 检查样式是否已存在
                existing_styles = [s.name for s in styles]
                if style_id in existing_styles:
                    continue
                
                # 创建新样式
                style = styles.add_style(style_id, WD_STYLE_TYPE.PARAGRAPH)
                self._configure_paragraph_format(style, style_config)
                
                logger.debug(f"创建段落样式: {style_name}")
                
            except Exception as e:
                logger.warning(f"创建段落样式 {style_name} 失败: {e}")
    
    def _create_heading_styles(
        self,
        document: Document,
        heading_styles: Dict[str, Any]
    ) -> None:
        """创建标题样式"""
        styles = document.styles
        
        for style_name, style_config in heading_styles.items():
            try:
                style_id = f"Custom_{style_name}"
                
                if style_id not in [s.name for s in styles]:
                    style = styles.add_style(style_id, WD_STYLE_TYPE.PARAGRAPH)
                    self._configure_paragraph_format(style, style_config)
                    
                    # 设置大纲级别
                    outline_level = style_config.get('outline_level', 1)
                    style.paragraph_format.outline_level = outline_level - 1
                    
                    logger.debug(f"创建标题样式: {style_name}")
                
            except Exception as e:
                logger.warning(f"创建标题样式 {style_name} 失败: {e}")
    
    def _create_list_styles(
        self,
        document: Document,
        list_styles: Dict[str, Any]
    ) -> None:
        """创建列表样式"""
        # python-docx对列表样式的支持有限
        # 这里提供基本的列表格式设置
        logger.debug("列表样式配置（基础支持）")
    
    def _configure_paragraph_format(
        self,
        style,
        config: Dict[str, Any]
    ) -> None:
        """配置段落格式"""
        paragraph_format = style.paragraph_format
        font = style.font
        
        # 字体设置
        if 'font_family' in config:
            font.name = config['font_family']
        if 'font_size' in config:
            font.size = Pt(config['font_size'])
        if config.get('bold', False):
            font.bold = True
        if config.get('italic', False):
            font.italic = True
        if config.get('underline', False):
            font.underline = True
        
        # 字体颜色
        if 'color' in config:
            color_value = config['color']
            if isinstance(color_value, str) and color_value.startswith('#'):
                # 十六进制颜色
                rgb_color = self._hex_to_rgb(color_value)
                font.color.rgb = RGBColor(*rgb_color)
        
        # 段落对齐
        if 'alignment' in config:
            alignment_map = {
                'left': WD_ALIGN_PARAGRAPH.LEFT,
                'center': WD_ALIGN_PARAGRAPH.CENTER,
                'right': WD_ALIGN_PARAGRAPH.RIGHT,
                'justify': WD_ALIGN_PARAGRAPH.JUSTIFY
            }
            paragraph_format.alignment = alignment_map.get(config['alignment'])
        
        # 行间距
        if 'line_spacing' in config:
            paragraph_format.line_spacing_rule = WD_LINE_SPACING.MULTIPLE
            paragraph_format.line_spacing = config['line_spacing']
        
        # 段前段后间距
        if 'space_before' in config:
            paragraph_format.space_before = Pt(config['space_before'])
        if 'space_after' in config:
            paragraph_format.space_after = Pt(config['space_after'])
        
        # 缩进
        if 'left_indent' in config:
            paragraph_format.left_indent = Cm(config['left_indent'])
        if 'right_indent' in config:
            paragraph_format.right_indent = Cm(config['right_indent'])
        if 'first_line_indent' in config:
            paragraph_format.first_line_indent = Cm(config['first_line_indent'])
        if 'hanging_indent' in config:
            paragraph_format.first_line_indent = -Cm(config['hanging_indent'])
    
    def _hex_to_rgb(self, hex_color: str) -> tuple:
        """将十六进制颜色转换为RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def format_formula_style(
        self,
        paragraph,
        formula_type: str = "inline"
    ) -> None:
        """
        格式化公式样式
        
        Args:
            paragraph: 段落对象
            formula_type: 公式类型
        """
        formula_styles = self.style_templates.get('default', {}).get('formula_styles', {})
        style_config = formula_styles.get(formula_type, {})
        
        if not style_config:
            return
        
        # 应用公式样式
        if formula_type == "display":
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            if 'space_before' in style_config:
                paragraph.paragraph_format.space_before = Pt(style_config['space_before'])
            if 'space_after' in style_config:
                paragraph.paragraph_format.space_after = Pt(style_config['space_after'])
        
        # 字体设置
        for run in paragraph.runs:
            if 'font_family' in style_config:
                run.font.name = style_config['font_family']
            if 'font_size' in style_config:
                run.font.size = Pt(style_config['font_size'])
    
    def format_table_style(
        self,
        table,
        style_name: str = "default"
    ) -> None:
        """
        格式化表格样式
        
        Args:
            table: 表格对象
            style_name: 样式名称
        """
        table_styles = self.style_templates.get('default', {}).get('table_styles', {})
        style_config = table_styles.get(style_name, {})
        
        if not style_config:
            return
        
        # 应用表格样式
        # 注意：python-docx对表格样式的支持有限
        
        # 设置表格对齐
        alignment = style_config.get('alignment', 'left')
        if alignment == 'center':
            table.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 设置单元格填充
        cell_padding = style_config.get('cell_padding', 3)
        # 这需要通过XML操作实现，这里省略具体实现
        
        logger.debug(f"应用表格样式: {style_name}")
    
    def apply_text_formatting(
        self,
        run,
        format_type: str
    ) -> None:
        """
        应用文本格式
        
        Args:
            run: 文本运行对象
            format_type: 格式类型
        """
        format_map = {
            'bold': lambda r: setattr(r.font, 'bold', True),
            'italic': lambda r: setattr(r.font, 'italic', True),
            'underline': lambda r: setattr(r.font, 'underline', True),
            'subscript': lambda r: setattr(r.font, 'subscript', True),
            'superscript': lambda r: setattr(r.font, 'superscript', True),
        }
        
        formatter = format_map.get(format_type)
        if formatter:
            formatter(run)
    
    def create_custom_numbering(
        self,
        document: Document,
        numbering_format: str = "1."
    ) -> None:
        """
        创建自定义编号
        
        Args:
            document: Word文档对象
            numbering_format: 编号格式
        """
        # python-docx对编号的支持有限
        # 这里提供基本的编号设置框架
        logger.debug(f"创建自定义编号: {numbering_format}")
    
    def get_available_styles(self, document: Document) -> List[str]:
        """
        获取可用样式列表
        
        Args:
            document: Word文档对象
            
        Returns:
            样式名称列表
        """
        return [style.name for style in document.styles]
    
    def copy_style_from_template(
        self,
        document: Document,
        template_name: str,
        style_name: str
    ) -> bool:
        """
        从模板复制样式
        
        Args:
            document: Word文档对象
            template_name: 模板名称
            style_name: 样式名称
            
        Returns:
            是否成功复制
        """
        try:
            template = self.style_templates.get(template_name, {})
            if not template:
                return False
            
            # 查找样式配置
            style_config = None
            for style_category in ['paragraph_styles', 'heading_styles']:
                if style_name in template.get(style_category, {}):
                    style_config = template[style_category][style_name]
                    break
            
            if not style_config:
                return False
            
            # 创建样式
            styles = document.styles
            style_id = f"Custom_{style_name}"
            
            if style_id not in [s.name for s in styles]:
                style = styles.add_style(style_id, WD_STYLE_TYPE.PARAGRAPH)
                self._configure_paragraph_format(style, style_config)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"复制样式失败: {e}")
            return False
