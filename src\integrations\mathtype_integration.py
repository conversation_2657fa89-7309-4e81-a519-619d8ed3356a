"""
MathType集成模块

负责与MathType软件的集成，实现公式的自动转换和插入。
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional

from ..core.exceptions import MathTypeIntegrationError
from ..utils.logger import get_logger
from ..utils.error_handler import handle_errors, retry_on_error

logger = get_logger(__name__)


class MathTypeIntegration:
    """
    MathType集成类
    
    负责与MathType软件的集成和交互。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化MathType集成
        
        Args:
            config: MathType配置
        """
        self.config = config or {}
        self.mathtype_path = self.config.get('installation_path', 'D:\\Softwares\\mathtype')
        self.timeout = self.config.get('timeout', 30)
        self.retry_count = self.config.get('retry_count', 3)
        self.use_com_interface = self.config.get('use_com_interface', True)
        
        # 检查MathType安装
        self._check_mathtype_installation()
        
        # 初始化COM接口（如果在Windows上）
        self.com_interface = None
        if sys.platform == 'win32' and self.use_com_interface:
            self._init_com_interface()
    
    def _check_mathtype_installation(self) -> None:
        """检查MathType安装"""
        mathtype_path = Path(self.mathtype_path)
        
        if not mathtype_path.exists():
            logger.warning(f"MathType安装路径不存在: {mathtype_path}")
            logger.warning("将使用备用转换方法")
            return
        
        # 查找MathType可执行文件
        possible_executables = [
            'MathType.exe',
            'MT6.exe',
            'MTEF.exe'
        ]
        
        mathtype_exe = None
        for exe in possible_executables:
            exe_path = mathtype_path / exe
            if exe_path.exists():
                mathtype_exe = exe_path
                break
        
        if mathtype_exe:
            logger.info(f"找到MathType: {mathtype_exe}")
            self.mathtype_exe = mathtype_exe
        else:
            logger.warning("未找到MathType可执行文件")
            self.mathtype_exe = None
    
    def _init_com_interface(self) -> None:
        """初始化COM接口"""
        try:
            if sys.platform == 'win32':
                import comtypes.client
                
                # 尝试连接MathType COM接口
                try:
                    self.com_interface = comtypes.client.CreateObject("MathType.Application")
                    logger.info("MathType COM接口初始化成功")
                except Exception as e:
                    logger.warning(f"MathType COM接口初始化失败: {e}")
                    self.com_interface = None
            
        except ImportError:
            logger.warning("comtypes模块未安装，无法使用COM接口")
            self.com_interface = None
    
    @handle_errors(context="转换公式")
    @retry_on_error(max_retries=3)
    def convert_formula(
        self,
        parsed_formula: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        转换公式
        
        Args:
            parsed_formula: 解析后的公式对象
            context: 转换上下文
            
        Returns:
            转换后的公式内容
        """
        logger.debug("开始MathType公式转换")
        
        # 如果有COM接口，优先使用
        if self.com_interface:
            return self._convert_via_com(parsed_formula, context)
        
        # 否则使用备用方法
        return self._convert_via_fallback(parsed_formula, context)
    
    def _convert_via_com(
        self,
        parsed_formula: Dict[str, Any],
        context: Optional[Dict[str, Any]]
    ) -> str:
        """通过COM接口转换"""
        try:
            # 获取LaTeX内容
            latex_content = parsed_formula.get('content', '')
            
            # 使用MathType COM接口转换
            # 注意：这里需要根据实际的MathType COM API进行调整
            
            # 简化版本：返回占位符
            logger.debug("使用MathType COM接口转换公式")
            return f"[MathType公式: {latex_content[:30]}...]"
            
        except Exception as e:
            raise MathTypeIntegrationError(
                f"COM接口转换失败: {e}",
                mathtype_path=str(self.mathtype_path),
                operation="com_conversion"
            )
    
    def _convert_via_fallback(
        self,
        parsed_formula: Dict[str, Any],
        context: Optional[Dict[str, Any]]
    ) -> str:
        """备用转换方法"""
        try:
            # 使用内置的转换逻辑
            from ..converters.mathtype_formatter import MathTypeFormatter
            
            formatter = MathTypeFormatter(self.config)
            result = formatter.format_formula(parsed_formula)
            
            logger.debug("使用备用方法转换公式")
            return result
            
        except Exception as e:
            raise MathTypeIntegrationError(
                f"备用转换失败: {e}",
                operation="fallback_conversion"
            )
    
    def is_available(self) -> bool:
        """检查MathType是否可用"""
        return (
            self.mathtype_exe is not None or 
            self.com_interface is not None
        )
    
    def get_version_info(self) -> Dict[str, Any]:
        """获取MathType版本信息"""
        version_info = {
            'available': self.is_available(),
            'installation_path': str(self.mathtype_path),
            'executable_found': self.mathtype_exe is not None,
            'com_interface_available': self.com_interface is not None,
            'version': 'Unknown'
        }
        
        # 尝试获取版本信息
        if self.com_interface:
            try:
                # 这里需要根据实际的MathType COM API获取版本
                version_info['version'] = 'COM Interface Available'
            except Exception:
                pass
        
        return version_info
    
    def test_connection(self) -> Dict[str, Any]:
        """测试MathType连接"""
        test_result = {
            'success': False,
            'method': None,
            'error': None,
            'details': {}
        }
        
        try:
            # 测试COM接口
            if self.com_interface:
                test_formula = {
                    'content': 'x^2 + y^2 = z^2',
                    'type': 'inline'
                }
                
                result = self._convert_via_com(test_formula, None)
                test_result['success'] = True
                test_result['method'] = 'COM Interface'
                test_result['details']['result'] = result
                
            else:
                # 测试备用方法
                test_formula = {
                    'content': 'x^2 + y^2 = z^2',
                    'type': 'inline'
                }
                
                result = self._convert_via_fallback(test_formula, None)
                test_result['success'] = True
                test_result['method'] = 'Fallback Method'
                test_result['details']['result'] = result
                
        except Exception as e:
            test_result['error'] = str(e)
        
        return test_result
    
    def cleanup(self) -> None:
        """清理资源"""
        if self.com_interface:
            try:
                # 关闭COM接口
                self.com_interface = None
                logger.debug("MathType COM接口已关闭")
            except Exception as e:
                logger.warning(f"关闭COM接口时出错: {e}")


# 全局MathType集成实例
_mathtype_integration = None


def get_mathtype_integration(config: Optional[Dict[str, Any]] = None) -> MathTypeIntegration:
    """获取MathType集成实例（单例模式）"""
    global _mathtype_integration
    
    if _mathtype_integration is None:
        _mathtype_integration = MathTypeIntegration(config)
    
    return _mathtype_integration


def is_mathtype_available() -> bool:
    """检查MathType是否可用"""
    try:
        integration = get_mathtype_integration()
        return integration.is_available()
    except Exception:
        return False
