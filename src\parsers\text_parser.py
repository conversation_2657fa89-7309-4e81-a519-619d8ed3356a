"""
文本解析器

负责解析LaTeX文档中的文本内容，包括格式化命令、列表、引用等。
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..core.exceptions import LaTeXParseError
from ..utils.logger import get_logger
from ..utils.error_handler import handle_errors

logger = get_logger(__name__)


class TextElementType(Enum):
    """文本元素类型"""
    PARAGRAPH = "paragraph"
    BOLD = "bold"
    ITALIC = "italic"
    UNDERLINE = "underline"
    TYPEWRITER = "typewriter"
    EMPHASIS = "emphasis"
    LIST_ITEM = "list_item"
    QUOTE = "quote"
    VERBATIM = "verbatim"
    FOOTNOTE = "footnote"


@dataclass
class TextElement:
    """文本元素数据结构"""
    type: TextElementType
    content: str
    start_pos: int
    end_pos: int
    attributes: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.attributes is None:
            self.attributes = {}


class TextParser:
    """
    文本解析器
    
    解析LaTeX文档中的文本内容和格式化命令。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化文本解析器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 编译正则表达式
        self._compile_patterns()
        
        # 格式化命令映射
        self.format_commands = {
            'textbf': TextElementType.BOLD,
            'textit': TextElementType.ITALIC,
            'texttt': TextElementType.TYPEWRITER,
            'underline': TextElementType.UNDERLINE,
            'emph': TextElementType.EMPHASIS,
        }
    
    def _compile_patterns(self) -> None:
        """编译正则表达式模式"""
        
        # 格式化命令
        self.format_pattern = re.compile(
            r'\\(textbf|textit|texttt|underline|emph)\{([^}]+)\}',
            re.MULTILINE
        )
        
        # 列表环境
        self.list_pattern = re.compile(
            r'\\begin\{(itemize|enumerate|description)\}(.*?)\\end\{\1\}',
            re.DOTALL
        )
        
        # 列表项
        self.item_pattern = re.compile(r'\\item(?:\[([^\]]*)\])?\s*(.*?)(?=\\item|\\end|\Z)', re.DOTALL)
        
        # 引用环境
        self.quote_pattern = re.compile(
            r'\\begin\{(quote|quotation)\}(.*?)\\end\{\1\}',
            re.DOTALL
        )
        
        # 代码环境
        self.verbatim_pattern = re.compile(
            r'\\begin\{(verbatim|lstlisting)\}(.*?)\\end\{\1\}',
            re.DOTALL
        )
        
        # 脚注
        self.footnote_pattern = re.compile(r'\\footnote\{([^}]+)\}')
        
        # 段落分隔（两个或更多换行符）
        self.paragraph_pattern = re.compile(r'\n\s*\n')
        
        # 特殊字符
        self.special_chars = {
            r'\\&': '&',
            r'\\%': '%',
            r'\\\$': '$',
            r'\\#': '#',
            r'\\_': '_',
            r'\\{': '{',
            r'\\}': '}',
            r'\\\\': '\n',  # 换行
            r'\\~': ' ',    # 不间断空格
        }
    
    @handle_errors(context="解析文本内容")
    def parse_text(self, content: str) -> List[Dict[str, Any]]:
        """
        解析文本内容
        
        Args:
            content: 文本内容
            
        Returns:
            文本元素列表
        """
        logger.info("开始解析文本内容")
        
        elements = []
        
        # 提取各种文本元素
        elements.extend(self._extract_format_elements(content))
        elements.extend(self._extract_list_elements(content))
        elements.extend(self._extract_quote_elements(content))
        elements.extend(self._extract_verbatim_elements(content))
        elements.extend(self._extract_footnotes(content))
        
        # 按位置排序
        elements.sort(key=lambda e: e.start_pos)
        
        logger.info(f"文本解析完成，共找到 {len(elements)} 个元素")
        
        return [self._element_to_dict(element) for element in elements]
    
    def _extract_format_elements(self, content: str) -> List[TextElement]:
        """提取格式化元素"""
        elements = []
        
        matches = self.format_pattern.finditer(content)
        for match in matches:
            command, text_content = match.groups()
            
            element_type = self.format_commands.get(command, TextElementType.PARAGRAPH)
            
            element = TextElement(
                type=element_type,
                content=text_content.strip(),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={'command': command}
            )
            
            elements.append(element)
        
        return elements
    
    def _extract_list_elements(self, content: str) -> List[TextElement]:
        """提取列表元素"""
        elements = []
        
        list_matches = self.list_pattern.finditer(content)
        for list_match in list_matches:
            list_type, list_content = list_match.groups()
            
            # 提取列表项
            item_matches = self.item_pattern.finditer(list_content)
            for item_match in item_matches:
                item_label, item_content = item_match.groups()
                
                # 计算在原文档中的位置
                item_start = list_match.start() + item_match.start()
                item_end = list_match.start() + item_match.end()
                
                element = TextElement(
                    type=TextElementType.LIST_ITEM,
                    content=item_content.strip(),
                    start_pos=item_start,
                    end_pos=item_end,
                    attributes={
                        'list_type': list_type,
                        'label': item_label
                    }
                )
                
                elements.append(element)
        
        return elements
    
    def _extract_quote_elements(self, content: str) -> List[TextElement]:
        """提取引用元素"""
        elements = []
        
        matches = self.quote_pattern.finditer(content)
        for match in matches:
            quote_type, quote_content = match.groups()
            
            element = TextElement(
                type=TextElementType.QUOTE,
                content=quote_content.strip(),
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={'quote_type': quote_type}
            )
            
            elements.append(element)
        
        return elements
    
    def _extract_verbatim_elements(self, content: str) -> List[TextElement]:
        """提取代码元素"""
        elements = []
        
        matches = self.verbatim_pattern.finditer(content)
        for match in matches:
            env_type, code_content = match.groups()
            
            element = TextElement(
                type=TextElementType.VERBATIM,
                content=code_content,  # 保持原始格式
                start_pos=match.start(),
                end_pos=match.end(),
                attributes={'environment': env_type}
            )
            
            elements.append(element)
        
        return elements
    
    def _extract_footnotes(self, content: str) -> List[TextElement]:
        """提取脚注"""
        elements = []
        
        matches = self.footnote_pattern.finditer(content)
        for match in matches:
            footnote_content = match.group(1)
            
            element = TextElement(
                type=TextElementType.FOOTNOTE,
                content=footnote_content.strip(),
                start_pos=match.start(),
                end_pos=match.end()
            )
            
            elements.append(element)
        
        return elements
    
    def _element_to_dict(self, element: TextElement) -> Dict[str, Any]:
        """将文本元素转换为字典"""
        return {
            'type': element.type.value,
            'content': element.content,
            'start_pos': element.start_pos,
            'end_pos': element.end_pos,
            'attributes': element.attributes
        }
    
    def clean_text(self, text: str) -> str:
        """
        清理LaTeX文本
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的文本
        """
        # 替换特殊字符
        for latex_char, replacement in self.special_chars.items():
            text = re.sub(latex_char, replacement, text)
        
        # 移除多余的空白
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        return text
    
    def extract_paragraphs(self, content: str) -> List[Dict[str, Any]]:
        """
        提取段落
        
        Args:
            content: 文档内容
            
        Returns:
            段落列表
        """
        paragraphs = []
        
        # 按段落分割
        parts = self.paragraph_pattern.split(content)
        
        current_pos = 0
        for part in parts:
            if part.strip():
                # 清理段落内容
                cleaned_content = self.clean_text(part)
                
                if cleaned_content:
                    paragraph = {
                        'type': 'paragraph',
                        'content': cleaned_content,
                        'start_pos': current_pos,
                        'end_pos': current_pos + len(part)
                    }
                    paragraphs.append(paragraph)
            
            current_pos += len(part) + 2  # +2 for the paragraph separator
        
        return paragraphs
    
    def extract_cross_references(self, content: str) -> List[Dict[str, Any]]:
        """
        提取交叉引用
        
        Args:
            content: 文档内容
            
        Returns:
            引用列表
        """
        references = []
        
        # 引用模式
        ref_patterns = {
            'ref': re.compile(r'\\ref\{([^}]+)\}'),
            'eqref': re.compile(r'\\eqref\{([^}]+)\}'),
            'cite': re.compile(r'\\cite(?:\[([^\]]*)\])?\{([^}]+)\}'),
            'pageref': re.compile(r'\\pageref\{([^}]+)\}'),
        }
        
        for ref_type, pattern in ref_patterns.items():
            matches = pattern.finditer(content)
            for match in matches:
                if ref_type == 'cite':
                    optional, keys = match.groups()
                    key_list = [key.strip() for key in keys.split(',')]
                    
                    for key in key_list:
                        reference = {
                            'type': ref_type,
                            'key': key,
                            'optional': optional,
                            'start_pos': match.start(),
                            'end_pos': match.end()
                        }
                        references.append(reference)
                else:
                    key = match.group(1)
                    reference = {
                        'type': ref_type,
                        'key': key,
                        'start_pos': match.start(),
                        'end_pos': match.end()
                    }
                    references.append(reference)
        
        return references
