"""
错误处理模块

提供统一的错误处理和异常管理功能。
"""

import traceback
from typing import Optional, Callable, Any, Dict
from functools import wraps

from ..core.exceptions import ConverterError
from .logger import get_logger

logger = get_logger(__name__)


class ErrorHandler:
    """
    错误处理器
    
    提供统一的错误处理、日志记录和异常转换功能。
    """
    
    @staticmethod
    def handle_exception(
        exception: Exception,
        context: Optional[str] = None,
        reraise: bool = True,
        log_level: str = "error"
    ) -> Optional[Exception]:
        """
        处理异常
        
        Args:
            exception: 异常对象
            context: 异常上下文信息
            reraise: 是否重新抛出异常
            log_level: 日志级别
            
        Returns:
            处理后的异常对象（如果不重新抛出）
        """
        # 构建错误消息
        error_msg = str(exception)
        if context:
            error_msg = f"{context}: {error_msg}"
        
        # 记录日志
        if log_level == "error":
            logger.error(error_msg)
            logger.error(f"异常类型: {type(exception).__name__}")
            logger.error(f"异常详情: {traceback.format_exc()}")
        elif log_level == "warning":
            logger.warning(error_msg)
        elif log_level == "info":
            logger.info(error_msg)
        
        # 如果是自定义异常，直接处理
        if isinstance(exception, ConverterError):
            if reraise:
                raise exception
            return exception
        
        # 转换为自定义异常
        converted_exception = ConverterError(
            message=error_msg,
            error_code="UNKNOWN_ERROR",
            details={"original_exception": str(exception)}
        )
        
        if reraise:
            raise converted_exception
        return converted_exception
    
    @staticmethod
    def safe_execute(
        func: Callable,
        *args,
        context: Optional[str] = None,
        default_return: Any = None,
        **kwargs
    ) -> Any:
        """
        安全执行函数
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            context: 执行上下文
            default_return: 异常时的默认返回值
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果或默认返回值
        """
        try:
            return func(*args, **kwargs)
        except Exception as e:
            ErrorHandler.handle_exception(
                e,
                context=context or f"执行函数 {func.__name__}",
                reraise=False
            )
            return default_return
    
    @staticmethod
    def retry_on_error(
        max_retries: int = 3,
        delay: float = 1.0,
        backoff_factor: float = 2.0,
        exceptions: tuple = (Exception,)
    ):
        """
        重试装饰器
        
        Args:
            max_retries: 最大重试次数
            delay: 初始延迟时间（秒）
            backoff_factor: 延迟时间倍增因子
            exceptions: 需要重试的异常类型
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs) -> Any:
                import time
                
                last_exception = None
                current_delay = delay
                
                for attempt in range(max_retries + 1):
                    try:
                        return func(*args, **kwargs)
                    except exceptions as e:
                        last_exception = e
                        
                        if attempt < max_retries:
                            logger.warning(
                                f"函数 {func.__name__} 执行失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}"
                            )
                            logger.info(f"等待 {current_delay:.1f} 秒后重试...")
                            time.sleep(current_delay)
                            current_delay *= backoff_factor
                        else:
                            logger.error(
                                f"函数 {func.__name__} 在 {max_retries + 1} 次尝试后仍然失败"
                            )
                            break
                
                # 重新抛出最后一个异常
                if last_exception:
                    raise last_exception
                    
            return wrapper
        return decorator


def handle_errors(
    context: Optional[str] = None,
    reraise: bool = True,
    log_level: str = "error"
):
    """
    错误处理装饰器
    
    Args:
        context: 错误上下文
        reraise: 是否重新抛出异常
        log_level: 日志级别
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_context = context or f"执行函数 {func.__name__}"
                return ErrorHandler.handle_exception(
                    e,
                    context=error_context,
                    reraise=reraise,
                    log_level=log_level
                )
        return wrapper
    return decorator


def log_performance(func: Callable) -> Callable:
    """
    性能日志装饰器
    
    记录函数执行时间。
    """
    @wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        import time
        
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"函数 {func.__name__} 执行完成，耗时: {execution_time:.3f} 秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"函数 {func.__name__} 执行失败，耗时: {execution_time:.3f} 秒")
            raise
    return wrapper
