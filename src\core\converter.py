"""
主转换器

LaTeX到Word转换系统的主要转换器类。
"""

from pathlib import Path
from typing import Dict, List, Any, Optional, Union

from .exceptions import ConverterError, LaTeXParseError, WordGenerationError
from ..utils.logger import get_logger
from ..utils.error_handler import handle_errors, log_performance
from ..utils.config_manager import ConfigManager
from ..utils.file_utils import FileUtils

logger = get_logger(__name__)


class LatexToWordConverter:
    """
    LaTeX到Word转换器
    
    主要的转换器类，协调各个模块完成LaTeX到Word的转换。
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化转换器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or ConfigManager()
        self.config = self.config_manager.get_config()
        
        # 初始化子模块
        self._init_modules()
        
        # 转换统计
        self.conversion_stats = {
            'total_conversions': 0,
            'successful_conversions': 0,
            'failed_conversions': 0,
            'total_formulas': 0,
            'successful_formulas': 0,
            'processing_time': 0.0
        }
    
    def _init_modules(self) -> None:
        """初始化各个模块"""
        # 延迟导入以避免循环依赖
        from ..parsers.latex_parser import LaTeXParser
        from ..converters.formula_converter import FormulaConverter
        from ..generators.word_generator import WordGenerator
        
        self.latex_parser = LaTeXParser(self.config.latex_parser)
        self.formula_converter = FormulaConverter(self.config_manager)

        # 延迟导入Word生成器以避免循环依赖
        from ..generators.word_generator import WordGenerator
        self.word_generator = WordGenerator(self.config_manager)
        
        logger.info("转换器模块初始化完成")
    
    @handle_errors(context="转换LaTeX文档")
    @log_performance
    def convert(
        self,
        input_path: Union[str, Path],
        output_path: Union[str, Path],
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        转换LaTeX文档为Word文档
        
        Args:
            input_path: 输入LaTeX文件路径
            output_path: 输出Word文件路径
            options: 转换选项
            
        Returns:
            转换结果字典
        """
        input_path = Path(input_path)
        output_path = Path(output_path)
        options = options or {}
        
        logger.info(f"开始转换: {input_path} -> {output_path}")
        
        try:
            # 1. 解析LaTeX文档
            logger.info("步骤1: 解析LaTeX文档")
            document = self.latex_parser.parse_file(input_path)
            
            # 2. 转换数学公式
            logger.info("步骤2: 转换数学公式")
            formula_results = self._convert_formulas(document.formulas)
            
            # 3. 生成Word文档
            logger.info("步骤3: 生成Word文档")
            word_result = self._generate_word_document(document, formula_results, output_path)
            
            # 更新统计
            self.conversion_stats['total_conversions'] += 1
            self.conversion_stats['successful_conversions'] += 1
            self.conversion_stats['total_formulas'] += len(document.formulas)
            self.conversion_stats['successful_formulas'] += sum(
                1 for r in formula_results if r.success
            )
            
            result = {
                'success': True,
                'input_file': str(input_path),
                'output_file': str(output_path),
                'document_info': {
                    'title': document.title,
                    'author': document.author,
                    'sections': len(document.sections),
                    'formulas': len(document.formulas),
                    'tables': len(document.tables),
                    'figures': len(document.figures)
                },
                'formula_conversion': {
                    'total': len(document.formulas),
                    'successful': sum(1 for r in formula_results if r.success),
                    'failed': sum(1 for r in formula_results if not r.success)
                },
                'processing_time': 0.0  # 将由装饰器填充
            }
            
            logger.info("转换完成")
            return result
            
        except Exception as e:
            self.conversion_stats['failed_conversions'] += 1
            logger.error(f"转换失败: {e}")
            
            return {
                'success': False,
                'error': str(e),
                'input_file': str(input_path),
                'output_file': str(output_path)
            }
    
    def _convert_formulas(self, formulas: List[Dict[str, Any]]) -> List[Any]:
        """转换数学公式"""
        if not formulas:
            return []
        
        logger.info(f"开始转换 {len(formulas)} 个公式")
        
        # 准备公式数据
        formula_data = []
        for i, formula in enumerate(formulas):
            formula_data.append({
                'id': f'formula_{i}',
                'content': formula['content'],
                'type': formula['type'],
                'original': formula
            })
        
        # 批量转换
        results = self.formula_converter.convert_formulas_batch(formula_data)
        
        logger.info(f"公式转换完成: {sum(1 for r in results if r.success)}/{len(results)} 成功")
        
        return results

    def _generate_word_document(
        self,
        document: Any,
        formula_results: List[Any],
        output_path: Path
    ) -> Dict[str, Any]:
        """生成Word文档"""
        try:
            # 准备文档数据
            latex_document = {
                'title': document.title,
                'author': document.author,
                'date': document.date,
                'sections': document.sections,
                'formulas': document.formulas,
                'tables': document.tables,
                'figures': document.figures
            }

            # 生成Word文档
            result = self.word_generator.generate_document(
                latex_document,
                formula_results,
                output_path
            )

            return result

        except Exception as e:
            raise WordGenerationError(
                f"Word文档生成失败: {e}",
                document_section="document_generation"
            )

    def convert_string(
        self,
        latex_content: str,
        output_path: Union[str, Path],
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        转换LaTeX字符串为Word文档
        
        Args:
            latex_content: LaTeX内容字符串
            output_path: 输出Word文件路径
            options: 转换选项
            
        Returns:
            转换结果字典
        """
        output_path = Path(output_path)
        options = options or {}
        
        logger.info("开始转换LaTeX字符串")
        
        try:
            # 解析LaTeX内容
            document = self.latex_parser.parse(latex_content)
            
            # 转换公式
            formula_results = self._convert_formulas(document.formulas)
            
            # 生成Word文档
            # word_result = self._generate_word_document(document, formula_results, output_path)
            
            result = {
                'success': True,
                'output_file': str(output_path),
                'document_info': {
                    'title': document.title,
                    'sections': len(document.sections),
                    'formulas': len(document.formulas)
                },
                'formula_conversion': {
                    'total': len(document.formulas),
                    'successful': sum(1 for r in formula_results if r.success)
                }
            }
            
            logger.info("字符串转换完成")
            return result
            
        except Exception as e:
            logger.error(f"字符串转换失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'output_file': str(output_path)
            }
    
    def get_conversion_stats(self) -> Dict[str, Any]:
        """获取转换统计信息"""
        return self.conversion_stats.copy()
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.conversion_stats = {
            'total_conversions': 0,
            'successful_conversions': 0,
            'failed_conversions': 0,
            'total_formulas': 0,
            'successful_formulas': 0,
            'processing_time': 0.0
        }
    
    def validate_input(self, input_path: Union[str, Path]) -> Dict[str, Any]:
        """
        验证输入文件
        
        Args:
            input_path: 输入文件路径
            
        Returns:
            验证结果字典
        """
        input_path = Path(input_path)
        
        result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'file_info': {}
        }
        
        try:
            # 检查文件是否存在
            if not input_path.exists():
                result['valid'] = False
                result['errors'].append(f"文件不存在: {input_path}")
                return result
            
            # 检查文件扩展名
            if input_path.suffix.lower() != '.tex':
                result['warnings'].append("文件扩展名不是.tex")
            
            # 获取文件信息
            file_info = FileUtils.get_file_info(input_path)
            result['file_info'] = file_info
            
            # 检查文件大小
            if file_info['size'] > 10 * 1024 * 1024:  # 10MB
                result['warnings'].append("文件较大，处理可能需要较长时间")
            
            # 尝试读取文件
            try:
                content = FileUtils.read_text_file(input_path)
                
                # 基本语法检查
                syntax_errors = self.latex_parser.validate_syntax(content)
                if syntax_errors:
                    result['warnings'].extend([
                        f"语法问题: {error['message']}" for error in syntax_errors[:5]
                    ])
                    if len(syntax_errors) > 5:
                        result['warnings'].append(f"还有 {len(syntax_errors) - 5} 个语法问题...")
                
            except Exception as e:
                result['valid'] = False
                result['errors'].append(f"文件读取失败: {e}")
            
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"验证过程出错: {e}")
        
        return result
    
    def preview_conversion(self, input_path: Union[str, Path]) -> Dict[str, Any]:
        """
        预览转换结果
        
        Args:
            input_path: 输入文件路径
            
        Returns:
            预览结果字典
        """
        input_path = Path(input_path)
        
        try:
            # 解析文档
            document = self.latex_parser.parse_file(input_path)
            
            # 分析公式
            formula_analysis = []
            for formula in document.formulas[:10]:  # 只分析前10个公式
                from ..converters.formula_classifier import FormulaClassifier
                classifier = FormulaClassifier()
                recommendations = classifier.get_conversion_recommendations(formula['content'])
                formula_analysis.append({
                    'content': formula['content'][:50] + '...' if len(formula['content']) > 50 else formula['content'],
                    'type': formula['type'],
                    'complexity': recommendations['complexity'],
                    'conversion_method': recommendations['conversion_method'],
                    'estimated_time': recommendations['estimated_processing_time']
                })
            
            preview = {
                'document_info': {
                    'title': document.title,
                    'author': document.author,
                    'document_class': document.document_class,
                    'packages': document.packages[:10],  # 只显示前10个包
                    'sections': len(document.sections),
                    'formulas': len(document.formulas),
                    'tables': len(document.tables),
                    'figures': len(document.figures)
                },
                'structure_preview': [
                    {
                        'title': section['title'],
                        'level': section['level'],
                        'content_length': len(section['content'])
                    }
                    for section in document.sections[:5]  # 前5个章节
                ],
                'formula_analysis': formula_analysis,
                'estimated_processing_time': sum(
                    fa['estimated_time'] for fa in formula_analysis
                ) + len(document.sections) * 0.1,  # 估算总时间
                'complexity_distribution': self._analyze_complexity_distribution(document.formulas)
            }
            
            return preview
            
        except Exception as e:
            logger.error(f"预览生成失败: {e}")
            return {
                'error': str(e)
            }
    
    def _analyze_complexity_distribution(self, formulas: List[Dict[str, Any]]) -> Dict[str, int]:
        """分析公式复杂度分布"""
        from ..converters.formula_classifier import FormulaClassifier
        
        classifier = FormulaClassifier()
        distribution = {'simple': 0, 'medium': 0, 'complex': 0, 'very_complex': 0}
        
        for formula in formulas:
            complexity = classifier.classify_complexity(formula['content'])
            distribution[complexity] = distribution.get(complexity, 0) + 1
        
        return distribution
