# Translations template for Sphinx.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-10-10 15:47+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: Georgian (http://app.transifex.com/sphinx-doc/sphinx-1/language/ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Language: ka\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

#: events.py:77
#, python-format
msgid "Event %r already present"
msgstr "მოვლენა %r უკვე არსებობს"

#: events.py:370
#, python-format
msgid "Unknown event name: %s"
msgstr "უცნობი მოვლენის სახელი: %s"

#: events.py:416
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr "დამმუშავებელმა %r მოვლენისთვის %r გამონაკლისი გადმოგვცა"

#: application.py:186
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "წყარო საქაღალდე ვერ ვიპოვე (%s)"

#: application.py:190
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr "გამოტანის საქაღალდე (%s) საქაღალდე არაა"

#: application.py:194
msgid "Source directory and destination directory cannot be identical"
msgstr "საწყისი და სამიზნე საქაღალდე ერთი და იგივე არ შეიძლება, იყოს"

#: application.py:224
#, python-format
msgid "Running Sphinx v%s"
msgstr "გაშვებულია Sphinx v%s"

#: application.py:246
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "პროექტს Sphinx-ის მინიმალური v%s სჭირდება და ამიტომ ამ ვერსიით ვერ აიგება."

#: application.py:262
msgid "making output directory"
msgstr "გამოტანის საქაღალდის შექმნა"

#: application.py:267 registry.py:450
#, python-format
msgid "while setting up extension %s:"
msgstr "გაფართოების %s მორგებისას:"

#: application.py:273
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "'setup', როგორც ის conf.py-შია ამჟამად აღწერილი, Python-ის მიერ გამოძახებადი არაა. შეცვალეთ აღწერა, რათა ის გამოძახებადი ფუნქცია გახდეს. ეს საჭიროა, რათა conf.py-ი, როგორც Sphinx-ის გაფართოება, მოიქცეს."

#: application.py:308
#, python-format
msgid "loading translations [%s]... "
msgstr "თარგმანების ჩატვირთვა [%s]... "

#: application.py:325 util/display.py:90
msgid "done"
msgstr "შესრულებულია"

#: application.py:327
msgid "not available for built-in messages"
msgstr "ჩაშენებული შეტყობინებებისთვის ხელმისაწვდომი არაა"

#: application.py:341
msgid "loading pickled environment"
msgstr "დამჟავებული გარემოს ჩატვირთვა"

#: application.py:349
#, python-format
msgid "failed: %s"
msgstr "შეცდომით: %s"

#: application.py:362
msgid "No builder selected, using default: html"
msgstr "ამგები არჩეული არაა. ვიყენებ ნაგულისხმევს: html"

#: application.py:394
msgid "build finished with problems."
msgstr ""

#: application.py:396
msgid "build succeeded."
msgstr ""

#: application.py:400
msgid ""
"build finished with problems, 1 warning (with warnings treated as errors)."
msgstr ""

#: application.py:403
msgid "build finished with problems, 1 warning."
msgstr ""

#: application.py:405
msgid "build succeeded, 1 warning."
msgstr ""

#: application.py:410
#, python-format
msgid ""
"build finished with problems, %s warnings (with warnings treated as errors)."
msgstr ""

#: application.py:413
#, python-format
msgid "build finished with problems, %s warnings."
msgstr ""

#: application.py:415
#, python-format
msgid "build succeeded, %s warnings."
msgstr ""

#: application.py:964
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "კვანძის კლასი %r უკვე რეგისტრირებულია. მისი მნახველები გადაფარული იქნება"

#: application.py:1043
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr "დირექტივა %r უკვე რეგისტრირებულია. ის გადაფარული იქნება"

#: application.py:1065 application.py:1090
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr "როლი %r უკვე რეგისტრირებულია. ის გადაფარული იქნება"

#: application.py:1640
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "გაფართოება %s არ აღწერს, არის თუ არა ის უსაფრთხო პარალელური წაკითხვისთვის. ვთვლით, რომ არა - კითხეთ გაფართოების ავტორს და აშკარად აღწერეთ ის"

#: application.py:1644
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "%s გაფართოება პარალელური წაკითხვისთვის უსაფრთხო არაა"

#: application.py:1647
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "გაფართოება %s არ აღწერს, არის თუ არა ის უსაფრთხო პარალელური ჩაწერისთვის. ვთვლით, რომ არა - კითხეთ გაფართოების ავტორს და აშკარად აღწერეთ ის"

#: application.py:1651
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "%s გაფართოება პარალელური ჩაწერისთვის უსაფრთხო არაა"

#: application.py:1659 application.py:1663
#, python-format
msgid "doing serial %s"
msgstr "ვაკეთებ სერიულს %s"

#: roles.py:205
#, python-format
msgid "Common Vulnerabilities and Exposures; CVE %s"
msgstr ""

#: roles.py:228
#, python-format
msgid "invalid CVE number %s"
msgstr ""

#: roles.py:249
#, python-format
msgid "Common Weakness Enumeration; CWE %s"
msgstr ""

#: roles.py:272
#, python-format
msgid "invalid CWE number %s"
msgstr ""

#: roles.py:293
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "Python-ის განვითარების შეთავაზებები; PEP %s"

#: roles.py:316
#, python-format
msgid "invalid PEP number %s"
msgstr "არასწორი PEP ნომერი %s"

#: roles.py:354
#, python-format
msgid "invalid RFC number %s"
msgstr "არასწორი RFC ნომერი %s"

#: registry.py:142
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "ამგებ კლასს %s \"name\" ატრიბუტი არ გააჩნია"

#: registry.py:144
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "ამგები %r უკვე არსებობს (მოდულში %s)"

#: registry.py:157
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "ამგების სახელი %s რეგისტრირებული არაა ან შესვლის წერტილში ხელმისაწვდომი არაა"

#: registry.py:164
#, python-format
msgid "Builder name %s not registered"
msgstr "ამგების სახელი %s რეგისტრირებული არაა"

#: registry.py:171
#, python-format
msgid "domain %s already registered"
msgstr "დომენი %s უკვე რეგისტრირებულია"

#: registry.py:194 registry.py:207 registry.py:218
#, python-format
msgid "domain %s not yet registered"
msgstr "დომენის %s ჯერ რეგისტრირებული არაა"

#: registry.py:198
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "%r დირექტივა დომენისთვის %s უკვე რეგისტრირებულია"

#: registry.py:210
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "%r როლი დომენისთვის %s უკვე რეგისტრირებულია"

#: registry.py:221
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "%r ინდექსი დომენისთვის %s უკვე რეგისტრირებულია"

#: registry.py:252
#, python-format
msgid "The %r object_type is already registered"
msgstr "%r ობიექტის ტიპი უკვე რეგისტრირებულია"

#: registry.py:278
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "%r ჯვარედინი მიმართვის ტიპი უკვე რეგისტრირებულია"

#: registry.py:285
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r უკვე რეგისტრირებულია"

#: registry.py:294
#, python-format
msgid "source_parser for %r is already registered"
msgstr "source_parser %r-სთვის უკვე რეგისტრირებულია"

#: registry.py:302
#, python-format
msgid "Source parser for %s not registered"
msgstr "წყაროს დამმუშავებელი %s-სთვის რეგისტრირებული არაა"

#: registry.py:318
#, python-format
msgid "Translator for %r already exists"
msgstr "მთარგმნელი %r-სთვის უკვე არსებობს"

#: registry.py:334
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "kwarg-ები add_node()-სთვის (შემომავალი, გამავალი) ფუნქციის კორტეჟი უნდა იყოს: %r=%r"

#: registry.py:417
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r უკვე რეგისტრირებულია"

#: registry.py:429
#, python-format
msgid "math renderer %s is already registered"
msgstr "მათემატიკის რენდერერი %s უკვე რეგისტრირებულია"

#: registry.py:444
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "გაფართოება %r %s ვერსიის შემდეგ Sphinx-ის ნაწილია. გაფართოება გამოტოვებულია."

#: registry.py:455
msgid "Original exception:\n"
msgstr "საწყისი გამონაკლისი:\n"

#: registry.py:456
#, python-format
msgid "Could not import extension %s"
msgstr "გაფართოების (%s) შემოტანა შეუძლებელია"

#: registry.py:461
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "გაფართოებას %r ფუნქცია setup() არ აქვს. დარწმუნებული ბრძანდებით, რომ ეს ნამდვილად Sphinx-ის გაფართოების მოდულია?"

#: registry.py:470
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "ამ პროექტში გამოყენებულ გაფართოებას %s Sphinx-ის მინიმუმ v%s სჭირდება. ამიტომ მას ამ ვერსიით ვერ ააგებთ."

#: registry.py:478
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "გაფართოებამ %r setup() ფუნქციიდან მხარდაუჭერელი ობიექტი დააბრუნა. მან ან არაფერი, ან მეტამონაცემების ლექსიკონი უნდა დააბრუნოს"

#: registry.py:512
#, python-format
msgid "`None` is not a valid filetype for %r."
msgstr ""

#: project.py:71
#, python-format
msgid ""
"multiple files found for the document \"%s\": %s\n"
"Use %r for the build."
msgstr ""

#: project.py:87
#, python-format
msgid "Ignored unreadable document %r."
msgstr ""

#: highlighting.py:168
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr "Pygments lexer-ის სახელი %r უცნობია"

#: highlighting.py:202
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr ""

#: extension.py:55
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "%s გაფართოება საჭიროა needs_extensons პარამეტრის მიერ, მაგრამ ჩატვირთული არაა."

#: extension.py:76
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "პროექტს გაფართოების %s ვერსია მინიმუმ %s სჭირდება და ამიტომ ჩატვირთული ვერსიით (%s) აგებული ვერ იქნება."

#: theming.py:121
#, python-format
msgid ""
"Theme configuration sections other than [theme] and [options] are not "
"supported (tried to get a value from %r)."
msgstr ""

#: theming.py:127
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "პარამეტრი %s.%s თემის კონფიგურაციებში აღმოჩენილი არაა"

#: theming.py:142
#, python-format
msgid "unsupported theme option %r given"
msgstr "თემის პარამეტრი %r მხარდაჭერილი არაა"

#: theming.py:215
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr ""

#: theming.py:236
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr ""

#: theming.py:276
#, python-format
msgid "The %r theme has circular inheritance"
msgstr ""

#: theming.py:283
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr ""

#: theming.py:290
#, python-format
msgid "The %r theme has too many ancestors"
msgstr ""

#: theming.py:318
#, python-format
msgid "no theme configuration file found in %r"
msgstr ""

#: theming.py:346 theming.py:399
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr ""

#: theming.py:350
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr ""

#: theming.py:354 theming.py:402
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr ""

#: theming.py:358
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr ""

#: theming.py:377
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr ""

#: config.py:314
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "კონფიგურაციის საქაღალდე ფაილს conf.py არ შეიცავს (%s)"

#: config.py:323
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr "აღმოჩენილია არასწორი კონფიგურაციის მნიშვნელობა: 'language = None'. განაახლეთ კონფიგურაცია და მიუთითეთ სწორი ენა. გადაირთვება 'en'-ზე (ინგლისური)."

#: config.py:346
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "ლექსიკონის კონფიგურაციის პარამეტრის %r გადაფარვა შეუძლებელია. ის გამოტოვებული იქნება (ინდივიდუალური ელემენტების დასაყენებლად გამოიყენეთ %r)"

#: config.py:355
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "რიცხვი %r კონფიგურაციის მნიშვნელობისთვის %r არასწორია. ის გამოტოვებული იქნება"

#: config.py:361
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "მხარდაუჭერელი ტიპის მქონე კონფიგურაციის პარამეტრის %r გადაფარვა შეუძლებელია. ის გამოტოვებული იქნება"

#: config.py:382
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "გადაფარვაში მითითებული კონფიგურაციის მნიშვნელობა %r უცნობია. ის გამოტოვებული იქნება"

#: config.py:435
#, python-format
msgid "No such config value: %r"
msgstr ""

#: config.py:458
#, python-format
msgid "Config value %r already present"
msgstr "კონფიგურაციის მნიშვნელობა %r უკვე არსებობს"

#: config.py:494
#, python-format
msgid ""
"cannot cache unpickable configuration value: %r (because it contains a "
"function, class, or module object)"
msgstr ""

#: config.py:531
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "თქვენს კონფიგურაციის ფაილში აღმოჩენილია შეცდომა: %s\n"

#: config.py:534
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "კონფგურაციის ფაილმა (ან მოდულმა, რომელის მან შემოიტანა) sys.exit() გამოიძახა"

#: config.py:541
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "თქვენს კონფიგურაციის ფაილში პროგრამირებადი შეცდომაა:\n\n%s"

#: config.py:564
#, python-format
msgid "Failed to convert %r to a set or tuple"
msgstr ""

#: config.py:585 config.py:590
#, python-format
msgid "Converting `source_suffix = %r` to `source_suffix = %r`."
msgstr ""

#: config.py:593
#, python-format
msgid ""
"The config value `source_suffix' expects a dictionary, a string, or a list "
"of strings. Got `%r' instead (type %s)."
msgstr ""

#: config.py:612
#, python-format
msgid "Section %s"
msgstr "სექცია %s"

#: config.py:613
#, python-format
msgid "Fig. %s"
msgstr "ნახ. %s"

#: config.py:614
#, python-format
msgid "Table %s"
msgstr "ცხრილი %s"

#: config.py:615
#, python-format
msgid "Listing %s"
msgstr "ჩამონათვალი %s"

#: config.py:722
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "კონფიგურაციის მნიშვნელობა `{name}` შეიძლება იყოს ერთ-ერთ სიიდან `{candidates}`, თქვენ კი `{current}` მიუთითეთ."

#: config.py:746
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "კონფიგურაციის მნიშვნელობის `{name}` ტიპია `{current.__name__}`, მე კი {permitted}-ს ველოდებოდი."

#: config.py:759
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "კონფიგურაციის მნიშვნელობის `{name}` ტიპია `{current.__name__}`, ნაგულისხმებია `{default.__name__}`."

#: config.py:770
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "primary_domain %r ვერ ვიპოვე. ის გამოტოვებული იქნება."

#: config.py:782
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr "Sphinx v2.0-ის შემდეგ root_doc-ს ნაგულისხმევად \"index\"-ს იყენებს. დაამატეთ თქვენს conf.py-ში 'root_doc = 'contents'\"."

#: domains/rst.py:127 domains/rst.py:184
#, python-format
msgid "%s (directive)"
msgstr "%s (დირექტივა)"

#: domains/rst.py:185 domains/rst.py:189
#, python-format
msgid ":%s: (directive option)"
msgstr ":%s: (დირექტივის პარამეტრი)"

#: domains/rst.py:213
#, python-format
msgid "%s (role)"
msgstr "%s (როლი)"

#: domains/rst.py:223
msgid "directive"
msgstr "დირექტივა"

#: domains/rst.py:224
msgid "directive-option"
msgstr "დირექტივის-პარამეტრი"

#: domains/rst.py:225
msgid "role"
msgstr "როლები"

#: domains/rst.py:247
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr "%s %s-ის დუბლირებული აღწერა. სხვა ასლი %s-შია"

#: domains/javascript.py:165
#, python-format
msgid "%s() (built-in function)"
msgstr "%s () (ჩაშენებული ფუნქცია)"

#: domains/javascript.py:166 domains/python/__init__.py:253
#, python-format
msgid "%s() (%s method)"
msgstr "%s () (%s მეთოდი)"

#: domains/javascript.py:168
#, python-format
msgid "%s() (class)"
msgstr "%s () (კლასი)"

#: domains/javascript.py:170
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (გლობალური ცვლადი ან მუდმივა)"

#: domains/javascript.py:172 domains/python/__init__.py:338
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (%s ატრიბუტი)"

#: domains/javascript.py:255
msgid "Arguments"
msgstr "არგუმენტები"

#: domains/cpp/__init__.py:442 domains/javascript.py:258
msgid "Throws"
msgstr "ისვრის"

#: domains/c/__init__.py:304 domains/cpp/__init__.py:453
#: domains/javascript.py:261 domains/python/_object.py:176
msgid "Returns"
msgstr "აბრუნებს"

#: domains/c/__init__.py:306 domains/javascript.py:263
#: domains/python/_object.py:178
msgid "Return type"
msgstr "დაბრუნების ტიპი"

#: domains/javascript.py:334
#, python-format
msgid "%s (module)"
msgstr "%s (მოდული)"

#: domains/c/__init__.py:675 domains/cpp/__init__.py:854
#: domains/javascript.py:371 domains/python/__init__.py:629
msgid "function"
msgstr "ფუნქცია"

#: domains/javascript.py:372 domains/python/__init__.py:633
msgid "method"
msgstr "მეთოდი"

#: domains/cpp/__init__.py:852 domains/javascript.py:373
#: domains/python/__init__.py:631
msgid "class"
msgstr "კლასი"

#: domains/javascript.py:374 domains/python/__init__.py:630
msgid "data"
msgstr "მონაცემები"

#: domains/javascript.py:375 domains/python/__init__.py:636
msgid "attribute"
msgstr "ატრიბუტი"

#: domains/javascript.py:376 domains/python/__init__.py:639
msgid "module"
msgstr "მოდული"

#: domains/javascript.py:407
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr ""

#: domains/changeset.py:25
#, python-format
msgid "Added in version %s"
msgstr ""

#: domains/changeset.py:26
#, python-format
msgid "Changed in version %s"
msgstr "ცვლილებები ვერსიაში %s"

#: domains/changeset.py:27
#, python-format
msgid "Deprecated since version %s"
msgstr "მოძველებულია ვერსიაში %s"

#: domains/changeset.py:28
#, python-format
msgid "Removed in version %s"
msgstr ""

#: domains/__init__.py:299
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: domains/citation.py:73
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr ""

#: domains/citation.py:84
#, python-format
msgid "Citation [%s] is not referenced."
msgstr ""

#: domains/math.py:63
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr ""

#: domains/math.py:119 writers/latex.py:2479
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "არასწორი math_eqref_format: %r"

#: environment/__init__.py:86
msgid "new config"
msgstr "ახალი კონფიგურაცია"

#: environment/__init__.py:87
msgid "config changed"
msgstr "კონფიგურაცია შეიცვალა"

#: environment/__init__.py:88
msgid "extensions changed"
msgstr "გაფართოებები შეიცვალა"

#: environment/__init__.py:249
msgid "build environment version not current"
msgstr "აგების გარემოს ვერსია მიმდინარე არაა"

#: environment/__init__.py:251
msgid "source directory has changed"
msgstr "საწყისი საქაღალდე შეიცვალა"

#: environment/__init__.py:311
#, python-format
msgid "The configuration has changed (1 option: %r)"
msgstr ""

#: environment/__init__.py:316
#, python-format
msgid "The configuration has changed (%d options: %s)"
msgstr ""

#: environment/__init__.py:322
#, python-format
msgid "The configuration has changed (%d options: %s, ...)"
msgstr ""

#: environment/__init__.py:364
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "გარემო არჩეულ ამგებთან თავსებადი არაა. აირჩიეთ სხვა დოკუმენტების ხის საქაღალდე."

#: environment/__init__.py:473
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "%s-ში დოკუმენტების სკანირება შეუძლებელია: %r"

#: environment/__init__.py:622
#, python-format
msgid "Domain %r is not registered"
msgstr "დომენი %r რეგისტრირებული არაა"

#: environment/__init__.py:773
msgid "document isn't included in any toctree"
msgstr "დოკუმენტი არც ერთ სარჩევის ხეში ჩასმული არაა"

#: environment/__init__.py:806
msgid "self referenced toctree found. Ignored."
msgstr "აღმოჩენილია თვითმიმართვადი სარჩევის ხე. გამოტოვებულია."

#: environment/__init__.py:835
#, python-format
msgid "document is referenced in multiple toctrees: %s, selecting: %s <- %s"
msgstr ""

#: locale/__init__.py:229
msgid "Attention"
msgstr "ყურადღება"

#: locale/__init__.py:230
msgid "Caution"
msgstr "გაფრთხილება"

#: locale/__init__.py:231
msgid "Danger"
msgstr "საფრთხე"

#: locale/__init__.py:232
msgid "Error"
msgstr "შეცდომა"

#: locale/__init__.py:233
msgid "Hint"
msgstr "მინიშნება"

#: locale/__init__.py:234
msgid "Important"
msgstr "მნიშვნელოვანი"

#: locale/__init__.py:235
msgid "Note"
msgstr "ნოტი"

#: locale/__init__.py:236
msgid "See also"
msgstr "ასევე იხილეთ"

#: locale/__init__.py:237
msgid "Tip"
msgstr "რჩევა"

#: locale/__init__.py:238
msgid "Warning"
msgstr "ყურადღება"

#: cmd/quickstart.py:43
msgid "automatically insert docstrings from modules"
msgstr ""

#: cmd/quickstart.py:44
msgid "automatically test code snippets in doctest blocks"
msgstr ""

#: cmd/quickstart.py:45
msgid "link between Sphinx documentation of different projects"
msgstr ""

#: cmd/quickstart.py:46
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr ""

#: cmd/quickstart.py:47
msgid "checks for documentation coverage"
msgstr "დოკუმენტაციის დაფარვის შემოწმება"

#: cmd/quickstart.py:48
msgid "include math, rendered as PNG or SVG images"
msgstr "მათემატიკის ჩასმა, რომელიც PNG ან SVG გამოსახულების სახითაა დარენდერებული"

#: cmd/quickstart.py:49
msgid "include math, rendered in the browser by MathJax"
msgstr "ბრაუზერში MathJax-ის მიერ დარენდერებული მათემატიკის ჩასმა"

#: cmd/quickstart.py:50
msgid "conditional inclusion of content based on config values"
msgstr ""

#: cmd/quickstart.py:51
msgid "include links to the source code of documented Python objects"
msgstr ""

#: cmd/quickstart.py:52
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr ""

#: cmd/quickstart.py:94
msgid "Please enter a valid path name."
msgstr "გთხოვთ შეიყვანოთ ბილიკის სწორი სახელი."

#: cmd/quickstart.py:110
msgid "Please enter some text."
msgstr "შეიყვანეთ რაიმე ტექსტი."

#: cmd/quickstart.py:117
#, python-format
msgid "Please enter one of %s."
msgstr "შეიყვანეთ %s-დან ერთ-ერთი."

#: cmd/quickstart.py:125
msgid "Please enter either 'y' or 'n'."
msgstr "შეიყვანეთ 'y' (დიახ) ან 'n' (არა)"

#: cmd/quickstart.py:131
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "მიუთითეთ ფაილის სუფიქსი. მაგ: '.rst' ან '.txt'."

#: cmd/quickstart.py:213
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "მოგესალმებით Sphinx %s-ის სწრაფი მორგების პროგრამა."

#: cmd/quickstart.py:217
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr ""

#: cmd/quickstart.py:225
#, python-format
msgid "Selected root path: %s"
msgstr "არჩეული root ბილიკი: %s"

#: cmd/quickstart.py:228
msgid "Enter the root path for documentation."
msgstr "შეიყვანეთ დოკუმენტაციის ძირითადი ბილიკი."

#: cmd/quickstart.py:229
msgid "Root path for the documentation"
msgstr "დოკუმენტაციის ძირითადი ბილიკი"

#: cmd/quickstart.py:237
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "შეცდომა: არჩეულ ძირითად ბილიკზე აღმოჩენილია არსებული conf.py ფაილი."

#: cmd/quickstart.py:243
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "sphinx-quickstart-ი არსებულ Sphinx-ის პროექტებს თავზე არ გადააწერს."

#: cmd/quickstart.py:246
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "შეიყვანეთ ახალი საწყისი ბილიკი (გასასვლელად უბრალოდ დააწექით ღილაკს 'Enter')"

#: cmd/quickstart.py:256
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr ""

#: cmd/quickstart.py:263
msgid "Separate source and build directories (y/n)"
msgstr "კოდის და აგების საქაღალდეები განსხვავდება? (y(დიახ)/n(არა))"

#: cmd/quickstart.py:269
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr ""

#: cmd/quickstart.py:275
msgid "Name prefix for templates and static dir"
msgstr "სახელის პრეფიქსი ნიმუშებისა და სტატიკის საქაღალდეებისთვის"

#: cmd/quickstart.py:280
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "პროექტს სახელი აგებულ დოკუმენტაციაში რამდენიმე ადგილას გამოჩნდება."

#: cmd/quickstart.py:284
msgid "Project name"
msgstr "პროექტის სახელი"

#: cmd/quickstart.py:286
msgid "Author name(s)"
msgstr "ავტორის სახელები"

#: cmd/quickstart.py:291
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr ""

#: cmd/quickstart.py:299
msgid "Project version"
msgstr "პროექტის ვერსია"

#: cmd/quickstart.py:301
msgid "Project release"
msgstr "პროექტის რელიზი"

#: cmd/quickstart.py:306
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr ""

#: cmd/quickstart.py:315
msgid "Project language"
msgstr "პროექტის ენა"

#: cmd/quickstart.py:322
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr ""

#: cmd/quickstart.py:327
msgid "Source file suffix"
msgstr ""

#: cmd/quickstart.py:332
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr ""

#: cmd/quickstart.py:340
msgid "Name of your master document (without suffix)"
msgstr "თქვენი მთავარი დოკუმენტის სახელი (სუფიქსს გარეშე)"

#: cmd/quickstart.py:350
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr ""

#: cmd/quickstart.py:357
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "sphinx-quickstart არსებულ ფაილებს თავზე არ გადააწერს."

#: cmd/quickstart.py:360
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr ""

#: cmd/quickstart.py:369
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr ""

#: cmd/quickstart.py:379
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr ""

#: cmd/quickstart.py:389
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr ""

#: cmd/quickstart.py:395
msgid "Create Makefile? (y/n)"
msgstr "შევქმნა Makefile? (y(დიახ)/n(არა))"

#: cmd/quickstart.py:399
msgid "Create Windows command file? (y/n)"
msgstr "შევქმნა Windows-ის ბრძანებების ფაილი? (y(დიახ)/n(არა))"

#: cmd/quickstart.py:451 ext/apidoc.py:92
#, python-format
msgid "Creating file %s."
msgstr "ფაილის შექმნა %s."

#: cmd/quickstart.py:456 ext/apidoc.py:89
#, python-format
msgid "File %s already exists, skipping."
msgstr "ფაილი %s უკვე არსებობს. ის გამოტოვებული იქნება."

#: cmd/quickstart.py:499
msgid "Finished: An initial directory structure has been created."
msgstr "დასრულდა: საწყისი საქაღალდეების სტრუქტურა შეიქმნა."

#: cmd/quickstart.py:502
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr ""

#: cmd/quickstart.py:510
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr ""

#: cmd/quickstart.py:513
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr ""

#: cmd/quickstart.py:520
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr ""

#: cmd/quickstart.py:555
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr ""

#: cmd/build.py:153 cmd/quickstart.py:565 ext/apidoc.py:374
#: ext/autosummary/generate.py:765
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr "მეტი ინფორმაციის მისაღებად ეწვიეთ <https://www.sphinx-doc.org/>."

#: cmd/quickstart.py:575
msgid "quiet mode"
msgstr "ჩუმი რეჟიმი"

#: cmd/quickstart.py:585
msgid "project root"
msgstr "პროექტის საწყისი საქაღალდე"

#: cmd/quickstart.py:588
msgid "Structure options"
msgstr "სტრუქტურის მორგება"

#: cmd/quickstart.py:594
msgid "if specified, separate source and build dirs"
msgstr "თუ მითითებულია, კოდის და აგების საქაღალდეები ცალ-ცალკე იქნება"

#: cmd/quickstart.py:600
msgid "if specified, create build dir under source dir"
msgstr "თუ მითითებულია, აგების საქაღალდე კოდის საქაღალდეში იქნება"

#: cmd/quickstart.py:606
msgid "replacement for dot in _templates etc."
msgstr ""

#: cmd/quickstart.py:609
msgid "Project basic options"
msgstr "პროექტის ძირითადი პარამეტრები"

#: cmd/quickstart.py:611
msgid "project name"
msgstr "პროექტის დასახელება"

#: cmd/quickstart.py:614
msgid "author names"
msgstr "ავტორის სახელები"

#: cmd/quickstart.py:621
msgid "version of project"
msgstr "პროექტის ვერსია"

#: cmd/quickstart.py:628
msgid "release of project"
msgstr "პროექტის რელიზი"

#: cmd/quickstart.py:635
msgid "document language"
msgstr "დოკუმენტის ენა"

#: cmd/quickstart.py:638
msgid "source file suffix"
msgstr "წყაროს ფაილის სუფიქსი"

#: cmd/quickstart.py:641
msgid "master document name"
msgstr "მთავარი დოკუმენტის სახელი"

#: cmd/quickstart.py:644
msgid "use epub"
msgstr "epub-ის გამოყენება"

#: cmd/quickstart.py:647
msgid "Extension options"
msgstr "გაფართოების პარამეტრები"

#: cmd/quickstart.py:654 ext/apidoc.py:578
#, python-format
msgid "enable %s extension"
msgstr "%s გაფართოების ჩართვა"

#: cmd/quickstart.py:661 ext/apidoc.py:570
msgid "enable arbitrary extensions"
msgstr ""

#: cmd/quickstart.py:664
msgid "Makefile and Batchfile creation"
msgstr "Makefile და Batchfile-ის შექმნა"

#: cmd/quickstart.py:670
msgid "create makefile"
msgstr "makefile-ის შექმნა"

#: cmd/quickstart.py:676
msgid "do not create makefile"
msgstr "makefile-ის არ შეიქმნება"

#: cmd/quickstart.py:683
msgid "create batchfile"
msgstr "batchfile-ის შექმნა"

#: cmd/quickstart.py:689
msgid "do not create batchfile"
msgstr "batchfile-ი არ შეიქმნება"

#: cmd/quickstart.py:698
msgid "use make-mode for Makefile/make.bat"
msgstr ""

#: cmd/quickstart.py:701 ext/apidoc.py:581
msgid "Project templating"
msgstr "პროექტის ნიმუშები"

#: cmd/quickstart.py:707 ext/apidoc.py:587
msgid "template directory for template files"
msgstr "ნიმუშების საქაღალდე ნიმუშის ფაილებისთვის"

#: cmd/quickstart.py:714
msgid "define a template variable"
msgstr "აღწერეთ სანიმუშე ცვლადი"

#: cmd/quickstart.py:749
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr ""

#: cmd/quickstart.py:768
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr ""

#: cmd/quickstart.py:775
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr ""

#: cmd/quickstart.py:793
#, python-format
msgid "Invalid template variable: %s"
msgstr "არასწორი ნიმუშის ცვლადი: %s"

#: cmd/build.py:49
msgid "Exception occurred while building, starting debugger:"
msgstr "აგებისას აღმოჩენილია გამონაკლისი. მიმდინარეობს გამმართველის გაშვება:"

#: _cli/util/errors.py:129 cmd/build.py:65
msgid "Interrupted!"
msgstr "შეწყდა!"

#: cmd/build.py:67
msgid "reST markup error:"
msgstr "reST მარკაფის შეცდომა:"

#: _cli/util/errors.py:143 cmd/build.py:73
msgid "Encoding error:"
msgstr "კოდირების შეცდომა:"

#: cmd/build.py:78 cmd/build.py:108
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr ""

#: _cli/util/errors.py:148 cmd/build.py:90
msgid "Recursion error:"
msgstr "რეკურსიის შეცდომა:"

#: _cli/util/errors.py:152 cmd/build.py:94
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr ""

#: _cli/util/errors.py:165 cmd/build.py:103
msgid "Exception occurred:"
msgstr "აღმოჩენილი გამონაკლისი:"

#: _cli/util/errors.py:178 cmd/build.py:117
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr ""

#: cmd/build.py:124
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr ""

#: cmd/build.py:144
msgid "job number should be a positive number"
msgstr "დავალების ნომერი დადებითი რიცხვი უნდა იყოს"

#: cmd/build.py:154
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr ""

#: cmd/build.py:180
msgid "path to documentation source files"
msgstr "ბილიკი დოკუმენტაციის კოდის ფაილებამდე"

#: cmd/build.py:183
msgid "path to output directory"
msgstr "ბილიკი გამოტანის საქაღალდემდე"

#: cmd/build.py:188
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr ""

#: cmd/build.py:194
msgid "general options"
msgstr "ზოგადი პარამეტრები"

#: cmd/build.py:201
msgid "builder to use (default: 'html')"
msgstr ""

#: cmd/build.py:210
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: cmd/build.py:220
msgid "write all files (default: only write new and changed files)"
msgstr "ყველა ფაილის ჩაწერა (ნაგულისხმევი: მხოლოდ ახალი და შეცვლილი ფაილების ჩაწერა)"

#: cmd/build.py:227
msgid "don't use a saved environment, always read all files"
msgstr "შენახული გარემო გამოყენებული არ იქნება. ყოველთვის მოხდება ყველა ფაილის წაკითხვა"

#: cmd/build.py:230
msgid "path options"
msgstr ""

#: cmd/build.py:236
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr ""

#: cmd/build.py:246
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr ""

#: cmd/build.py:257
msgid "use no configuration file, only use settings from -D options"
msgstr ""

#: cmd/build.py:266
msgid "override a setting in configuration file"
msgstr "კონფიგურაციის ფაილის პარამეტრის გადაფარვა"

#: cmd/build.py:275
msgid "pass a value into HTML templates"
msgstr "მნიშვნელობის გადაცემა HTML ნიმუშებში"

#: cmd/build.py:284
msgid "define tag: include \"only\" blocks with TAG"
msgstr ""

#: cmd/build.py:291
msgid "nitpicky mode: warn about all missing references"
msgstr ""

#: cmd/build.py:294
msgid "console output options"
msgstr "კონსოლის გამოტანის პარამეტრები"

#: cmd/build.py:301
msgid "increase verbosity (can be repeated)"
msgstr "დიაგნოსტიკური შეტყობინებების სიხშირის გაზრდა (შეგიძლიათ, გაიმეოროთ)"

#: cmd/build.py:308 ext/apidoc.py:413
msgid "no output on stdout, just warnings on stderr"
msgstr "stdout-ზე გამოტანილი არაფერი იქნება. მხოლოდ გაფრთხილებები, stderr-ზე"

#: cmd/build.py:315
msgid "no output at all, not even warnings"
msgstr "არაფრი გამოტანა. გაფრთხილებებისაც კი"

#: cmd/build.py:323
msgid "do emit colored output (default: auto-detect)"
msgstr "ფერადი გამოტანის ჩართვა (ნაგულისხმევი: ავტომატურად-აღმოჩენა)"

#: cmd/build.py:331
msgid "do not emit colored output (default: auto-detect)"
msgstr "ფერადი გამოტანის გამორთვა (ნაგულისხმევი: ავტომატურად-აღმოჩენა)"

#: cmd/build.py:334
msgid "warning control options"
msgstr ""

#: cmd/build.py:340
msgid "write warnings (and errors) to given file"
msgstr "გაფრთხილებების (და შეცდომების) მითითებულ ფაილში ჩაწერა"

#: cmd/build.py:347
msgid "turn warnings into errors"
msgstr "გაფრთხილებების შეცდომად აღქმა"

#: cmd/build.py:355
msgid "show full traceback on exception"
msgstr ""

#: cmd/build.py:358
msgid "run Pdb on exception"
msgstr "გამონაკლისისას Pdb-ის გაშვება"

#: cmd/build.py:364
msgid "raise an exception on warnings"
msgstr ""

#: cmd/build.py:407
msgid "cannot combine -a option and filenames"
msgstr "-a პარამეტრის და ფაილის სახელების ერთად მითითება შეუძლებელია"

#: cmd/build.py:439
#, python-format
msgid "cannot open warning file %r: %s"
msgstr "გაფრთხილებების ფაილის %r გახსნის შეცდომა: %s"

#: cmd/build.py:458
msgid "-D option argument must be in the form name=value"
msgstr ""

#: cmd/build.py:465
msgid "-A option argument must be in the form name=value"
msgstr ""

#: builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "სულელი ამგები, რომელიც ფაილებს არ აგენერირებს."

#: builders/linkcheck.py:60
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr ""

#: builders/linkcheck.py:137
#, python-format
msgid "broken link: %s (%s)"
msgstr "გაფუჭებული ბმული: %s (%s)"

#: builders/linkcheck.py:526
#, python-format
msgid "Anchor '%s' not found"
msgstr "მიმაგრება '%s' ვერ ვიპოვე"

#: builders/linkcheck.py:726
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr ""

#: builders/singlehtml.py:36
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "HTML გვერდის საქაღალდეა %(outdir)s."

#: builders/singlehtml.py:168
msgid "assembling single document"
msgstr "ერთი დოკუმენტის აწყობა"

#: builders/latex/__init__.py:349 builders/manpage.py:59
#: builders/singlehtml.py:173 builders/texinfo.py:120
msgid "writing"
msgstr "ჩაწერა"

#: builders/singlehtml.py:186
msgid "writing additional files"
msgstr "დამატებითი ფაილების ჩაწერა"

#: builders/manpage.py:39
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "სახელმძღვანელოს გვერდების საქაღალდეა %(outdir)s."

#: builders/manpage.py:47
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr ""

#: builders/manpage.py:76
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "\"man_pages\" პარამეტრის მნიშვნელობა უცნობ დოკუმენტზე %s მიუთითებს"

#: builders/text.py:34
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "ტექსტური ფაილების საქაღლდეა %(outdir)s."

#: builders/html/__init__.py:1213 builders/text.py:81 builders/xml.py:97
#, python-format
msgid "error writing file %s: %s"
msgstr "შეცდომა '%s' ფაილის ჩაწერისას: %s"

#: builders/xml.py:38
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "XML ფაილების საქაღალდეა %(outdir)s."

#: builders/xml.py:110
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "ფსევდო-XML ფაილებს საქაღალდეა %(outdir)s."

#: builders/texinfo.py:47
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "Texinfo-ის ფაილების საქაღალდეა %(outdir)s."

#: builders/texinfo.py:49
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr ""

#: builders/texinfo.py:78
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "კონფიგურაციის პარამეტრის \"texinfo_documents\" მნიშვნელობა მითითებული არაა. დოკუმენტების ჩაწერი არ იქნება"

#: builders/texinfo.py:90
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "\"texinfo_documents\" კონფიგურაციის პარამეტრი მიუთითებს უცნობ დოკუმენტზე %s"

#: builders/latex/__init__.py:327 builders/texinfo.py:114
#, python-format
msgid "processing %s"
msgstr "დამუშავება %s"

#: builders/latex/__init__.py:407 builders/texinfo.py:173
msgid "resolving references..."
msgstr "მიმართვების ამოხსნა..."

#: builders/latex/__init__.py:418 builders/texinfo.py:183
msgid " (in "
msgstr " ( "

#: builders/_epub_base.py:421 builders/html/__init__.py:757
#: builders/latex/__init__.py:485 builders/texinfo.py:201
msgid "copying images... "
msgstr "გამოსახულებების კოპირება... "

#: builders/_epub_base.py:443 builders/latex/__init__.py:500
#: builders/texinfo.py:218
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "გამოსახულების ფაილის %r კოპირების შეცდომა: %s"

#: builders/texinfo.py:225
msgid "copying Texinfo support files"
msgstr "მიმდინარეობს Texinfo-ის მხარდაჭერის ფაილების კოპირება"

#: builders/texinfo.py:233
#, python-format
msgid "error writing file Makefile: %s"
msgstr "შეცდომა Makefile-ის ჩაწერისას: %s"

#: builders/gettext.py:230
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "შეტყობინების კატალოგების საქაღალდეა %(outdir)s."

#: builders/__init__.py:371 builders/gettext.py:251
#, python-format
msgid "building [%s]: "
msgstr "აგება [%s]: "

#: builders/gettext.py:252
#, python-format
msgid "targets for %d template files"
msgstr "%d ნიმუშის ფაილის სამიზნეები"

#: builders/gettext.py:257
msgid "reading templates... "
msgstr "ნიმუშების კითხვა...  "

#: builders/gettext.py:292
msgid "writing message catalogs... "
msgstr "შეტყობინების კატალოგების ჩაწერა... "

#: builders/__init__.py:200
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr ""

#: builders/__init__.py:208
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr ""

#: builders/__init__.py:231
msgid "building [mo]: "
msgstr "აგება [mo]: "

#: builders/__init__.py:234 builders/__init__.py:729 builders/__init__.py:761
msgid "writing output... "
msgstr "გამოტანის ჩაწერა... "

#: builders/__init__.py:251
#, python-format
msgid "all of %d po files"
msgstr "სულ %d po ფაილი"

#: builders/__init__.py:273
#, python-format
msgid "targets for %d po files that are specified"
msgstr "%d po ფაილისთვის სამიზნე მითითებული არაა"

#: builders/__init__.py:285
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "%d po ფაილისთვის სამიზნე მოძველებულია"

#: builders/__init__.py:295
msgid "all source files"
msgstr "ყველა კოდის ფაილი"

#: builders/__init__.py:307
#, python-format
msgid "file %r given on command line does not exist, "
msgstr "ბრძანების სტრიქონში მითითებული ფაილი %r არ არსებობს. "

#: builders/__init__.py:313
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "ბრძანების სტრიქონში მითითებული ფაილი %r კოდის საქაღალდეში არაა. გამოტოვება"

#: builders/__init__.py:324
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr "ბრძანების სტრიქონში მითითებული ფაილი %r სწორი დოკუმენტი არაა. გამოტოვება"

#: builders/__init__.py:339
#, python-format
msgid "%d source files given on command line"
msgstr "ბრძანების სტრიქონში მითითებულია %d კოდის ფაილი"

#: builders/__init__.py:354
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "%d კოდის ფაილის სამიზნე მოძველებულია"

#: builders/__init__.py:382
msgid "looking for now-outdated files... "
msgstr "მოძველებული ფაილების ძებნა... "

#: builders/__init__.py:386
#, python-format
msgid "%d found"
msgstr "ნაპოვნია %d"

#: builders/__init__.py:388
msgid "none found"
msgstr "არაფერია ნაპოვნი"

#: builders/__init__.py:395
msgid "pickling environment"
msgstr "დამჟავების გარემო"

#: builders/__init__.py:402
msgid "checking consistency"
msgstr "თანმიმდევრულობის შემოწმება"

#: builders/__init__.py:406
msgid "no targets are out of date."
msgstr "მოძველებული სამიზნეები აღმოჩენილი არაა."

#: builders/__init__.py:446
msgid "updating environment: "
msgstr "გარემოს განახლება: "

#: builders/__init__.py:471
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%s დაემატა, %s შეიცვალა, %s წაიშალა"

#: builders/__init__.py:507
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches a "
"built-in exclude pattern %r. Please move your master document to a different"
" location."
msgstr ""

#: builders/__init__.py:516
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches an "
"exclude pattern specified in conf.py, %r. Please remove this pattern from "
"conf.py."
msgstr ""

#: builders/__init__.py:527
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it is not included"
" in the custom include_patterns = %r. Ensure that a pattern in "
"include_patterns matches the master document."
msgstr ""

#: builders/__init__.py:534
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s). The master document must "
"be within the source directory or a subdirectory of it."
msgstr ""

#: builders/__init__.py:553 builders/__init__.py:569
msgid "reading sources... "
msgstr "წყაროების კითხვა... "

#: builders/__init__.py:686
#, python-format
msgid "docnames to write: %s"
msgstr "ჩასაწერი დოკუმენტის სახელები: %s"

#: builders/__init__.py:699
msgid "preparing documents"
msgstr "დოკუმენტების მომზადება"

#: builders/__init__.py:702
msgid "copying assets"
msgstr ""

#: builders/__init__.py:845
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr ""

#: builders/epub3.py:83
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "EPub ფაილი %(outdir)s-შია."

#: builders/epub3.py:189
msgid "writing nav.xhtml file..."
msgstr "nav.xhtml ფაილის ჩაწერა..."

#: builders/epub3.py:220
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "კონფიგურაციის მნიშვნელობა \"epub_language\" (ან \"language\") EPUB3-სთვის ცარიელი არ უნდა ყოფილიყო"

#: builders/epub3.py:227
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "კონფიგურაციის მნიშვნელობა \"epub_uid\" EPUB3-სთვის XML NAME უნდა იყოს"

#: builders/epub3.py:231
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "კონფიგურაციის მნიშვნელობა \"epub_title\" (ან \"html_title\") EPUB3-სთვის ცარიელი არ უნდა იყოს"

#: builders/epub3.py:238
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "კონფიგურაციის მნიშვნელობა \"epub_author\" EPUB3-სთვის ცარიელი არ უნდა იყოს"

#: builders/epub3.py:242
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "კონფიგურაციის მნიშვნელობა \"epub_contributor\" EPUB3-სთვის ცარიელი არ უნდა იყოს"

#: builders/epub3.py:247
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "კონფიგურაციის მნიშვნელობა \"epub_description\" EPUB3-სთვის ცარიელი არ უნდა იყოს"

#: builders/epub3.py:251
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "კონფიგურაციის მნიშვნელობა \"epub_publisher\" EPUB3-სთვის ცარიელი არ უნდა იყოს"

#: builders/epub3.py:255
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "კონფიგურაციის მნიშვნელობა \"epub_copyright\" (ან \"copyright\") EPUB3-სთვის ცარიელი არ უნდა იყოს"

#: builders/epub3.py:262
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "კონფიგურაციის მნიშვნელობა \"epub_identifier\" EPUB3-სთვის ცარიელი არ უნდა იყოს"

#: builders/epub3.py:265
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "კონფიგურაციის მნიშვნელობა \"version\" EPUB3-სთვის ცარიელი არ უნდა იყოს"

#: builders/epub3.py:279 builders/html/__init__.py:1262
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "არასწორი css_file: %r. გამოტოვება"

#: builders/_epub_base.py:220
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "აღმოჩენილია დუბლირებული სარჩევის ჩანაწერი: %s"

#: builders/_epub_base.py:432
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "გამოსახულების ფაილი %r ვერ წავიკითხე: ის, სამაგიეროდ, დაკოპირდება"

#: builders/_epub_base.py:463
#, python-format
msgid "cannot write image file %r: %s"
msgstr "გამოსახულების ფაილის %r ჩაწერის შეცდომა: %s"

#: builders/_epub_base.py:475
msgid "Pillow not found - copying image files"
msgstr "Pillow ვერ ვიპოვე - სურათის ფაილების კოპირება"

#: builders/_epub_base.py:507
msgid "writing mimetype file..."
msgstr "mimetype ფაილის ჩაწერა..."

#: builders/_epub_base.py:516
msgid "writing META-INF/container.xml file..."
msgstr "მიმდინარეობს META-INF/container.xml ფაილის ჩაწერა..."

#: builders/_epub_base.py:553
msgid "writing content.opf file..."
msgstr "content.opf ფაილის ჩაწერა..."

#: builders/_epub_base.py:585
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "უცნობი mimetype ფაილისთვის %s. გამოტოვება"

#: builders/_epub_base.py:756
msgid "writing toc.ncx file..."
msgstr "toc.ncx ფაილის ჩაწერა..."

#: builders/_epub_base.py:785
#, python-format
msgid "writing %s file..."
msgstr "%s ფაილის ჩაწერა..."

#: builders/changes.py:33
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "გადახედვის ფაილის მდებარეობაა %(outdir)s."

#: builders/changes.py:60
#, python-format
msgid "no changes in version %s."
msgstr "ვერსიაში %s ცვლილებები არაა."

#: builders/changes.py:62
msgid "writing summary file..."
msgstr "შეჯამების ფაილის ჩაწერა..."

#: builders/changes.py:77
msgid "Builtins"
msgstr "ჩაშენებულები"

#: builders/changes.py:79
msgid "Module level"
msgstr "მოდულის დონე"

#: builders/changes.py:131
msgid "copying source files..."
msgstr "კოდის ფაილების კოპირება..."

#: builders/changes.py:140
#, python-format
msgid "could not read %r for changelog creation"
msgstr "ცვლილებების ჟურნალის შესაქმნელად %r-ის წაკითხვა შეუძლებელია"

#: util/rst.py:72
#, python-format
msgid "default role %s not found"
msgstr "ნაგულისხმევი როლი %s ვერ ვიპოვე"

#: util/docfields.py:95
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr ""

#: util/osutil.py:130
#, python-format
msgid ""
"Aborted attempted copy from %s to %s (the destination path has existing "
"data)."
msgstr ""

#: util/nodes.py:419
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr ""

#: util/nodes.py:487
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr ""

#: util/nodes.py:701
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr ""

#: util/fileutil.py:76
#, python-format
msgid ""
"Aborted attempted copy from rendered template %s to %s (the destination path"
" has existing data)."
msgstr ""

#: util/fileutil.py:91
#, python-format
msgid "Writing evaluated template result to %s"
msgstr ""

#: util/inventory.py:170
#, python-format
msgid "inventory <%s> contains duplicate definitions of %s"
msgstr ""

#: util/inventory.py:185
#, python-format
msgid "inventory <%s> contains multiple definitions for %s"
msgstr ""

#: util/docutils.py:283
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr "უცნობი დირექტივა ან როლის სახელი: %s:%s"

#: util/docutils.py:746
#, python-format
msgid "unknown node type: %r"
msgstr "უცნობი კვანძის ტიპი: %r"

#: util/display.py:83
msgid "skipped"
msgstr "გამოტოვებული"

#: util/display.py:88
msgid "failed"
msgstr "შეცდომით"

#: util/i18n.py:105
#, python-format
msgid "reading error: %s, %s"
msgstr "წაკითხვის შეცდომა: %s, %s"

#: util/i18n.py:112
#, python-format
msgid "writing error: %s, %s"
msgstr "ჩაწერის შეცდომა: %s, %s"

#: util/i18n.py:141
#, python-format
msgid "locale_dir %s does not exist"
msgstr ""

#: util/i18n.py:236
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr ""

#: directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr ""

#: directives/code.py:63
msgid "non-whitespace stripped by dedent"
msgstr ""

#: directives/code.py:84
#, python-format
msgid "Invalid caption: %s"
msgstr "არასწორი წარწერა: %s"

#: directives/code.py:129 directives/code.py:291 directives/code.py:478
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr ""

#: directives/code.py:211
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "\"%s\" და \"%s\" პარამეტრების ერთდროული გამოყენება შეუძლებელია"

#: directives/code.py:225
#, python-format
msgid "Include file %r not found or reading it failed"
msgstr "ჩასასმელი ფაილი %r ვერ ვიპოვე ან მისი წაკითხვა შეუძლებელია"

#: directives/code.py:228
#, python-format
msgid ""
"Encoding %r used for reading included file %r seems to be wrong, try giving "
"an :encoding: option"
msgstr ""

#: directives/code.py:270
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "ობიექტი სახელად %r ჩასასმელი ფაილში %r აღმოჩენილი არაა"

#: directives/code.py:303
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr ""

#: directives/code.py:308
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr ""

#: directives/other.py:122
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr ""

#: directives/other.py:155 environment/adapters/toctree.py:355
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr ""

#: directives/other.py:158 environment/adapters/toctree.py:359
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr ""

#: directives/other.py:171
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr "აღმოჩენილია დუბლირებული სარჩევის ჩანაწერი: %s"

#: directives/other.py:204
msgid "Section author: "
msgstr "სექციის ავტორი: "

#: directives/other.py:206
msgid "Module author: "
msgstr "მოდულის ავტორი: "

#: directives/other.py:208
msgid "Code author: "
msgstr "კოდის ავტორი: "

#: directives/other.py:210
msgid "Author: "
msgstr "ავტორი: "

#: directives/other.py:284
msgid ".. acks content is not a list"
msgstr ""

#: directives/other.py:309
msgid ".. hlist content is not a list"
msgstr ""

#: _cli/__init__.py:73
msgid "Usage:"
msgstr ""

#: _cli/__init__.py:75
msgid "{0} [OPTIONS] <COMMAND> [<ARGS>]"
msgstr ""

#: _cli/__init__.py:78
msgid "  The Sphinx documentation generator."
msgstr ""

#: _cli/__init__.py:87
msgid "Commands:"
msgstr ""

#: _cli/__init__.py:98
msgid "Options"
msgstr ""

#: _cli/__init__.py:112 _cli/__init__.py:183
msgid "For more information, visit https://www.sphinx-doc.org/en/master/man/."
msgstr ""

#: _cli/__init__.py:172
msgid ""
"{0}: error: {1}\n"
"Run '{0} --help' for information"
msgstr ""

#: _cli/__init__.py:182
msgid "   Manage documentation with Sphinx."
msgstr ""

#: _cli/__init__.py:194
msgid "Show the version and exit."
msgstr ""

#: _cli/__init__.py:202
msgid "Show this message and exit."
msgstr ""

#: _cli/__init__.py:206
msgid "Logging"
msgstr ""

#: _cli/__init__.py:213
msgid "Increase verbosity (can be repeated)"
msgstr ""

#: _cli/__init__.py:221
msgid "Only print errors and warnings."
msgstr ""

#: _cli/__init__.py:228
msgid "No output at all"
msgstr ""

#: _cli/__init__.py:234
msgid "<command>"
msgstr ""

#: _cli/__init__.py:265
msgid "See 'sphinx --help'.\n"
msgstr ""

#: builders/html/__init__.py:478 builders/latex/__init__.py:201
#: transforms/__init__.py:133 writers/manpage.py:101 writers/texinfo.py:218
#, python-format
msgid "%b %d, %Y"
msgstr "%b %d, %Y"

#: transforms/__init__.py:143
msgid "could not calculate translation progress!"
msgstr ""

#: transforms/__init__.py:148
msgid "no translated elements!"
msgstr ""

#: transforms/__init__.py:267
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr ""

#: transforms/__init__.py:313
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr "ნაკვალევი [%s] მიმართული არაა."

#: transforms/__init__.py:322
msgid "Footnote [*] is not referenced."
msgstr ""

#: transforms/__init__.py:333
msgid "Footnote [#] is not referenced."
msgstr "ნაკვალევი [#] მიმართული არაა."

#: transforms/i18n.py:229 transforms/i18n.py:304
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: transforms/i18n.py:274
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr ""

#: transforms/i18n.py:324
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: transforms/i18n.py:346
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: ext/linkcode.py:75 ext/viewcode.py:200
msgid "[source]"
msgstr ""

#: ext/imgconverter.py:40
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr ""

#: ext/imgconverter.py:49 ext/imgconverter.py:73
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: ext/imgconverter.py:68
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr ""

#: ext/viewcode.py:257
msgid "highlighting module code... "
msgstr "მოდულის კოდის გამოკვეთა... "

#: ext/viewcode.py:285
msgid "[docs]"
msgstr ""

#: ext/viewcode.py:305
msgid "Module code"
msgstr "მოდულის კოდი"

#: ext/viewcode.py:311
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr ""

#: ext/viewcode.py:337
msgid "Overview: module code"
msgstr "გადახედვა: მოდულის კოდი"

#: ext/viewcode.py:338
msgid "<h1>All modules for which code is available</h1>"
msgstr ""

#: ext/coverage.py:47
#, python-format
msgid "invalid regex %r in %s"
msgstr "არასწორი რეგულარული გამოსახულება %r %s-ში"

#: ext/coverage.py:134 ext/coverage.py:280
#, python-format
msgid "module %s could not be imported: %s"
msgstr ""

#: ext/coverage.py:141
#, python-format
msgid ""
"the following modules are documented but were not specified in "
"coverage_modules: %s"
msgstr ""

#: ext/coverage.py:149
msgid ""
"the following modules are specified in coverage_modules but were not "
"documented"
msgstr ""

#: ext/coverage.py:163
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr ""

#: ext/coverage.py:177
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr ""

#: ext/coverage.py:245
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr ""

#: ext/coverage.py:429
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr ""

#: ext/coverage.py:445
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr ""

#: ext/coverage.py:458
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr ""

#: ext/todo.py:71
msgid "Todo"
msgstr "განრიგის სია"

#: ext/todo.py:104
#, python-format
msgid "TODO entry found: %s"
msgstr ""

#: ext/todo.py:163
msgid "<<original entry>>"
msgstr "<<საწყისი ჩანაწერი>>"

#: ext/todo.py:165
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr ""

#: ext/todo.py:175
msgid "original entry"
msgstr "საწყისი ჩანაწერი"

#: ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr ""

#: ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr ""

#: ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr "'%s' სწორი პარამეტრი არაა."

#: ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "'%s' სწორი pyversion-ის პარამეტრი არაა"

#: ext/doctest.py:220
msgid "invalid TestCode type"
msgstr "არასწორი TestCode ტიპი"

#: ext/doctest.py:281
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr ""

#: ext/doctest.py:434
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr ""

#: ext/doctest.py:522
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr ""

#: ext/graphviz.py:135
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr ""

#: ext/graphviz.py:145
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "გარე Graphviz ფაილი %r ვერ ვიპოვე ან მისი წაკითხვა შეუძლებელია"

#: ext/graphviz.py:152
msgid "Ignoring \"graphviz\" directive without content."
msgstr "\"graphviz\" დირექტივა, რომელსაც შემცველობა არ აქვს, იგნორირებულია."

#: ext/graphviz.py:268
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr ""

#: ext/graphviz.py:303
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr ""

#: ext/graphviz.py:310
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: ext/graphviz.py:313
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: ext/graphviz.py:329
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr "graphviz_output_format უნდა იყოს ერთ-ერთი სიიდან: 'png', 'svg'. მაგრამ არის %r"

#: ext/graphviz.py:333 ext/graphviz.py:386 ext/graphviz.py:423
#, python-format
msgid "dot code %r: %s"
msgstr ""

#: ext/graphviz.py:436 ext/graphviz.py:444
#, python-format
msgid "[graph: %s]"
msgstr "[გრაფიკი: %s]"

#: ext/graphviz.py:438 ext/graphviz.py:446
msgid "[graph]"
msgstr "[გრაფიკი]"

#: ext/imgmath.py:369 ext/mathjax.py:52
msgid "Link to this equation"
msgstr ""

#: ext/apidoc.py:85
#, python-format
msgid "Would create file %s."
msgstr "შეიქმნებოდა ფაილი %s."

#: ext/apidoc.py:375
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr ""

#: ext/apidoc.py:392
msgid "path to module to document"
msgstr "ბილიკი მოდულიდან დოკუმენტამდე"

#: ext/apidoc.py:396
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr ""

#: ext/apidoc.py:407
msgid "directory to place all output"
msgstr ""

#: ext/apidoc.py:422
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr ""

#: ext/apidoc.py:429
msgid "overwrite existing files"
msgstr "არსებულ ფაილებზე გადაწერა"

#: ext/apidoc.py:437
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr ""

#: ext/apidoc.py:446
msgid "run the script without creating files"
msgstr "სკრიპტის ფაილების შექმნის გარეშე გაშვება"

#: ext/apidoc.py:453
msgid "put documentation for each module on its own page"
msgstr ""

#: ext/apidoc.py:460
msgid "include \"_private\" modules"
msgstr "\"_private\" მოდულების ჩასმა"

#: ext/apidoc.py:467
msgid "filename of table of contents (default: modules)"
msgstr ""

#: ext/apidoc.py:474
msgid "don't create a table of contents file"
msgstr "სარჩევის ფაილი არ შეიქმნება"

#: ext/apidoc.py:481
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr ""

#: ext/apidoc.py:492
msgid "put module documentation before submodule documentation"
msgstr ""

#: ext/apidoc.py:498
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr ""

#: ext/apidoc.py:508
msgid "file suffix (default: rst)"
msgstr "ფაილის სუფიქსი (ნაგულისხმევი: rst)"

#: ext/apidoc.py:515 ext/autosummary/generate.py:838
msgid "Remove existing files in the output directory that were not generated"
msgstr ""

#: ext/apidoc.py:524
msgid "generate a full project with sphinx-quickstart"
msgstr "sphinx-quickstart-ით სრული პროექტის გენარაცია"

#: ext/apidoc.py:531
msgid "append module_path to sys.path, used when --full is given"
msgstr "როცა მითითებულია --full, module_path-ი sys.path-ის ბოლოში მიეწერება"

#: ext/apidoc.py:538
msgid "project name (default: root module name)"
msgstr "პროექტის სახელი (ნაგულისხმევი: ძირითადი მოდულის სახელი)"

#: ext/apidoc.py:545
msgid "project author(s), used when --full is given"
msgstr "პროექტის ავტორ(ებ)-ი. გამოიყენება, როცა მიუთითებთ პარამეტრს --full"

#: ext/apidoc.py:552
msgid "project version, used when --full is given"
msgstr "პროექტის ვერსია. გამოიყენება, როცა მითითებულია --full"

#: ext/apidoc.py:559
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "პროექტის რელიზი. გამოიყენება, როცა მითითებულია --full. ნაგულისხმებ მნიშვნელობაა იგივე, რაც --doc-version"

#: ext/apidoc.py:564
msgid "extension options"
msgstr "გაფართოების პარამეტრები"

#: ext/apidoc.py:638
#, python-format
msgid "%s is not a directory."
msgstr "%s საქაღალდეს არ წარმოადგენს."

#: ext/apidoc.py:710 ext/autosummary/generate.py:874
#, python-format
msgid "Failed to remove %s: %s"
msgstr ""

#: ext/autosectionlabel.py:48
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr "სექცია \"%s\" მიიღებს ჭდეს \"%s\""

#: domains/std/__init__.py:702 domains/std/__init__.py:808
#: ext/autosectionlabel.py:52
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "დუბლირებული ჭდე %s. სხვა აღწერა %s-შია"

#: ext/duration.py:85
msgid ""
"====================== slowest reading durations ======================="
msgstr ""

#: ext/imgmath.py:159
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr ""

#: ext/imgmath.py:174
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr ""

#: ext/imgmath.py:328
#, python-format
msgid "display latex %r: %s"
msgstr ""

#: ext/imgmath.py:362
#, python-format
msgid "inline latex %r: %s"
msgstr ""

#: writers/latex.py:1093 writers/manpage.py:262 writers/texinfo.py:660
msgid "Footnotes"
msgstr "სქოლიოები"

#: writers/manpage.py:308 writers/text.py:935
#, python-format
msgid "[image: %s]"
msgstr "[გამოსახულება: %s]"

#: writers/manpage.py:309 writers/text.py:936
msgid "[image]"
msgstr ""

#: writers/html5.py:99 writers/html5.py:108
msgid "Link to this definition"
msgstr ""

#: writers/html5.py:415
#, python-format
msgid "numfig_format is not defined for %s"
msgstr "numfig_format-ი %s-სთვის აღწერილი არაა"

#: writers/html5.py:427
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr "ნებისმიერი ID, რომელიც %s კვანძზე მინიჭებული არაა"

#: writers/html5.py:482
msgid "Link to this term"
msgstr ""

#: writers/html5.py:525 writers/html5.py:530
msgid "Link to this heading"
msgstr ""

#: writers/html5.py:535
msgid "Link to this table"
msgstr ""

#: writers/html5.py:549 writers/latex.py:1102
#, python-format
msgid "unsupported rubric heading level: %s"
msgstr ""

#: writers/html5.py:613
msgid "Link to this code"
msgstr ""

#: writers/html5.py:615
msgid "Link to this image"
msgstr ""

#: writers/html5.py:617
msgid "Link to this toctree"
msgstr ""

#: writers/html5.py:759
msgid "Could not obtain image size. :scale: option is ignored."
msgstr "გამოსახულების ზომის მიღება შეუძლებელია. :scale: მოხდება პარამეტრის გამოტოვება."

#: builders/latex/__init__.py:208 domains/std/__init__.py:645
#: domains/std/__init__.py:657 templates/latex/latex.tex.jinja:106
#: themes/basic/genindex-single.html:22 themes/basic/genindex-single.html:48
#: themes/basic/genindex-split.html:3 themes/basic/genindex-split.html:6
#: themes/basic/genindex.html:3 themes/basic/genindex.html:26
#: themes/basic/genindex.html:59 themes/basic/layout.html:127
#: writers/texinfo.py:511
msgid "Index"
msgstr "ინდექსი"

#: writers/latex.py:746 writers/texinfo.py:642
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr ""

#: writers/texinfo.py:1214
msgid "caption not inside a figure."
msgstr "წარწერა ფიგურის შიგნით არაა."

#: writers/texinfo.py:1300
#, python-format
msgid "unimplemented node type: %r"
msgstr "განუხორციელებელი კვანძის ტიპი: %r"

#: writers/latex.py:364
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr ""

#: builders/latex/__init__.py:226 writers/latex.py:414
#, python-format
msgid "no Babel option known for language %r"
msgstr "ენისთვის %r Babel-ის პარამეტრი ცნობილი არაა"

#: writers/latex.py:432
msgid "too large :maxdepth:, ignored."
msgstr "ძალიან დიდი :maxdepth:. გამოტოვება."

#: writers/latex.py:593
#, python-format
msgid "template %s not found; loading from legacy %s instead"
msgstr ""

#: writers/latex.py:711
msgid "document title is not a single Text node"
msgstr "დოკუმენტის სათაური ერთი ტექსტური კვანძი არაა"

#: writers/latex.py:1178
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr ""

#: writers/latex.py:1575
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "ზომის ერთეული %s არასწორია. გამოტოვება."

#: writers/latex.py:1931
#, python-format
msgid "unknown index entry type %s found"
msgstr "აღმოჩენილია ინდექსის ჩანაწერის უცნობი ტიპი %s"

#: domains/std/__init__.py:86 domains/std/__init__.py:103
#, python-format
msgid "environment variable; %s"
msgstr "გარემოს ცვლადი; %s"

#: domains/std/__init__.py:111
#, python-format
msgid "%s; configuration value"
msgstr ""

#: domains/std/__init__.py:165
msgid "Type"
msgstr ""

#: domains/std/__init__.py:175
msgid "Default"
msgstr ""

#: domains/std/__init__.py:234
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr ""

#: domains/std/__init__.py:305
#, python-format
msgid "%s command line option"
msgstr "%s ბრძანების სტრიქონის პარამეტრი"

#: domains/std/__init__.py:307
msgid "command line option"
msgstr "ბრძანების სტრიქონის ვარიანტი"

#: domains/std/__init__.py:429
msgid "glossary term must be preceded by empty line"
msgstr "სარჩევის ელემენტებს წინ ცარიელი ხაზი უნდა იყოს"

#: domains/std/__init__.py:437
msgid "glossary terms must not be separated by empty lines"
msgstr "სარჩევის ელემენტები ცარიელი ხაზებით უნდა გამოყოთ"

#: domains/std/__init__.py:443 domains/std/__init__.py:456
msgid "glossary seems to be misformatted, check indentation"
msgstr "სარჩევის ფორმატი არასწორია. გადაამოწმეთ შეწევა"

#: domains/std/__init__.py:601
msgid "glossary term"
msgstr "სარჩევის ელემენტი"

#: domains/std/__init__.py:602
msgid "grammar token"
msgstr "გრამატიკის კოდი"

#: domains/std/__init__.py:603
msgid "reference label"
msgstr "მიმართვის ჭდე"

#: domains/std/__init__.py:606
msgid "environment variable"
msgstr "გარემოს ცვლადი"

#: domains/std/__init__.py:607
msgid "program option"
msgstr "პროგრამის პარამეტრი"

#: domains/std/__init__.py:608
msgid "document"
msgstr "დოკუმენტი"

#: domains/std/__init__.py:646 domains/std/__init__.py:658
msgid "Module Index"
msgstr "მოდულის ინდექსი"

#: domains/std/__init__.py:647 domains/std/__init__.py:659
#: themes/basic/defindex.html:18
msgid "Search Page"
msgstr "ძებნის გვერდი"

#: domains/std/__init__.py:721
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr "დუბლირებული %s აღწერისთვის %s. სხვა ასლი %s-შია"

#: domains/std/__init__.py:926
msgid "numfig is disabled. :numref: is ignored."
msgstr "numfig გამორთულია. :numref: გამოტოვებული იქნება."

#: domains/std/__init__.py:934
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr ""

#: domains/std/__init__.py:946
#, python-format
msgid "the link has no caption: %s"
msgstr "ბმულს წარწერა არ გააჩნია: %s"

#: domains/std/__init__.py:960
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "არასწორი numfig_format: %s (%r)"

#: domains/std/__init__.py:963
#, python-format
msgid "invalid numfig_format: %s"
msgstr "არასწორი numfig_format: %s"

#: domains/std/__init__.py:1194
#, python-format
msgid "undefined label: %r"
msgstr "აღუწერელი ჭდე: %r"

#: domains/std/__init__.py:1196
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr ""

#: domains/python/__init__.py:107 domains/python/__init__.py:244
#, python-format
msgid "%s() (in module %s)"
msgstr "%s () (მოდულში %s)"

#: domains/python/__init__.py:167 domains/python/__init__.py:334
#: domains/python/__init__.py:385 domains/python/__init__.py:424
#, python-format
msgid "%s (in module %s)"
msgstr "%s (მოდულში %s)"

#: domains/python/__init__.py:169
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (ჩაშენებული ცვლადი)"

#: domains/python/__init__.py:194
#, python-format
msgid "%s (built-in class)"
msgstr "%s (ჩაშენებული კლასი)"

#: domains/python/__init__.py:195
#, python-format
msgid "%s (class in %s)"
msgstr "%s (კლასი %s-ში)"

#: domains/python/__init__.py:249
#, python-format
msgid "%s() (%s class method)"
msgstr "%s () (%s კლასის მეთოდი)"

#: domains/python/__init__.py:251
#, python-format
msgid "%s() (%s static method)"
msgstr "%s () (%s სტატიკური მეთოდი)"

#: domains/python/__init__.py:389
#, python-format
msgid "%s (%s property)"
msgstr "%s (%s თვისება)"

#: domains/python/__init__.py:428
#, python-format
msgid "%s (type alias in %s)"
msgstr ""

#: domains/python/__init__.py:557
msgid "Python Module Index"
msgstr "Python-ის მოდულის ინდექსი"

#: domains/python/__init__.py:558
msgid "modules"
msgstr "მოდულები"

#: domains/python/__init__.py:607
msgid "Deprecated"
msgstr "მოძველებულია"

#: domains/python/__init__.py:632
msgid "exception"
msgstr "გამონაკლისი"

#: domains/python/__init__.py:634
msgid "class method"
msgstr "კლასის მეთოდი"

#: domains/python/__init__.py:635
msgid "static method"
msgstr "სტატიკური მეთოდი"

#: domains/python/__init__.py:637
msgid "property"
msgstr "თვისება"

#: domains/python/__init__.py:638
msgid "type alias"
msgstr ""

#: domains/python/__init__.py:698
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr ""

#: domains/python/__init__.py:817
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr ""

#: domains/python/__init__.py:878
msgid " (deprecated)"
msgstr " (მოძველებული)"

#: domains/c/__init__.py:298 domains/cpp/__init__.py:436
#: domains/python/_object.py:164 ext/napoleon/docstring.py:786
msgid "Parameters"
msgstr "პარამეტრები"

#: domains/python/_object.py:169
msgid "Variables"
msgstr "ცვლადები"

#: domains/python/_object.py:173
msgid "Raises"
msgstr "გამონაკლისები"

#: domains/c/__init__.py:199
#, python-format
msgid "%s (C %s)"
msgstr "%s (C %s)"

#: domains/c/__init__.py:260 domains/c/_symbol.py:510
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr ""

#: domains/c/__init__.py:301 domains/cpp/__init__.py:449
msgid "Return values"
msgstr "დაბრუნებული მნიშვნელობები"

#: domains/c/__init__.py:673 domains/cpp/__init__.py:855
msgid "member"
msgstr "წვერი"

#: domains/c/__init__.py:674
msgid "variable"
msgstr "ცვლადი"

#: domains/c/__init__.py:676
msgid "macro"
msgstr "მაკრო"

#: domains/c/__init__.py:677
msgid "struct"
msgstr "სტრუქტურა"

#: domains/c/__init__.py:678 domains/cpp/__init__.py:853
msgid "union"
msgstr "გაერთიანება"

#: domains/c/__init__.py:679 domains/cpp/__init__.py:858
msgid "enum"
msgstr "ჩამონათვალი"

#: domains/c/__init__.py:680 domains/cpp/__init__.py:859
msgid "enumerator"
msgstr "დამთვლელი"

#: domains/c/__init__.py:681 domains/cpp/__init__.py:856
msgid "type"
msgstr "ტიპი"

#: domains/c/__init__.py:683 domains/cpp/__init__.py:861
msgid "function parameter"
msgstr "ფუნქციის პარამეტრი"

#: domains/cpp/__init__.py:155
msgid "Template Parameters"
msgstr "შაბლონის პარამეტრები"

#: domains/cpp/__init__.py:277
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: domains/cpp/__init__.py:360 domains/cpp/_symbol.py:793
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr ""

#: domains/cpp/__init__.py:857
msgid "concept"
msgstr "კონცეფცია"

#: domains/cpp/__init__.py:862
msgid "template parameter"
msgstr "შაბლონის პარამეტრი"

#: themes/haiku/layout.html:16
msgid "Contents"
msgstr "შიგთავსი"

#: themes/agogo/layout.html:29 themes/basic/globaltoc.html:2
#: themes/basic/localtoc.html:4 themes/scrolls/layout.html:32
msgid "Table of Contents"
msgstr "სარჩევი"

#: themes/agogo/layout.html:34 themes/basic/layout.html:130
#: themes/basic/search.html:3 themes/basic/search.html:15
msgid "Search"
msgstr "ძებნა"

#: themes/agogo/layout.html:37 themes/basic/searchbox.html:8
#: themes/basic/searchfield.html:12
msgid "Go"
msgstr "გადასვლა"

#: themes/agogo/layout.html:81 themes/basic/sourcelink.html:7
msgid "Show Source"
msgstr "წყაროს ჩვენება"

#: themes/classic/layout.html:12 themes/classic/static/sidebar.js.jinja:51
msgid "Collapse sidebar"
msgstr "გვერდითი ზოლის ჩაკეცვა"

#: themes/basic/layout.html:18
msgid "Navigation"
msgstr "ნავიგაცია"

#: themes/basic/layout.html:115
#, python-format
msgid "Search within %(docstitle)s"
msgstr "%(docstitle)s-ში ძებნა"

#: themes/basic/layout.html:124
msgid "About these documents"
msgstr "ამ დოკუმენტების შესახებ"

#: themes/basic/layout.html:133 themes/basic/layout.html:177
#: themes/basic/layout.html:179
msgid "Copyright"
msgstr "საავტორო უფლებები"

#: themes/basic/layout.html:183 themes/basic/layout.html:189
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr ""

#: themes/basic/layout.html:201
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "ბოლო განახლების დრო %(last_updated)s."

#: themes/basic/layout.html:204
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr ""

#: themes/basic/genindex-single.html:26
#, python-format
msgid "Index &#x2013; %(key)s"
msgstr ""

#: themes/basic/genindex-single.html:54 themes/basic/genindex-split.html:16
#: themes/basic/genindex-split.html:30 themes/basic/genindex.html:65
msgid "Full index on one page"
msgstr "სრული ინდექსი ერთ გვერდზე"

#: themes/basic/genindex-split.html:8
msgid "Index pages by letter"
msgstr "ინდექსის გვერდები ასოების მიხედვით"

#: themes/basic/genindex-split.html:17
msgid "can be huge"
msgstr "შეიძლება უზარმაზარი იყოს"

#: themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "%(docstitle)s-შ ძებნა"

#: themes/basic/sourcelink.html:4
msgid "This Page"
msgstr "ეს გვერდი"

#: themes/basic/defindex.html:4
msgid "Overview"
msgstr "მიმოხილვა"

#: themes/basic/defindex.html:8
msgid "Welcome! This is"
msgstr "კეთილი იყოს თქვენი მობრძანება"

#: themes/basic/defindex.html:9
msgid "the documentation for"
msgstr ""

#: themes/basic/defindex.html:10
msgid "last updated"
msgstr ""

#: themes/basic/defindex.html:13
msgid "Indices and tables:"
msgstr "ინდექსები და ცხრილები:"

#: themes/basic/defindex.html:16
msgid "Complete Table of Contents"
msgstr "სრული სარჩევი"

#: themes/basic/defindex.html:17
msgid "lists all sections and subsections"
msgstr "სექციებისა და ქვესექციების ჩამონათვალი"

#: themes/basic/defindex.html:19
msgid "search this documentation"
msgstr "ამ დოკუმენტაციაში ძებნა"

#: themes/basic/defindex.html:21
msgid "Global Module Index"
msgstr "გლობალური მოდულების ინდექსი"

#: themes/basic/defindex.html:22
msgid "quick access to all modules"
msgstr "სწრაფი წვდომა ყველა მოდულთან"

#: builders/html/__init__.py:499 themes/basic/defindex.html:23
msgid "General Index"
msgstr "ზოგადი ინდექსი"

#: themes/basic/defindex.html:24
msgid "all functions, classes, terms"
msgstr "ყველა ფუნქცია, კლასი, წესი"

#: themes/basic/searchbox.html:4
msgid "Quick search"
msgstr "სწრაფი ძებნა"

#: themes/basic/search.html:20
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr ""

#: themes/basic/search.html:28
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr ""

#: themes/basic/search.html:35
msgid "search"
msgstr "ძებნა"

#: themes/basic/relations.html:4
msgid "Previous topic"
msgstr "წინა თემა"

#: themes/basic/relations.html:6
msgid "previous chapter"
msgstr "წინა თავი"

#: themes/basic/relations.html:11
msgid "Next topic"
msgstr "შემდეგი თემა"

#: themes/basic/relations.html:13
msgid "next chapter"
msgstr "შემდეგი თავი"

#: themes/classic/static/sidebar.js.jinja:42
msgid "Expand sidebar"
msgstr "გვერდითი ზოლის გაფართოება"

#: themes/basic/changes/frameset.html:5
#: themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr ""

#: themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr ""

#: themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "ბიბლიოთეკის ცვლილებები"

#: themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "C API ცვლილებები"

#: themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "სხვა ცვლილებები"

#: themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr ""

#: themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "ძებნის დამთხვევების დამალვა"

#: themes/basic/static/searchtools.js:117
msgid "Search Results"
msgstr "ძებნს შედეგები"

#: themes/basic/static/searchtools.js:119
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr ""

#: themes/basic/static/searchtools.js:123
msgid "Search finished, found one page matching the search query."
msgid_plural ""
"Search finished, found ${resultCount} pages matching the search query."
msgstr[0] ""
msgstr[1] ""

#: themes/basic/static/searchtools.js:253
msgid "Searching"
msgstr "ძებნა"

#: themes/basic/static/searchtools.js:270
msgid "Preparing search..."
msgstr "ძებნის მომზადება..."

#: themes/basic/static/searchtools.js:474
msgid ", in "
msgstr ""

#: environment/collectors/asset.py:95
#, python-format
msgid "image file not readable: %s"
msgstr "გამოსახულების ფაილი წაკითხვადი არაა: %s"

#: environment/collectors/asset.py:123
#, python-format
msgid "image file %s not readable: %s"
msgstr "გამოსახულების ფაილი %s წაკითხვადი არაა: %s"

#: environment/collectors/asset.py:160
#, python-format
msgid "download file not readable: %s"
msgstr "გადმოწერილი ფაილი წაკითხვადი არაა: %s"

#: environment/collectors/toctree.py:258
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr ""

#: environment/adapters/toctree.py:318
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "აღმოჩენილია სარჩევის ხის წრიული მიმართვები. გამოტოვებული იქნება: %s <- %s"

#: environment/adapters/toctree.py:342
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "სარჩევის ხე შეიცავს მიმართვას დოკუმენტამდე %r, რომელსაც სათაური არ გააჩნია. ბმული არ შეიქმნება"

#: environment/adapters/toctree.py:357
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr "სარჩევის ხე არ-ჩართულ დოკუმენტამდე, %r, მიმართვას შეიცავს"

#: environment/adapters/indexentries.py:126
#, python-format
msgid "see %s"
msgstr "იხილეთ %s"

#: environment/adapters/indexentries.py:136
#, python-format
msgid "see also %s"
msgstr "აგრეთვე იხილეთ %s"

#: environment/adapters/indexentries.py:144
#, python-format
msgid "unknown index entry type %r"
msgstr "უცნობი ინდექსის ჩანაწერის ტიპი %r"

#: environment/adapters/indexentries.py:273
#: templates/latex/sphinxmessages.sty.jinja:11
msgid "Symbols"
msgstr "სიმბოლოები"

#: builders/html/_build_info.py:32
msgid "failed to read broken build info file (unknown version)"
msgstr ""

#: builders/html/_build_info.py:36
msgid "failed to read broken build info file (missing config entry)"
msgstr ""

#: builders/html/_build_info.py:39
msgid "failed to read broken build info file (missing tags entry)"
msgstr ""

#: builders/html/__init__.py:113
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "HTML გვერდების საქაღალდეა %(outdir)s."

#: builders/html/__init__.py:340
#, python-format
msgid "Failed to read build info file: %r"
msgstr "აგების ინფორმაციის ფაილის წაკითხვის შეცდომა: %r"

#: builders/html/__init__.py:355
msgid "build_info mismatch, copying .buildinfo to .buildinfo.bak"
msgstr ""

#: builders/html/__init__.py:358
msgid "building [html]: "
msgstr ""

#: builders/html/__init__.py:374
#, python-format
msgid ""
"template %s has been changed since the previous build, all docs will be "
"rebuilt"
msgstr ""

#: builders/html/__init__.py:499
msgid "index"
msgstr "ინდექსი"

#: builders/html/__init__.py:547
#, python-format
msgid "Logo of %s"
msgstr ""

#: builders/html/__init__.py:572
msgid "next"
msgstr "შემდეგი"

#: builders/html/__init__.py:581
msgid "previous"
msgstr "წინა"

#: builders/html/__init__.py:678
msgid "generating indices"
msgstr "ინდექსების გენერაცია"

#: builders/html/__init__.py:693
msgid "writing additional pages"
msgstr "დამატებითი გვერდების ჩაწერა"

#: builders/html/__init__.py:772
#, python-format
msgid "cannot copy image file '%s': %s"
msgstr ""

#: builders/html/__init__.py:784
msgid "copying downloadable files... "
msgstr "გადმოწერადი ფაილების კოპირება... "

#: builders/html/__init__.py:796
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "გადმოწერადი ფაილის %r კოპირების შეცდომა:%s"

#: builders/html/__init__.py:843
#, python-format
msgid "Failed to copy a file in the theme's 'static' directory: %s: %r"
msgstr ""

#: builders/html/__init__.py:861
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr "ფაილის html_static_file-ში კოპირებს შეცდომა: %s: %r"

#: builders/html/__init__.py:896
msgid "copying static files"
msgstr "სტატიკური ფაილების კოპირება"

#: builders/html/__init__.py:912
#, python-format
msgid "cannot copy static file %r"
msgstr "სტატიკური ფაილის %r კოპირების შეცდომა"

#: builders/html/__init__.py:917
msgid "copying extra files"
msgstr "დამატებითი ფაილების კოპირება"

#: builders/html/__init__.py:927
#, python-format
msgid "cannot copy extra file %r"
msgstr "დამატებითი ფაილის %r კოპირების შეცდომა"

#: builders/html/__init__.py:933
#, python-format
msgid "Failed to write build info file: %r"
msgstr "აგების ინფორმაციის ფაილის ჩაწერის შეცდომა: %r"

#: builders/html/__init__.py:982
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr "ძებნის ინდექსის ჩატვირთვა შეუძლებელია, მაგრამ ყველა დოკუმენტის აგება არ მოხდება: ინდექსი დაუსრულებელი იქნება."

#: builders/html/__init__.py:1027
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr ""

#: builders/html/__init__.py:1188
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr ""

#: builders/html/__init__.py:1197
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "შეცდომა %s გვერდის რენდერისას.\nმიზეზი: %r"

#: builders/html/__init__.py:1229
msgid "dumping object inventory"
msgstr "ობიექტის ინვენტარის დამპი"

#: builders/html/__init__.py:1237
#, python-format
msgid "dumping search index in %s"
msgstr "%s-ში არსებული ძებნის ინდექსის დამპი"

#: builders/html/__init__.py:1279
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "არასწორი js_file: %r, გამოტოვებულია"

#: builders/html/__init__.py:1312
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr ""

#: builders/html/__init__.py:1317
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "მითითებული math_renderer %r უცნობია."

#: builders/html/__init__.py:1325
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "html_extra_path ჩანაწერი %r არ არსებობს"

#: builders/html/__init__.py:1332
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr "html_extra_path ჩანაწერი %r გამოტანის საქაღალდეშია"

#: builders/html/__init__.py:1342
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "html_static_path ჩანაწერი %r არ არსებობს"

#: builders/html/__init__.py:1349
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr "html_static_path ჩანაწერი %r გამოტანის საქაღალდეშია"

#: builders/html/__init__.py:1361 builders/latex/__init__.py:507
#, python-format
msgid "logo file %r does not exist"
msgstr "ლოგოს ფაილი %r არ არსებობს"

#: builders/html/__init__.py:1372
#, python-format
msgid "favicon file %r does not exist"
msgstr "favicon ფაილი %r არ არსებობს"

#: builders/html/__init__.py:1384
#, python-format
msgid ""
"Values in 'html_sidebars' must be a list of strings. At least one pattern "
"has a string value: %s. Change to `html_sidebars = %r`."
msgstr ""

#: builders/html/__init__.py:1397
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr ""

#: builders/html/__init__.py:1414
#, python-format
msgid "%s %s documentation"
msgstr "%s %s დოკუმენტაცია"

#: builders/latex/transforms.py:118
msgid "Failed to get a docname!"
msgstr ""

#: builders/latex/transforms.py:119
#, python-format
msgid "Failed to get a docname for source %r!"
msgstr ""

#: builders/latex/transforms.py:485
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr ""

#: builders/latex/__init__.py:117
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "LaTeX-ის ფაილების საქაღალდეა %(outdir)s."

#: builders/latex/__init__.py:119
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr ""

#: builders/latex/__init__.py:157
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr ""

#: builders/latex/__init__.py:169
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr ""

#: builders/latex/__init__.py:211 templates/latex/latex.tex.jinja:91
msgid "Release"
msgstr "გამოცემა"

#: builders/latex/__init__.py:432
msgid "copying TeX support files"
msgstr "მიმდინარეობს TeX-ის მხარდაჭერის ფაილების კოპირება"

#: builders/latex/__init__.py:469
msgid "copying additional files"
msgstr "დამატებითი ფაილების კოპირება"

#: builders/latex/__init__.py:543
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr "უცნობი კონფიგურაციის პარამეტრი: latex_elements[%r]. გამოტოვება."

#: builders/latex/__init__.py:551
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr "უცნობი თემის პარამეტრი: latex_theme_options[%r]. გამოტოვება."

#: builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr "%r-ს პარამეტრი \"theme\" არ აქვს"

#: builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr "%r-ს \"%s\" პარამეტრი არ აქვს"

#: _cli/util/errors.py:124
msgid "Exception occurred, starting debugger:"
msgstr ""

#: _cli/util/errors.py:133
msgid "reStructuredText markup error:"
msgstr ""

#: _cli/util/errors.py:168
msgid "The full traceback has been saved in:"
msgstr ""

#: _cli/util/errors.py:172
msgid ""
"To report this error to the developers, please open an issue at "
"<https://github.com/sphinx-doc/sphinx/issues/>. Thanks!"
msgstr ""

#: transforms/post_transforms/__init__.py:124
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr ""

#: transforms/post_transforms/__init__.py:184
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr ""

#: transforms/post_transforms/__init__.py:250
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr "%s:%s მიმართვის სამიზნე ვერ ვიპოვე: %s"

#: transforms/post_transforms/__init__.py:256
#, python-format
msgid "%r reference target not found: %s"
msgstr "%r მიმართვის სამიზნე ვერ ვიპოვე: %s"

#: transforms/post_transforms/images.py:77
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr "დაშორებული გამოსახულების მიღების შეცდომა: %s [%s]"

#: transforms/post_transforms/images.py:94
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr "დაშორებული გამოსახულების მიღების შეცდომა: %s [%d]"

#: transforms/post_transforms/images.py:141
#, python-format
msgid "Unknown image format: %s..."
msgstr "უცნობი გამოსახულების ფორმატი: %s..."

#: ext/napoleon/docstring.py:707
msgid "Example"
msgstr "მაგალითი"

#: ext/napoleon/docstring.py:708
msgid "Examples"
msgstr "მაგალითები"

#: ext/napoleon/__init__.py:344 ext/napoleon/docstring.py:752
msgid "Keyword Arguments"
msgstr "საკვანძო სიტყვების არგუმენტები"

#: ext/napoleon/docstring.py:768
msgid "Notes"
msgstr "ჩანაწერები"

#: ext/napoleon/docstring.py:777
msgid "Other Parameters"
msgstr "სხვა პარამეტრები"

#: ext/napoleon/docstring.py:813
msgid "Receives"
msgstr "იღებს"

#: ext/napoleon/docstring.py:817
msgid "References"
msgstr "ბმები"

#: ext/napoleon/docstring.py:849
msgid "Warns"
msgstr "გაფრთხილებები"

#: ext/napoleon/docstring.py:853
msgid "Yields"
msgstr ""

#: ext/napoleon/docstring.py:1015
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr "არასწორი მნიშვნელობების ნაკრები (აკლია დამხურავი ფრჩხილი): %s"

#: ext/napoleon/docstring.py:1022
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr "არასწორი მნიშვნელობების ნაკრები (აკლია გამხსნელი ფრჩხილი): %s"

#: ext/napoleon/docstring.py:1029
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr ""

#: ext/napoleon/docstring.py:1036
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr ""

#: ext/autosummary/__init__.py:255
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr ""

#: ext/autosummary/__init__.py:257
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr ""

#: ext/autosummary/__init__.py:276
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr ""

#: ext/autosummary/__init__.py:329
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: ext/autosummary/__init__.py:343
#, python-format
msgid "failed to parse name %s"
msgstr "სახელის %s დამუშავების შეცომა"

#: ext/autosummary/__init__.py:348
#, python-format
msgid "failed to import object %s"
msgstr "ობიექტის %s შემოტანის შეცდომა"

#: ext/autosummary/__init__.py:647
#, python-format
msgid ""
"Summarised items should not include the current module. Replace %r with %r."
msgstr ""

#: ext/autosummary/__init__.py:818
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr "autosummary_generate: ფაილი ვერ ვიპოვე: %s"

#: ext/autosummary/__init__.py:826
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr ""

#: ext/autosummary/generate.py:214 ext/autosummary/generate.py:390
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: ext/autosummary/generate.py:525
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr ""

#: ext/autosummary/generate.py:529
#, python-format
msgid "[autosummary] writing to %s"
msgstr "[autosummary] %s-ში ჩაწერა"

#: ext/autosummary/generate.py:571
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: ext/autosummary/generate.py:766
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr ""

#: ext/autosummary/generate.py:788
msgid "source files to generate rST files for"
msgstr "rST ფაილების დასაგენერირებელიკოდის ფაილები"

#: ext/autosummary/generate.py:796
msgid "directory to place all output in"
msgstr "გამოტანის ჩასაწერი საქაღალდე"

#: ext/autosummary/generate.py:804
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "ფაილის ნაგულიხმევი სუფიქსი (ნაგულისხმევი: %(default)s)"

#: ext/autosummary/generate.py:812
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr "მომხმარებლის ნიმუშების საქაღალდე (ნაგულისხმევი: %(default)s)"

#: ext/autosummary/generate.py:820
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr ""

#: ext/autosummary/generate.py:828
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr ""

#: ext/intersphinx/_resolve.py:47
#, python-format
msgid "(in %s v%s)"
msgstr "(%s v%s-ში)"

#: ext/intersphinx/_resolve.py:49
#, python-format
msgid "(in %s)"
msgstr ""

#: ext/intersphinx/_resolve.py:103
#, python-format
msgid "inventory '%s': duplicate matches found for %s:%s"
msgstr ""

#: ext/intersphinx/_resolve.py:113
#, python-format
msgid "inventory '%s': multiple matches found for %s:%s"
msgstr ""

#: ext/intersphinx/_resolve.py:359
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:367
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:378
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:585
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr ""

#: ext/intersphinx/_load.py:59
#, python-format
msgid ""
"Invalid intersphinx project identifier `%r` in intersphinx_mapping. Project "
"identifiers must be non-empty strings."
msgstr ""

#: ext/intersphinx/_load.py:70
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Expected a two-element tuple "
"or list."
msgstr ""

#: ext/intersphinx/_load.py:81
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Values must be a (target URI,"
" inventory locations) pair."
msgstr ""

#: ext/intersphinx/_load.py:92
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique non-empty strings."
msgstr ""

#: ext/intersphinx/_load.py:101
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique (other instance in intersphinx_mapping[%r])."
msgstr ""

#: ext/intersphinx/_load.py:120
#, python-format
msgid ""
"Invalid inventory location value `%r` in intersphinx_mapping[%r][1]. "
"Inventory locations must be non-empty strings or None."
msgstr ""

#: ext/intersphinx/_load.py:131
msgid "Invalid `intersphinx_mapping` configuration (1 error)."
msgstr ""

#: ext/intersphinx/_load.py:134
#, python-format
msgid "Invalid `intersphinx_mapping` configuration (%s errors)."
msgstr ""

#: ext/intersphinx/_load.py:155
msgid "An invalid intersphinx_mapping entry was added after normalisation."
msgstr ""

#: ext/intersphinx/_load.py:240
#, python-format
msgid "loading intersphinx inventory '%s' from %s ..."
msgstr ""

#: ext/intersphinx/_load.py:265
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr ""

#: ext/intersphinx/_load.py:275
msgid "failed to reach any of the inventories with the following issues:"
msgstr ""

#: ext/intersphinx/_load.py:319
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr ""

#: ext/autodoc/type_comment.py:132
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr "%r-სთვის ხელმოწერის განახლების შეცდომა: პარამეტრი ვერ ვიპოვე: %s"

#: ext/autodoc/type_comment.py:135
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr ""

#: ext/autodoc/__init__.py:141
#, python-format
msgid "invalid value for member-order option: %s"
msgstr ""

#: ext/autodoc/__init__.py:149
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr ""

#: ext/autodoc/__init__.py:408
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr ""

#: ext/autodoc/__init__.py:525
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:795
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: ext/autodoc/__init__.py:890
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr ""

#: ext/autodoc/__init__.py:934
#, python-format
msgid "A mocked object is detected: %r"
msgstr ""

#: ext/autodoc/__init__.py:953
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:1016
msgid "\"::\" in automodule name doesn't make sense"
msgstr ""

#: ext/autodoc/__init__.py:1023
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr ""

#: ext/autodoc/__init__.py:1036
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr ""

#: ext/autodoc/__init__.py:1102
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr ""

#: ext/autodoc/__init__.py:1325 ext/autodoc/__init__.py:1402
#: ext/autodoc/__init__.py:2810
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:1616
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:1743
#, python-format
msgid "Bases: %s"
msgstr "ძირითადი კლასები: %s"

#: ext/autodoc/__init__.py:1757
#, python-format
msgid "missing attribute %s in object %s"
msgstr "ატრიბუტი %s ობიექტში %s აღმოჩენილი არაა"

#: ext/autodoc/__init__.py:1838 ext/autodoc/__init__.py:1875
#: ext/autodoc/__init__.py:1970
#, python-format
msgid "alias of %s"
msgstr ""

#: ext/autodoc/__init__.py:1858
#, python-format
msgid "alias of TypeVar(%s)"
msgstr "მეტსახელი TypeVar(%s)-სთვის"

#: ext/autodoc/__init__.py:2198 ext/autodoc/__init__.py:2298
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr "%s-სთვის მეთოდის ხელმოწერის მიღება შეუძლებელია: %s"

#: ext/autodoc/__init__.py:2429
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr "%s-ზე აღმოჩენილია არასწორი __slots__: გამოტოვებულია."

#: ext/autodoc/preserve_defaults.py:190
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr "%r-სთვის ნაგულისხმევი არგუმენტის მნიშვნელობის დამუშავების შეცდომა: %s"

#: templates/latex/longtable.tex.jinja:52
#: templates/latex/sphinxmessages.sty.jinja:8
msgid "continued from previous page"
msgstr "წინა გვერდიდან"

#: templates/latex/longtable.tex.jinja:63
#: templates/latex/sphinxmessages.sty.jinja:9
msgid "continues on next page"
msgstr "გრძელდება შემდეგ გვერდზე"

#: templates/latex/sphinxmessages.sty.jinja:10
msgid "Non-alphabetical"
msgstr "არაანბანური"

#: templates/latex/sphinxmessages.sty.jinja:12
msgid "Numbers"
msgstr "რიცხვები"

#: templates/latex/sphinxmessages.sty.jinja:13
msgid "page"
msgstr "პანელი"
