#!/usr/bin/env python3
"""
完整转换测试脚本

测试从LaTeX到Word的完整转换流程。
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_full_conversion():
    """测试完整的转换流程"""
    print("LaTeX到Word完整转换测试")
    print("="*50)
    
    try:
        # 导入必要的模块
        from core.converter import LatexToWordConverter
        from utils.config_manager import ConfigManager
        from utils.logger import setup_logger
        
        # 设置日志
        setup_logger({'level': 'INFO', 'console_output': True})
        
        # 创建转换器
        print("1. 初始化转换器...")
        converter = LatexToWordConverter()
        
        # 验证输入文件
        input_file = "examples/input/sample.tex"
        print(f"2. 验证输入文件: {input_file}")
        
        validation_result = converter.validate_input(input_file)
        if not validation_result['valid']:
            print("❌ 输入文件验证失败:")
            for error in validation_result['errors']:
                print(f"   - {error}")
            return
        
        print("✅ 输入文件验证通过")
        if validation_result['warnings']:
            print("⚠️ 警告:")
            for warning in validation_result['warnings']:
                print(f"   - {warning}")
        
        # 预览转换
        print("\n3. 生成转换预览...")
        preview = converter.preview_conversion(input_file)
        
        if 'error' in preview:
            print(f"❌ 预览生成失败: {preview['error']}")
            return
        
        print("✅ 转换预览:")
        doc_info = preview['document_info']
        print(f"   标题: {doc_info.get('title', '无')}")
        print(f"   作者: {doc_info.get('author', '无')}")
        print(f"   章节数: {doc_info.get('sections', 0)}")
        print(f"   公式数: {doc_info.get('formulas', 0)}")
        print(f"   表格数: {doc_info.get('tables', 0)}")
        print(f"   图形数: {doc_info.get('figures', 0)}")
        print(f"   估计处理时间: {preview.get('estimated_processing_time', 0):.2f} 秒")
        
        # 显示复杂度分布
        complexity_dist = preview.get('complexity_distribution', {})
        print(f"   公式复杂度分布:")
        for complexity, count in complexity_dist.items():
            print(f"     {complexity}: {count}")
        
        # 执行转换
        print("\n4. 执行转换...")
        output_file = "examples/output/converted_document.docx"
        
        # 确保输出目录存在
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        
        result = converter.convert(input_file, output_file)
        
        if result['success']:
            print("✅ 转换成功!")
            print(f"   输出文件: {result['output_file']}")
            
            doc_info = result['document_info']
            print(f"   文档信息:")
            print(f"     标题: {doc_info.get('title', '无')}")
            print(f"     章节数: {doc_info.get('sections', 0)}")
            print(f"     公式数: {doc_info.get('formulas', 0)}")
            
            formula_info = result['formula_conversion']
            print(f"   公式转换:")
            print(f"     总数: {formula_info.get('total', 0)}")
            print(f"     成功: {formula_info.get('successful', 0)}")
            print(f"     失败: {formula_info.get('failed', 0)}")
            
            print(f"   处理时间: {result.get('processing_time', 0):.2f} 秒")
            
        else:
            print(f"❌ 转换失败: {result.get('error', '未知错误')}")
            return
        
        # 显示统计信息
        print("\n5. 转换统计:")
        stats = converter.get_conversion_stats()
        print(f"   总转换次数: {stats.get('total_conversions', 0)}")
        print(f"   成功转换: {stats.get('successful_conversions', 0)}")
        print(f"   失败转换: {stats.get('failed_conversions', 0)}")
        print(f"   总公式数: {stats.get('total_formulas', 0)}")
        print(f"   成功公式: {stats.get('successful_formulas', 0)}")
        
        print("\n✅ 完整转换测试成功!")
        print(f"\n📄 输出文件已生成: {output_file}")
        print("   请用Microsoft Word打开查看转换结果")
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请确保已安装所有依赖: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_individual_components():
    """测试各个组件"""
    print("\n" + "="*50)
    print("组件测试")
    print("="*50)
    
    try:
        # 测试LaTeX解析器
        print("1. 测试LaTeX解析器...")
        from parsers.latex_parser import LaTeXParser
        
        parser = LaTeXParser()
        document = parser.parse_file("examples/input/sample.tex")
        print(f"   ✅ 解析成功: {len(document.sections)} 个章节, {len(document.formulas)} 个公式")
        
        # 测试公式分类器
        print("2. 测试公式分类器...")
        from converters.formula_classifier import FormulaClassifier
        
        classifier = FormulaClassifier()
        test_formula = r"\frac{a}{b} + \sqrt{c^2 + d^2}"
        complexity = classifier.classify_complexity(test_formula)
        formula_type = classifier.classify_formula_type(test_formula)
        print(f"   ✅ 公式分析成功: 复杂度={complexity}, 类型={formula_type}")
        
        # 测试公式转换器
        print("3. 测试公式转换器...")
        from converters.formula_converter import FormulaConverter
        
        converter = FormulaConverter()
        result = converter.convert_formula(test_formula, "inline")
        if result.success:
            print(f"   ✅ 公式转换成功: 方法={result.conversion_method}")
        else:
            print(f"   ⚠️ 公式转换失败: {result.error_message}")
        
        # 测试Word生成器
        print("4. 测试Word生成器...")
        from generators.word_generator import WordGenerator
        from docx import Document
        
        word_gen = WordGenerator()
        doc = Document()
        doc.add_heading("测试文档", 0)
        doc.add_paragraph("这是一个测试段落。")
        
        test_output = "examples/output/test_word.docx"
        Path(test_output).parent.mkdir(parents=True, exist_ok=True)
        doc.save(test_output)
        print(f"   ✅ Word文档生成成功: {test_output}")
        
        print("\n✅ 所有组件测试通过!")
        
    except Exception as e:
        print(f"❌ 组件测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_dependencies():
    """检查依赖"""
    print("\n" + "="*50)
    print("依赖检查")
    print("="*50)
    
    required_packages = [
        'docx',
        'yaml', 
        'regex',
        'lxml',
        'pylatexenc',
        'loguru',
        'pydantic',
        'pathlib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'docx':
                import docx
            elif package == 'yaml':
                import yaml
            elif package == 'regex':
                import re  # 使用标准库的re模块
            else:
                __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺失的包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ 所有依赖都已安装")
        return True

def main():
    """主函数"""
    print("LaTeX到Word转换系统 - 完整测试")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装缺失的包")
        return
    
    # 测试各个组件
    test_individual_components()
    
    # 测试完整转换流程
    test_full_conversion()
    
    print("\n" + "="*60)
    print("测试完成!")
    print("\n下一步建议:")
    print("1. 检查生成的Word文档")
    print("2. 开发MathType集成模块")
    print("3. 优化公式转换算法")
    print("4. 添加更多样式模板")
    print("5. 完善错误处理机制")

if __name__ == "__main__":
    main()
