"""
文档结构分析器

负责分析LaTeX文档的层次结构，包括章节、段落等。
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from ..core.exceptions import LaTeXParseError
from ..utils.logger import get_logger
from ..utils.error_handler import handle_errors

logger = get_logger(__name__)


@dataclass
class Section:
    """章节数据结构"""
    level: int                    # 章节级别 (1=section, 2=subsection, etc.)
    title: str                    # 章节标题
    content: str                  # 章节内容
    start_pos: int               # 开始位置
    end_pos: int                 # 结束位置
    numbering: Optional[str] = None  # 章节编号
    label: Optional[str] = None   # 标签
    subsections: List['Section'] = None  # 子章节
    
    def __post_init__(self):
        if self.subsections is None:
            self.subsections = []


class StructureAnalyzer:
    """
    文档结构分析器
    
    分析LaTeX文档的层次结构，提取章节信息。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化结构分析器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 章节级别映射
        self.section_levels = {
            'part': 0,
            'chapter': 1,
            'section': 2,
            'subsection': 3,
            'subsubsection': 4,
            'paragraph': 5,
            'subparagraph': 6
        }
        
        # 编译正则表达式
        self._compile_patterns()
    
    def _compile_patterns(self) -> None:
        """编译正则表达式模式"""
        
        # 章节标题模式
        self.section_pattern = re.compile(
            r'\\(part|chapter|(?:sub)*section|(?:sub)*paragraph)(\*?)\s*'
            r'(?:\[([^\]]*)\])?\s*\{([^}]+)\}',
            re.MULTILINE
        )
        
        # 标签模式
        self.label_pattern = re.compile(r'\\label\{([^}]+)\}')
        
        # 目录命令
        self.toc_pattern = re.compile(r'\\tableofcontents')
        
        # 页面分隔
        self.page_break_pattern = re.compile(r'\\(?:newpage|clearpage|pagebreak)')
    
    @handle_errors(context="分析文档结构")
    def analyze_structure(self, content: str) -> List[Dict[str, Any]]:
        """
        分析文档结构
        
        Args:
            content: 文档内容
            
        Returns:
            章节结构列表
        """
        logger.info("开始分析文档结构")
        
        # 查找所有章节
        sections = self._find_sections(content)
        
        # 构建层次结构
        structured_sections = self._build_hierarchy(sections, content)
        
        # 提取章节内容
        self._extract_section_content(structured_sections, content)
        
        logger.info(f"文档结构分析完成，共 {len(structured_sections)} 个顶级章节")
        
        return [self._section_to_dict(section) for section in structured_sections]
    
    def _find_sections(self, content: str) -> List[Section]:
        """查找所有章节"""
        sections = []
        
        matches = self.section_pattern.finditer(content)
        for match in matches:
            section_type, star, optional_title, title = match.groups()
            
            # 确定章节级别
            level = self.section_levels.get(section_type, 2)
            
            # 检查是否为无编号章节
            is_unnumbered = star == '*'
            
            # 查找标签
            label = self._find_label_after_position(content, match.end())
            
            section = Section(
                level=level,
                title=title.strip(),
                content="",  # 稍后填充
                start_pos=match.start(),
                end_pos=match.end(),
                label=label
            )
            
            sections.append(section)
        
        return sections
    
    def _find_label_after_position(self, content: str, pos: int) -> Optional[str]:
        """在指定位置后查找标签"""
        # 在章节标题后的一小段内容中查找标签
        search_content = content[pos:pos+200]  # 搜索后200个字符
        
        label_match = self.label_pattern.search(search_content)
        if label_match:
            return label_match.group(1)
        
        return None
    
    def _build_hierarchy(self, sections: List[Section], content: str) -> List[Section]:
        """构建章节层次结构"""
        if not sections:
            return []
        
        # 按位置排序
        sections.sort(key=lambda s: s.start_pos)
        
        # 构建层次结构
        root_sections = []
        section_stack = []
        
        for section in sections:
            # 弹出级别大于等于当前章节的章节
            while section_stack and section_stack[-1].level >= section.level:
                section_stack.pop()
            
            if section_stack:
                # 添加为子章节
                parent = section_stack[-1]
                parent.subsections.append(section)
            else:
                # 添加为根章节
                root_sections.append(section)
            
            section_stack.append(section)
        
        return root_sections
    
    def _extract_section_content(self, sections: List[Section], content: str) -> None:
        """提取章节内容"""
        all_sections = self._flatten_sections(sections)
        
        for i, section in enumerate(all_sections):
            # 确定内容结束位置
            if i + 1 < len(all_sections):
                end_pos = all_sections[i + 1].start_pos
            else:
                end_pos = len(content)
            
            # 提取内容
            section_content = content[section.end_pos:end_pos].strip()
            
            # 移除子章节的内容
            for subsection in section.subsections:
                subsection_start = subsection.start_pos - section.end_pos
                if subsection_start >= 0:
                    section_content = section_content[:subsection_start].strip()
                    break
            
            section.content = section_content
            section.end_pos = end_pos
    
    def _flatten_sections(self, sections: List[Section]) -> List[Section]:
        """扁平化章节列表"""
        flattened = []
        
        def _add_sections(section_list):
            for section in section_list:
                flattened.append(section)
                _add_sections(section.subsections)
        
        _add_sections(sections)
        return sorted(flattened, key=lambda s: s.start_pos)
    
    def _section_to_dict(self, section: Section) -> Dict[str, Any]:
        """将章节对象转换为字典"""
        return {
            'level': section.level,
            'title': section.title,
            'content': section.content,
            'start_pos': section.start_pos,
            'end_pos': section.end_pos,
            'numbering': section.numbering,
            'label': section.label,
            'subsections': [self._section_to_dict(sub) for sub in section.subsections]
        }
    
    def generate_toc(self, sections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        生成目录
        
        Args:
            sections: 章节列表
            
        Returns:
            目录项列表
        """
        toc = []
        
        def _add_to_toc(section_list, parent_numbering=""):
            for i, section in enumerate(section_list):
                # 生成编号
                if parent_numbering:
                    numbering = f"{parent_numbering}.{i + 1}"
                else:
                    numbering = str(i + 1)
                
                toc_item = {
                    'level': section['level'],
                    'title': section['title'],
                    'numbering': numbering,
                    'label': section.get('label')
                }
                
                toc.append(toc_item)
                
                # 递归处理子章节
                if section['subsections']:
                    _add_to_toc(section['subsections'], numbering)
        
        _add_to_toc(sections)
        return toc
    
    def find_section_by_label(self, sections: List[Dict[str, Any]], label: str) -> Optional[Dict[str, Any]]:
        """
        根据标签查找章节
        
        Args:
            sections: 章节列表
            label: 标签
            
        Returns:
            章节信息或None
        """
        def _search_sections(section_list):
            for section in section_list:
                if section.get('label') == label:
                    return section
                
                # 递归搜索子章节
                result = _search_sections(section['subsections'])
                if result:
                    return result
            
            return None
        
        return _search_sections(sections)
    
    def get_section_path(self, sections: List[Dict[str, Any]], target_section: Dict[str, Any]) -> List[str]:
        """
        获取章节路径
        
        Args:
            sections: 章节列表
            target_section: 目标章节
            
        Returns:
            章节路径（标题列表）
        """
        def _find_path(section_list, path):
            for section in section_list:
                current_path = path + [section['title']]
                
                if section == target_section:
                    return current_path
                
                # 递归搜索子章节
                result = _find_path(section['subsections'], current_path)
                if result:
                    return result
            
            return None
        
        return _find_path(sections, []) or []
