# Word文档样式模板配置

# 默认样式模板
default:
  name: "默认模板"
  description: "标准的学术文档样式"
  
  # 页面设置
  page_setup:
    size: "A4"
    orientation: "portrait"
    margins:
      top: 2.5
      bottom: 2.5
      left: 2.5
      right: 2.5
    header_margin: 1.25
    footer_margin: 1.25
  
  # 字体设置
  fonts:
    default: "Times New Roman"
    chinese: "宋体"
    code: "Courier New"
    math: "Cambria Math"
  
  # 段落样式
  paragraph_styles:
    normal:
      font_family: "Times New Roman"
      font_size: 12
      line_spacing: 1.5
      space_after: 6
      alignment: "justify"
      first_line_indent: 0
    
    title:
      font_family: "Times New Roman"
      font_size: 18
      bold: true
      alignment: "center"
      space_before: 0
      space_after: 18
    
    author:
      font_family: "Times New Roman"
      font_size: 14
      alignment: "center"
      space_after: 12
    
    abstract:
      font_family: "Times New Roman"
      font_size: 11
      line_spacing: 1.2
      left_indent: 1.0
      right_indent: 1.0
      space_before: 12
      space_after: 12
    
    quote:
      font_family: "Times New Roman"
      font_size: 11
      italic: true
      left_indent: 1.0
      right_indent: 1.0
      space_before: 6
      space_after: 6
    
    code:
      font_family: "Courier New"
      font_size: 10
      background_color: "#f5f5f5"
      border: true
      left_indent: 0.5
      space_before: 6
      space_after: 6
  
  # 标题样式
  heading_styles:
    heading1:
      font_family: "Times New Roman"
      font_size: 16
      bold: true
      space_before: 18
      space_after: 12
      numbering: true
      numbering_format: "1"
      outline_level: 1
    
    heading2:
      font_family: "Times New Roman"
      font_size: 14
      bold: true
      space_before: 14
      space_after: 8
      numbering: true
      numbering_format: "1.1"
      outline_level: 2
    
    heading3:
      font_family: "Times New Roman"
      font_size: 12
      bold: true
      space_before: 12
      space_after: 6
      numbering: true
      numbering_format: "1.1.1"
      outline_level: 3
    
    heading4:
      font_family: "Times New Roman"
      font_size: 12
      bold: true
      italic: true
      space_before: 10
      space_after: 4
      numbering: true
      numbering_format: "1.1.1.1"
      outline_level: 4
  
  # 列表样式
  list_styles:
    bullet:
      bullet_char: "•"
      left_indent: 1.0
      hanging_indent: 0.5
      space_after: 3
    
    numbered:
      numbering_format: "1."
      left_indent: 1.0
      hanging_indent: 0.5
      space_after: 3
    
    definition:
      left_indent: 0.5
      hanging_indent: 0.5
      space_after: 6
  
  # 表格样式
  table_styles:
    default:
      border_style: "single"
      border_width: 0.5
      cell_padding: 3
      header_background: "#f0f0f0"
      header_bold: true
      alignment: "left"
    
    simple:
      border_style: "none"
      header_underline: true
      cell_padding: 6
      alignment: "left"
    
    formal:
      border_style: "single"
      border_width: 1.0
      cell_padding: 6
      header_background: "#e0e0e0"
      header_bold: true
      alignment: "center"
  
  # 公式样式
  formula_styles:
    inline:
      font_family: "Cambria Math"
      font_size: 12
      baseline_offset: 0
    
    display:
      font_family: "Cambria Math"
      font_size: 12
      alignment: "center"
      space_before: 12
      space_after: 12
      numbering: true
      numbering_position: "right"
    
    equation_array:
      font_family: "Cambria Math"
      font_size: 12
      alignment: "center"
      space_before: 12
      space_after: 12
      column_spacing: 1.0

# 学术论文模板
academic:
  name: "学术论文模板"
  description: "适用于学术论文的正式样式"
  
  # 继承默认模板
  inherit_from: "default"
  
  # 覆盖特定样式
  paragraph_styles:
    normal:
      font_size: 11
      line_spacing: 1.2
      space_after: 3
    
    abstract:
      font_size: 10
      line_spacing: 1.0
      left_indent: 0.5
      right_indent: 0.5
  
  heading_styles:
    heading1:
      font_size: 14
      space_before: 14
      space_after: 8
    
    heading2:
      font_size: 12
      space_before: 10
      space_after: 6

# 技术文档模板
technical:
  name: "技术文档模板"
  description: "适用于技术文档的样式"
  
  inherit_from: "default"
  
  fonts:
    default: "Arial"
    code: "Consolas"
  
  paragraph_styles:
    normal:
      font_family: "Arial"
      font_size: 11
      line_spacing: 1.3
    
    code:
      font_family: "Consolas"
      font_size: 9
      background_color: "#f8f8f8"
      border: true
      border_color: "#cccccc"
  
  heading_styles:
    heading1:
      font_family: "Arial"
      font_size: 16
      color: "#2e74b5"
    
    heading2:
      font_family: "Arial"
      font_size: 14
      color: "#2e74b5"

# 中文文档模板
chinese:
  name: "中文文档模板"
  description: "适用于中文文档的样式"
  
  inherit_from: "default"
  
  fonts:
    default: "宋体"
    heading: "黑体"
    english: "Times New Roman"
  
  paragraph_styles:
    normal:
      font_family: "宋体"
      font_size: 12
      line_spacing: 1.5
      first_line_indent: 2.0  # 中文段落首行缩进2字符
    
    title:
      font_family: "黑体"
      font_size: 22
      bold: true
      alignment: "center"
  
  heading_styles:
    heading1:
      font_family: "黑体"
      font_size: 18
      bold: true
    
    heading2:
      font_family: "黑体"
      font_size: 16
      bold: true
    
    heading3:
      font_family: "黑体"
      font_size: 14
      bold: true
