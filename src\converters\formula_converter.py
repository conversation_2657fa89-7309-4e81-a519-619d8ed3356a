"""
数学公式转换器

主要的公式转换器，负责将LaTeX数学公式转换为MathType格式。
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass

from ..core.exceptions import FormulaConversionError
from ..utils.logger import get_logger
from ..utils.error_handler import handle_errors, log_performance
from ..utils.config_manager import ConfigManager

logger = get_logger(__name__)


@dataclass
class ConversionResult:
    """转换结果数据结构"""
    success: bool                           # 转换是否成功
    mathtype_content: Optional[str] = None  # MathType格式内容
    error_message: Optional[str] = None     # 错误信息
    original_content: Optional[str] = None  # 原始LaTeX内容
    conversion_method: Optional[str] = None # 转换方法
    metadata: Dict[str, Any] = None        # 元数据
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class FormulaConverter:
    """
    数学公式转换器
    
    负责将LaTeX数学公式转换为MathType格式，支持多种转换策略。
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化公式转换器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or ConfigManager()
        self.config = self.config_manager.get_config()
        
        # 初始化子模块
        self._init_submodules()
        
        # 加载公式映射
        self.formula_mapping = self.config_manager.get_formula_mapping()
        
        # 转换统计
        self.conversion_stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'by_type': {},
            'by_method': {}
        }
    
    def _init_submodules(self) -> None:
        """初始化子模块"""
        from .latex_formula_parser import LaTeXFormulaParser
        from .mathtype_formatter import MathTypeFormatter
        from .formula_classifier import FormulaClassifier
        
        self.latex_parser = LaTeXFormulaParser(self.config.formula_conversion)
        self.mathtype_formatter = MathTypeFormatter(self.config.mathtype)
        self.formula_classifier = FormulaClassifier(self.config.formula_conversion)
    
    @handle_errors(context="转换数学公式")
    @log_performance
    def convert_formula(
        self,
        latex_content: str,
        formula_type: str = "inline",
        context: Optional[Dict[str, Any]] = None
    ) -> ConversionResult:
        """
        转换单个数学公式
        
        Args:
            latex_content: LaTeX公式内容
            formula_type: 公式类型 (inline/display/equation等)
            context: 转换上下文信息
            
        Returns:
            转换结果
        """
        logger.debug(f"开始转换公式: {latex_content[:50]}...")
        
        self.conversion_stats['total'] += 1
        
        try:
            # 预处理LaTeX内容
            processed_content = self._preprocess_latex(latex_content)
            
            # 分类公式复杂度
            complexity = self.formula_classifier.classify_complexity(processed_content)
            
            # 解析LaTeX公式
            parsed_formula = self.latex_parser.parse(processed_content, formula_type)
            
            # 选择转换方法
            conversion_method = self._select_conversion_method(
                parsed_formula, complexity, context
            )
            
            # 执行转换
            mathtype_content = self._execute_conversion(
                parsed_formula, conversion_method, context
            )
            
            # 后处理
            final_content = self._postprocess_mathtype(mathtype_content, formula_type)
            
            # 创建成功结果
            result = ConversionResult(
                success=True,
                mathtype_content=final_content,
                original_content=latex_content,
                conversion_method=conversion_method,
                metadata={
                    'formula_type': formula_type,
                    'complexity': complexity,
                    'processing_time': 0  # 将由装饰器填充
                }
            )
            
            self.conversion_stats['success'] += 1
            self._update_stats(formula_type, conversion_method, True)
            
            logger.debug(f"公式转换成功: {conversion_method}")
            return result
            
        except Exception as e:
            error_msg = f"公式转换失败: {e}"
            logger.error(error_msg)
            
            self.conversion_stats['failed'] += 1
            self._update_stats(formula_type, "failed", False)
            
            return ConversionResult(
                success=False,
                error_message=error_msg,
                original_content=latex_content,
                metadata={'formula_type': formula_type}
            )
    
    def convert_formulas_batch(
        self,
        formulas: List[Dict[str, Any]],
        context: Optional[Dict[str, Any]] = None
    ) -> List[ConversionResult]:
        """
        批量转换数学公式
        
        Args:
            formulas: 公式列表，每个元素包含content和type字段
            context: 转换上下文信息
            
        Returns:
            转换结果列表
        """
        logger.info(f"开始批量转换 {len(formulas)} 个公式")
        
        results = []
        
        for i, formula in enumerate(formulas):
            try:
                content = formula.get('content', '')
                formula_type = formula.get('type', 'inline')
                
                # 添加批次信息到上下文
                batch_context = (context or {}).copy()
                batch_context.update({
                    'batch_index': i,
                    'batch_total': len(formulas),
                    'formula_id': formula.get('id', f'formula_{i}')
                })
                
                result = self.convert_formula(content, formula_type, batch_context)
                results.append(result)
                
                # 进度日志
                if (i + 1) % 10 == 0:
                    logger.info(f"批量转换进度: {i + 1}/{len(formulas)}")
                    
            except Exception as e:
                logger.error(f"批量转换第 {i} 个公式失败: {e}")
                results.append(ConversionResult(
                    success=False,
                    error_message=str(e),
                    original_content=formula.get('content', ''),
                    metadata={'batch_index': i}
                ))
        
        success_count = sum(1 for r in results if r.success)
        logger.info(f"批量转换完成: {success_count}/{len(formulas)} 成功")
        
        return results
    
    def _preprocess_latex(self, content: str) -> str:
        """预处理LaTeX内容"""
        # 移除多余的空白
        content = content.strip()
        
        # 标准化换行符
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        
        # 处理自定义命令
        content = self._expand_custom_commands(content)
        
        return content
    
    def _expand_custom_commands(self, content: str) -> str:
        """展开自定义命令"""
        # 从配置中获取自定义命令映射
        custom_commands = self.formula_mapping.get('custom_commands', {})
        
        for command, replacement in custom_commands.items():
            # 简单的字符串替换
            content = content.replace(f'\\{command}', replacement)
        
        return content
    
    def _select_conversion_method(
        self,
        parsed_formula: Dict[str, Any],
        complexity: str,
        context: Optional[Dict[str, Any]]
    ) -> str:
        """选择转换方法"""
        
        # 根据复杂度选择方法
        if complexity == "simple":
            return "direct_mapping"
        elif complexity == "medium":
            return "template_based"
        else:
            return "mathtype_api"
    
    def _execute_conversion(
        self,
        parsed_formula: Dict[str, Any],
        method: str,
        context: Optional[Dict[str, Any]]
    ) -> str:
        """执行转换"""
        
        if method == "direct_mapping":
            return self._convert_by_direct_mapping(parsed_formula)
        elif method == "template_based":
            return self._convert_by_template(parsed_formula)
        elif method == "mathtype_api":
            return self._convert_by_mathtype_api(parsed_formula, context)
        else:
            raise FormulaConversionError(
                f"未知的转换方法: {method}",
                conversion_stage="method_selection"
            )
    
    def _convert_by_direct_mapping(self, parsed_formula: Dict[str, Any]) -> str:
        """通过直接映射转换"""
        symbols = self.formula_mapping.get('symbols', {})
        functions = self.formula_mapping.get('functions', {})
        
        content = parsed_formula['content']
        
        # 替换符号
        for latex_symbol, unicode_symbol in symbols.items():
            content = content.replace(latex_symbol, unicode_symbol)
        
        # 替换函数
        for latex_func, mathtype_func in functions.items():
            content = content.replace(latex_func, mathtype_func)
        
        return content
    
    def _convert_by_template(self, parsed_formula: Dict[str, Any]) -> str:
        """通过模板转换"""
        # 使用MathType格式化器
        return self.mathtype_formatter.format_formula(parsed_formula)
    
    def _convert_by_mathtype_api(
        self,
        parsed_formula: Dict[str, Any],
        context: Optional[Dict[str, Any]]
    ) -> str:
        """通过MathType API转换"""
        # 这里将调用MathType集成模块
        from ..integrations.mathtype_integration import MathTypeIntegration
        
        mathtype_integration = MathTypeIntegration(self.config.mathtype)
        return mathtype_integration.convert_formula(parsed_formula, context)
    
    def _postprocess_mathtype(self, content: str, formula_type: str) -> str:
        """后处理MathType内容"""
        # 根据公式类型调整格式
        if formula_type == "display":
            # 显示公式可能需要居中等格式
            pass
        elif formula_type == "inline":
            # 内联公式可能需要调整基线
            pass
        
        return content
    
    def _update_stats(self, formula_type: str, method: str, success: bool) -> None:
        """更新转换统计"""
        # 按类型统计
        if formula_type not in self.conversion_stats['by_type']:
            self.conversion_stats['by_type'][formula_type] = {'success': 0, 'failed': 0}
        
        if success:
            self.conversion_stats['by_type'][formula_type]['success'] += 1
        else:
            self.conversion_stats['by_type'][formula_type]['failed'] += 1
        
        # 按方法统计
        if method not in self.conversion_stats['by_method']:
            self.conversion_stats['by_method'][method] = {'success': 0, 'failed': 0}
        
        if success:
            self.conversion_stats['by_method'][method]['success'] += 1
        else:
            self.conversion_stats['by_method'][method]['failed'] += 1
    
    def get_conversion_stats(self) -> Dict[str, Any]:
        """获取转换统计信息"""
        return self.conversion_stats.copy()
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.conversion_stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'by_type': {},
            'by_method': {}
        }
