# Translations template for Sphinx.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <PERSON> <<PERSON><PERSON><PERSON>@hotmail.co.uk>, 2020
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-10-10 15:47+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: <PERSON> <<PERSON><PERSON><PERSON>@hotmail.co.uk>, 2020\n"
"Language-Team: Arabic (http://app.transifex.com/sphinx-doc/sphinx-1/language/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#: events.py:77
#, python-format
msgid "Event %r already present"
msgstr ""

#: events.py:370
#, python-format
msgid "Unknown event name: %s"
msgstr "حدث غير معروف: %s"

#: events.py:416
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr ""

#: application.py:186
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "لا يمكن العثور على المجلد المصدر (%s)"

#: application.py:190
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr ""

#: application.py:194
msgid "Source directory and destination directory cannot be identical"
msgstr "لا يمكن ان يكون المجلد المصدر والمجلد الهدف متطابقين"

#: application.py:224
#, python-format
msgid "Running Sphinx v%s"
msgstr "تشغيل Sphinx v%s"

#: application.py:246
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "يحتاج هذا المشروع على الاقل الى الاصدار %s من Sphinx وبالتالي لا يمكن بناءه باستخدام الاصدار الحالي"

#: application.py:262
msgid "making output directory"
msgstr ""

#: application.py:267 registry.py:450
#, python-format
msgid "while setting up extension %s:"
msgstr ""

#: application.py:273
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr ""

#: application.py:308
#, python-format
msgid "loading translations [%s]... "
msgstr "تحميل الترجمات [ %s ]"

#: application.py:325 util/display.py:90
msgid "done"
msgstr "تم"

#: application.py:327
msgid "not available for built-in messages"
msgstr "غير متوفرة للرسائل الافتراضية المدمجة"

#: application.py:341
msgid "loading pickled environment"
msgstr ""

#: application.py:349
#, python-format
msgid "failed: %s"
msgstr "فشل: %s"

#: application.py:362
msgid "No builder selected, using default: html"
msgstr "لم يتم اختيار نوع البناء، تم استخدام نوع البناء الافتراضي: html"

#: application.py:394
msgid "build finished with problems."
msgstr ""

#: application.py:396
msgid "build succeeded."
msgstr ""

#: application.py:400
msgid ""
"build finished with problems, 1 warning (with warnings treated as errors)."
msgstr ""

#: application.py:403
msgid "build finished with problems, 1 warning."
msgstr ""

#: application.py:405
msgid "build succeeded, 1 warning."
msgstr ""

#: application.py:410
#, python-format
msgid ""
"build finished with problems, %s warnings (with warnings treated as errors)."
msgstr ""

#: application.py:413
#, python-format
msgid "build finished with problems, %s warnings."
msgstr ""

#: application.py:415
#, python-format
msgid "build succeeded, %s warnings."
msgstr ""

#: application.py:964
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr ""

#: application.py:1043
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr ""

#: application.py:1065 application.py:1090
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr ""

#: application.py:1640
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr ""

#: application.py:1644
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr ""

#: application.py:1647
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr ""

#: application.py:1651
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr ""

#: application.py:1659 application.py:1663
#, python-format
msgid "doing serial %s"
msgstr ""

#: roles.py:205
#, python-format
msgid "Common Vulnerabilities and Exposures; CVE %s"
msgstr ""

#: roles.py:228
#, python-format
msgid "invalid CVE number %s"
msgstr ""

#: roles.py:249
#, python-format
msgid "Common Weakness Enumeration; CWE %s"
msgstr ""

#: roles.py:272
#, python-format
msgid "invalid CWE number %s"
msgstr ""

#: roles.py:293
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr ""

#: roles.py:316
#, python-format
msgid "invalid PEP number %s"
msgstr ""

#: roles.py:354
#, python-format
msgid "invalid RFC number %s"
msgstr ""

#: registry.py:142
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr ""

#: registry.py:144
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr ""

#: registry.py:157
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr ""

#: registry.py:164
#, python-format
msgid "Builder name %s not registered"
msgstr ""

#: registry.py:171
#, python-format
msgid "domain %s already registered"
msgstr ""

#: registry.py:194 registry.py:207 registry.py:218
#, python-format
msgid "domain %s not yet registered"
msgstr ""

#: registry.py:198
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr ""

#: registry.py:210
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr ""

#: registry.py:221
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr ""

#: registry.py:252
#, python-format
msgid "The %r object_type is already registered"
msgstr ""

#: registry.py:278
#, python-format
msgid "The %r crossref_type is already registered"
msgstr ""

#: registry.py:285
#, python-format
msgid "source_suffix %r is already registered"
msgstr ""

#: registry.py:294
#, python-format
msgid "source_parser for %r is already registered"
msgstr ""

#: registry.py:302
#, python-format
msgid "Source parser for %s not registered"
msgstr ""

#: registry.py:318
#, python-format
msgid "Translator for %r already exists"
msgstr ""

#: registry.py:334
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr ""

#: registry.py:417
#, python-format
msgid "enumerable_node %r already registered"
msgstr ""

#: registry.py:429
#, python-format
msgid "math renderer %s is already registered"
msgstr ""

#: registry.py:444
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr ""

#: registry.py:455
msgid "Original exception:\n"
msgstr ""

#: registry.py:456
#, python-format
msgid "Could not import extension %s"
msgstr ""

#: registry.py:461
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr ""

#: registry.py:470
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr ""

#: registry.py:478
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr ""

#: registry.py:512
#, python-format
msgid "`None` is not a valid filetype for %r."
msgstr ""

#: project.py:71
#, python-format
msgid ""
"multiple files found for the document \"%s\": %s\n"
"Use %r for the build."
msgstr ""

#: project.py:87
#, python-format
msgid "Ignored unreadable document %r."
msgstr ""

#: highlighting.py:168
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr ""

#: highlighting.py:202
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr ""

#: extension.py:55
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr ""

#: extension.py:76
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr ""

#: theming.py:121
#, python-format
msgid ""
"Theme configuration sections other than [theme] and [options] are not "
"supported (tried to get a value from %r)."
msgstr ""

#: theming.py:127
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr ""

#: theming.py:142
#, python-format
msgid "unsupported theme option %r given"
msgstr ""

#: theming.py:215
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr ""

#: theming.py:236
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr ""

#: theming.py:276
#, python-format
msgid "The %r theme has circular inheritance"
msgstr ""

#: theming.py:283
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr ""

#: theming.py:290
#, python-format
msgid "The %r theme has too many ancestors"
msgstr ""

#: theming.py:318
#, python-format
msgid "no theme configuration file found in %r"
msgstr ""

#: theming.py:346 theming.py:399
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr ""

#: theming.py:350
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr ""

#: theming.py:354 theming.py:402
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr ""

#: theming.py:358
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr ""

#: theming.py:377
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr ""

#: config.py:314
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "مجلد الاعدادات لا يحتوي على ملف conf.py (%s)"

#: config.py:323
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr ""

#: config.py:346
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr ""

#: config.py:355
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr ""

#: config.py:361
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr ""

#: config.py:382
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr ""

#: config.py:435
#, python-format
msgid "No such config value: %r"
msgstr ""

#: config.py:458
#, python-format
msgid "Config value %r already present"
msgstr ""

#: config.py:494
#, python-format
msgid ""
"cannot cache unpickable configuration value: %r (because it contains a "
"function, class, or module object)"
msgstr ""

#: config.py:531
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr ""

#: config.py:534
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr ""

#: config.py:541
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr ""

#: config.py:564
#, python-format
msgid "Failed to convert %r to a set or tuple"
msgstr ""

#: config.py:585 config.py:590
#, python-format
msgid "Converting `source_suffix = %r` to `source_suffix = %r`."
msgstr ""

#: config.py:593
#, python-format
msgid ""
"The config value `source_suffix' expects a dictionary, a string, or a list "
"of strings. Got `%r' instead (type %s)."
msgstr ""

#: config.py:612
#, python-format
msgid "Section %s"
msgstr "قسم %s"

#: config.py:613
#, python-format
msgid "Fig. %s"
msgstr ""

#: config.py:614
#, python-format
msgid "Table %s"
msgstr "جدول %s"

#: config.py:615
#, python-format
msgid "Listing %s"
msgstr ""

#: config.py:722
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr ""

#: config.py:746
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr ""

#: config.py:759
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr ""

#: config.py:770
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "primary_domain %r لتم يتم العثور عليه، لهذا تم تجاهلة"

#: config.py:782
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr ""

#: domains/rst.py:127 domains/rst.py:184
#, python-format
msgid "%s (directive)"
msgstr ""

#: domains/rst.py:185 domains/rst.py:189
#, python-format
msgid ":%s: (directive option)"
msgstr ""

#: domains/rst.py:213
#, python-format
msgid "%s (role)"
msgstr ""

#: domains/rst.py:223
msgid "directive"
msgstr ""

#: domains/rst.py:224
msgid "directive-option"
msgstr ""

#: domains/rst.py:225
msgid "role"
msgstr "متغير بيئة العمل"

#: domains/rst.py:247
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr ""

#: domains/javascript.py:165
#, python-format
msgid "%s() (built-in function)"
msgstr ""

#: domains/javascript.py:166 domains/python/__init__.py:253
#, python-format
msgid "%s() (%s method)"
msgstr ""

#: domains/javascript.py:168
#, python-format
msgid "%s() (class)"
msgstr ""

#: domains/javascript.py:170
#, python-format
msgid "%s (global variable or constant)"
msgstr ""

#: domains/javascript.py:172 domains/python/__init__.py:338
#, python-format
msgid "%s (%s attribute)"
msgstr ""

#: domains/javascript.py:255
msgid "Arguments"
msgstr ""

#: domains/cpp/__init__.py:442 domains/javascript.py:258
msgid "Throws"
msgstr ""

#: domains/c/__init__.py:304 domains/cpp/__init__.py:453
#: domains/javascript.py:261 domains/python/_object.py:176
msgid "Returns"
msgstr ""

#: domains/c/__init__.py:306 domains/javascript.py:263
#: domains/python/_object.py:178
msgid "Return type"
msgstr ""

#: domains/javascript.py:334
#, python-format
msgid "%s (module)"
msgstr ""

#: domains/c/__init__.py:675 domains/cpp/__init__.py:854
#: domains/javascript.py:371 domains/python/__init__.py:629
msgid "function"
msgstr ""

#: domains/javascript.py:372 domains/python/__init__.py:633
msgid "method"
msgstr ""

#: domains/cpp/__init__.py:852 domains/javascript.py:373
#: domains/python/__init__.py:631
msgid "class"
msgstr "كائن"

#: domains/javascript.py:374 domains/python/__init__.py:630
msgid "data"
msgstr ""

#: domains/javascript.py:375 domains/python/__init__.py:636
msgid "attribute"
msgstr ""

#: domains/javascript.py:376 domains/python/__init__.py:639
msgid "module"
msgstr ""

#: domains/javascript.py:407
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr ""

#: domains/changeset.py:25
#, python-format
msgid "Added in version %s"
msgstr ""

#: domains/changeset.py:26
#, python-format
msgid "Changed in version %s"
msgstr ""

#: domains/changeset.py:27
#, python-format
msgid "Deprecated since version %s"
msgstr ""

#: domains/changeset.py:28
#, python-format
msgid "Removed in version %s"
msgstr ""

#: domains/__init__.py:299
#, python-format
msgid "%s %s"
msgstr ""

#: domains/citation.py:73
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr ""

#: domains/citation.py:84
#, python-format
msgid "Citation [%s] is not referenced."
msgstr ""

#: domains/math.py:63
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr ""

#: domains/math.py:119 writers/latex.py:2479
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr ""

#: environment/__init__.py:86
msgid "new config"
msgstr ""

#: environment/__init__.py:87
msgid "config changed"
msgstr ""

#: environment/__init__.py:88
msgid "extensions changed"
msgstr ""

#: environment/__init__.py:249
msgid "build environment version not current"
msgstr ""

#: environment/__init__.py:251
msgid "source directory has changed"
msgstr ""

#: environment/__init__.py:311
#, python-format
msgid "The configuration has changed (1 option: %r)"
msgstr ""

#: environment/__init__.py:316
#, python-format
msgid "The configuration has changed (%d options: %s)"
msgstr ""

#: environment/__init__.py:322
#, python-format
msgid "The configuration has changed (%d options: %s, ...)"
msgstr ""

#: environment/__init__.py:364
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr ""

#: environment/__init__.py:473
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr ""

#: environment/__init__.py:622
#, python-format
msgid "Domain %r is not registered"
msgstr ""

#: environment/__init__.py:773
msgid "document isn't included in any toctree"
msgstr ""

#: environment/__init__.py:806
msgid "self referenced toctree found. Ignored."
msgstr ""

#: environment/__init__.py:835
#, python-format
msgid "document is referenced in multiple toctrees: %s, selecting: %s <- %s"
msgstr ""

#: locale/__init__.py:229
msgid "Attention"
msgstr "تنبيه"

#: locale/__init__.py:230
msgid "Caution"
msgstr "احتياط"

#: locale/__init__.py:231
msgid "Danger"
msgstr "خطر"

#: locale/__init__.py:232
msgid "Error"
msgstr "خطأ"

#: locale/__init__.py:233
msgid "Hint"
msgstr "تلميح"

#: locale/__init__.py:234
msgid "Important"
msgstr "مهم"

#: locale/__init__.py:235
msgid "Note"
msgstr "ملاحظة"

#: locale/__init__.py:236
msgid "See also"
msgstr "شاهد أيضا"

#: locale/__init__.py:237
msgid "Tip"
msgstr "نصيحة"

#: locale/__init__.py:238
msgid "Warning"
msgstr "تحذير"

#: cmd/quickstart.py:43
msgid "automatically insert docstrings from modules"
msgstr ""

#: cmd/quickstart.py:44
msgid "automatically test code snippets in doctest blocks"
msgstr ""

#: cmd/quickstart.py:45
msgid "link between Sphinx documentation of different projects"
msgstr ""

#: cmd/quickstart.py:46
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr ""

#: cmd/quickstart.py:47
msgid "checks for documentation coverage"
msgstr ""

#: cmd/quickstart.py:48
msgid "include math, rendered as PNG or SVG images"
msgstr ""

#: cmd/quickstart.py:49
msgid "include math, rendered in the browser by MathJax"
msgstr ""

#: cmd/quickstart.py:50
msgid "conditional inclusion of content based on config values"
msgstr ""

#: cmd/quickstart.py:51
msgid "include links to the source code of documented Python objects"
msgstr ""

#: cmd/quickstart.py:52
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr ""

#: cmd/quickstart.py:94
msgid "Please enter a valid path name."
msgstr ""

#: cmd/quickstart.py:110
msgid "Please enter some text."
msgstr "الرجاء ادخال بعض النصوص"

#: cmd/quickstart.py:117
#, python-format
msgid "Please enter one of %s."
msgstr ""

#: cmd/quickstart.py:125
msgid "Please enter either 'y' or 'n'."
msgstr "أدخل إما 'نعم' أو'لا'"

#: cmd/quickstart.py:131
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "أدخل امتداد الملف, مثلا '.rst' أو '.txt'"

#: cmd/quickstart.py:213
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr ""

#: cmd/quickstart.py:217
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr ""

#: cmd/quickstart.py:225
#, python-format
msgid "Selected root path: %s"
msgstr ""

#: cmd/quickstart.py:228
msgid "Enter the root path for documentation."
msgstr ""

#: cmd/quickstart.py:229
msgid "Root path for the documentation"
msgstr ""

#: cmd/quickstart.py:237
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr ""

#: cmd/quickstart.py:243
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr ""

#: cmd/quickstart.py:246
msgid "Please enter a new root path (or just Enter to exit)"
msgstr ""

#: cmd/quickstart.py:256
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr ""

#: cmd/quickstart.py:263
msgid "Separate source and build directories (y/n)"
msgstr ""

#: cmd/quickstart.py:269
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr ""

#: cmd/quickstart.py:275
msgid "Name prefix for templates and static dir"
msgstr ""

#: cmd/quickstart.py:280
msgid ""
"The project name will occur in several places in the built documentation."
msgstr ""

#: cmd/quickstart.py:284
msgid "Project name"
msgstr "اسم المشروع"

#: cmd/quickstart.py:286
msgid "Author name(s)"
msgstr "اسم المؤلف(ون)"

#: cmd/quickstart.py:291
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr ""

#: cmd/quickstart.py:299
msgid "Project version"
msgstr "نسخة المشروع"

#: cmd/quickstart.py:301
msgid "Project release"
msgstr "إصدار المشروع"

#: cmd/quickstart.py:306
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr ""

#: cmd/quickstart.py:315
msgid "Project language"
msgstr "لغة المشروع"

#: cmd/quickstart.py:322
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr ""

#: cmd/quickstart.py:327
msgid "Source file suffix"
msgstr "امتداد ملف المصدر"

#: cmd/quickstart.py:332
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr ""

#: cmd/quickstart.py:340
msgid "Name of your master document (without suffix)"
msgstr ""

#: cmd/quickstart.py:350
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr ""

#: cmd/quickstart.py:357
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr ""

#: cmd/quickstart.py:360
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr ""

#: cmd/quickstart.py:369
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr ""

#: cmd/quickstart.py:379
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr ""

#: cmd/quickstart.py:389
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr ""

#: cmd/quickstart.py:395
msgid "Create Makefile? (y/n)"
msgstr "إنشاء Makefile ؟ (نعم / لا)"

#: cmd/quickstart.py:399
msgid "Create Windows command file? (y/n)"
msgstr "إنشاء ملف أوامر للويندوز؟ (نعم/لا)"

#: cmd/quickstart.py:451 ext/apidoc.py:92
#, python-format
msgid "Creating file %s."
msgstr ""

#: cmd/quickstart.py:456 ext/apidoc.py:89
#, python-format
msgid "File %s already exists, skipping."
msgstr ""

#: cmd/quickstart.py:499
msgid "Finished: An initial directory structure has been created."
msgstr ""

#: cmd/quickstart.py:502
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr ""

#: cmd/quickstart.py:510
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr ""

#: cmd/quickstart.py:513
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr ""

#: cmd/quickstart.py:520
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr ""

#: cmd/quickstart.py:555
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr ""

#: cmd/build.py:153 cmd/quickstart.py:565 ext/apidoc.py:374
#: ext/autosummary/generate.py:765
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr ""

#: cmd/quickstart.py:575
msgid "quiet mode"
msgstr ""

#: cmd/quickstart.py:585
msgid "project root"
msgstr ""

#: cmd/quickstart.py:588
msgid "Structure options"
msgstr ""

#: cmd/quickstart.py:594
msgid "if specified, separate source and build dirs"
msgstr ""

#: cmd/quickstart.py:600
msgid "if specified, create build dir under source dir"
msgstr ""

#: cmd/quickstart.py:606
msgid "replacement for dot in _templates etc."
msgstr ""

#: cmd/quickstart.py:609
msgid "Project basic options"
msgstr ""

#: cmd/quickstart.py:611
msgid "project name"
msgstr "اسم المشروع"

#: cmd/quickstart.py:614
msgid "author names"
msgstr "أسماء المؤلفين"

#: cmd/quickstart.py:621
msgid "version of project"
msgstr ""

#: cmd/quickstart.py:628
msgid "release of project"
msgstr ""

#: cmd/quickstart.py:635
msgid "document language"
msgstr ""

#: cmd/quickstart.py:638
msgid "source file suffix"
msgstr ""

#: cmd/quickstart.py:641
msgid "master document name"
msgstr ""

#: cmd/quickstart.py:644
msgid "use epub"
msgstr ""

#: cmd/quickstart.py:647
msgid "Extension options"
msgstr ""

#: cmd/quickstart.py:654 ext/apidoc.py:578
#, python-format
msgid "enable %s extension"
msgstr ""

#: cmd/quickstart.py:661 ext/apidoc.py:570
msgid "enable arbitrary extensions"
msgstr ""

#: cmd/quickstart.py:664
msgid "Makefile and Batchfile creation"
msgstr ""

#: cmd/quickstart.py:670
msgid "create makefile"
msgstr "إنشاء Makefile"

#: cmd/quickstart.py:676
msgid "do not create makefile"
msgstr ""

#: cmd/quickstart.py:683
msgid "create batchfile"
msgstr "إنشاء Batchfile ؟"

#: cmd/quickstart.py:689
msgid "do not create batchfile"
msgstr ""

#: cmd/quickstart.py:698
msgid "use make-mode for Makefile/make.bat"
msgstr ""

#: cmd/quickstart.py:701 ext/apidoc.py:581
msgid "Project templating"
msgstr ""

#: cmd/quickstart.py:707 ext/apidoc.py:587
msgid "template directory for template files"
msgstr ""

#: cmd/quickstart.py:714
msgid "define a template variable"
msgstr "عرف متغير للقالب"

#: cmd/quickstart.py:749
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr ""

#: cmd/quickstart.py:768
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr ""

#: cmd/quickstart.py:775
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr ""

#: cmd/quickstart.py:793
#, python-format
msgid "Invalid template variable: %s"
msgstr ""

#: cmd/build.py:49
msgid "Exception occurred while building, starting debugger:"
msgstr ""

#: _cli/util/errors.py:129 cmd/build.py:65
msgid "Interrupted!"
msgstr ""

#: cmd/build.py:67
msgid "reST markup error:"
msgstr ""

#: _cli/util/errors.py:143 cmd/build.py:73
msgid "Encoding error:"
msgstr ""

#: cmd/build.py:78 cmd/build.py:108
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr ""

#: _cli/util/errors.py:148 cmd/build.py:90
msgid "Recursion error:"
msgstr ""

#: _cli/util/errors.py:152 cmd/build.py:94
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr ""

#: _cli/util/errors.py:165 cmd/build.py:103
msgid "Exception occurred:"
msgstr ""

#: _cli/util/errors.py:178 cmd/build.py:117
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr ""

#: cmd/build.py:124
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr ""

#: cmd/build.py:144
msgid "job number should be a positive number"
msgstr ""

#: cmd/build.py:154
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr ""

#: cmd/build.py:180
msgid "path to documentation source files"
msgstr ""

#: cmd/build.py:183
msgid "path to output directory"
msgstr ""

#: cmd/build.py:188
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr ""

#: cmd/build.py:194
msgid "general options"
msgstr ""

#: cmd/build.py:201
msgid "builder to use (default: 'html')"
msgstr ""

#: cmd/build.py:210
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: cmd/build.py:220
msgid "write all files (default: only write new and changed files)"
msgstr ""

#: cmd/build.py:227
msgid "don't use a saved environment, always read all files"
msgstr ""

#: cmd/build.py:230
msgid "path options"
msgstr ""

#: cmd/build.py:236
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr ""

#: cmd/build.py:246
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr ""

#: cmd/build.py:257
msgid "use no configuration file, only use settings from -D options"
msgstr ""

#: cmd/build.py:266
msgid "override a setting in configuration file"
msgstr ""

#: cmd/build.py:275
msgid "pass a value into HTML templates"
msgstr ""

#: cmd/build.py:284
msgid "define tag: include \"only\" blocks with TAG"
msgstr ""

#: cmd/build.py:291
msgid "nitpicky mode: warn about all missing references"
msgstr ""

#: cmd/build.py:294
msgid "console output options"
msgstr ""

#: cmd/build.py:301
msgid "increase verbosity (can be repeated)"
msgstr ""

#: cmd/build.py:308 ext/apidoc.py:413
msgid "no output on stdout, just warnings on stderr"
msgstr ""

#: cmd/build.py:315
msgid "no output at all, not even warnings"
msgstr ""

#: cmd/build.py:323
msgid "do emit colored output (default: auto-detect)"
msgstr ""

#: cmd/build.py:331
msgid "do not emit colored output (default: auto-detect)"
msgstr ""

#: cmd/build.py:334
msgid "warning control options"
msgstr ""

#: cmd/build.py:340
msgid "write warnings (and errors) to given file"
msgstr ""

#: cmd/build.py:347
msgid "turn warnings into errors"
msgstr ""

#: cmd/build.py:355
msgid "show full traceback on exception"
msgstr ""

#: cmd/build.py:358
msgid "run Pdb on exception"
msgstr ""

#: cmd/build.py:364
msgid "raise an exception on warnings"
msgstr ""

#: cmd/build.py:407
msgid "cannot combine -a option and filenames"
msgstr ""

#: cmd/build.py:439
#, python-format
msgid "cannot open warning file %r: %s"
msgstr ""

#: cmd/build.py:458
msgid "-D option argument must be in the form name=value"
msgstr ""

#: cmd/build.py:465
msgid "-A option argument must be in the form name=value"
msgstr ""

#: builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr ""

#: builders/linkcheck.py:60
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr ""

#: builders/linkcheck.py:137
#, python-format
msgid "broken link: %s (%s)"
msgstr ""

#: builders/linkcheck.py:526
#, python-format
msgid "Anchor '%s' not found"
msgstr ""

#: builders/linkcheck.py:726
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr ""

#: builders/singlehtml.py:36
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "صفحة الHTML موجودة في  %(outdir)s"

#: builders/singlehtml.py:168
msgid "assembling single document"
msgstr ""

#: builders/latex/__init__.py:349 builders/manpage.py:59
#: builders/singlehtml.py:173 builders/texinfo.py:120
msgid "writing"
msgstr ""

#: builders/singlehtml.py:186
msgid "writing additional files"
msgstr "كتابة ملفات إضافية"

#: builders/manpage.py:39
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr ""

#: builders/manpage.py:47
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr ""

#: builders/manpage.py:76
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr ""

#: builders/text.py:34
#, python-format
msgid "The text files are in %(outdir)s."
msgstr ""

#: builders/html/__init__.py:1213 builders/text.py:81 builders/xml.py:97
#, python-format
msgid "error writing file %s: %s"
msgstr ""

#: builders/xml.py:38
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "ملفات الXML موجودة في %(outdir)s"

#: builders/xml.py:110
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr ""

#: builders/texinfo.py:47
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr ""

#: builders/texinfo.py:49
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr ""

#: builders/texinfo.py:78
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr ""

#: builders/texinfo.py:90
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr ""

#: builders/latex/__init__.py:327 builders/texinfo.py:114
#, python-format
msgid "processing %s"
msgstr "معالجة %s"

#: builders/latex/__init__.py:407 builders/texinfo.py:173
msgid "resolving references..."
msgstr ""

#: builders/latex/__init__.py:418 builders/texinfo.py:183
msgid " (in "
msgstr ""

#: builders/_epub_base.py:421 builders/html/__init__.py:757
#: builders/latex/__init__.py:485 builders/texinfo.py:201
msgid "copying images... "
msgstr "نسخ الصور..."

#: builders/_epub_base.py:443 builders/latex/__init__.py:500
#: builders/texinfo.py:218
#, python-format
msgid "cannot copy image file %r: %s"
msgstr ""

#: builders/texinfo.py:225
msgid "copying Texinfo support files"
msgstr ""

#: builders/texinfo.py:233
#, python-format
msgid "error writing file Makefile: %s"
msgstr ""

#: builders/gettext.py:230
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr ""

#: builders/__init__.py:371 builders/gettext.py:251
#, python-format
msgid "building [%s]: "
msgstr "بناء [%s]"

#: builders/gettext.py:252
#, python-format
msgid "targets for %d template files"
msgstr ""

#: builders/gettext.py:257
msgid "reading templates... "
msgstr "قراءة القوالب"

#: builders/gettext.py:292
msgid "writing message catalogs... "
msgstr ""

#: builders/__init__.py:200
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr ""

#: builders/__init__.py:208
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr ""

#: builders/__init__.py:231
msgid "building [mo]: "
msgstr "بناء [mo]:"

#: builders/__init__.py:234 builders/__init__.py:729 builders/__init__.py:761
msgid "writing output... "
msgstr ""

#: builders/__init__.py:251
#, python-format
msgid "all of %d po files"
msgstr ""

#: builders/__init__.py:273
#, python-format
msgid "targets for %d po files that are specified"
msgstr ""

#: builders/__init__.py:285
#, python-format
msgid "targets for %d po files that are out of date"
msgstr ""

#: builders/__init__.py:295
msgid "all source files"
msgstr "جميع ملفات المصدر"

#: builders/__init__.py:307
#, python-format
msgid "file %r given on command line does not exist, "
msgstr ""

#: builders/__init__.py:313
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr ""

#: builders/__init__.py:324
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr ""

#: builders/__init__.py:339
#, python-format
msgid "%d source files given on command line"
msgstr ""

#: builders/__init__.py:354
#, python-format
msgid "targets for %d source files that are out of date"
msgstr ""

#: builders/__init__.py:382
msgid "looking for now-outdated files... "
msgstr ""

#: builders/__init__.py:386
#, python-format
msgid "%d found"
msgstr ""

#: builders/__init__.py:388
msgid "none found"
msgstr ""

#: builders/__init__.py:395
msgid "pickling environment"
msgstr ""

#: builders/__init__.py:402
msgid "checking consistency"
msgstr "التحقق من التوافق"

#: builders/__init__.py:406
msgid "no targets are out of date."
msgstr ""

#: builders/__init__.py:446
msgid "updating environment: "
msgstr "تحديث البيئة:"

#: builders/__init__.py:471
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr ""

#: builders/__init__.py:507
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches a "
"built-in exclude pattern %r. Please move your master document to a different"
" location."
msgstr ""

#: builders/__init__.py:516
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches an "
"exclude pattern specified in conf.py, %r. Please remove this pattern from "
"conf.py."
msgstr ""

#: builders/__init__.py:527
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it is not included"
" in the custom include_patterns = %r. Ensure that a pattern in "
"include_patterns matches the master document."
msgstr ""

#: builders/__init__.py:534
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s). The master document must "
"be within the source directory or a subdirectory of it."
msgstr ""

#: builders/__init__.py:553 builders/__init__.py:569
msgid "reading sources... "
msgstr ""

#: builders/__init__.py:686
#, python-format
msgid "docnames to write: %s"
msgstr ""

#: builders/__init__.py:699
msgid "preparing documents"
msgstr "تجهيز المستندات"

#: builders/__init__.py:702
msgid "copying assets"
msgstr ""

#: builders/__init__.py:845
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr ""

#: builders/epub3.py:83
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr ""

#: builders/epub3.py:189
msgid "writing nav.xhtml file..."
msgstr ""

#: builders/epub3.py:220
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr ""

#: builders/epub3.py:227
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr ""

#: builders/epub3.py:231
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr ""

#: builders/epub3.py:238
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr ""

#: builders/epub3.py:242
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr ""

#: builders/epub3.py:247
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr ""

#: builders/epub3.py:251
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr ""

#: builders/epub3.py:255
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr ""

#: builders/epub3.py:262
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr ""

#: builders/epub3.py:265
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr ""

#: builders/epub3.py:279 builders/html/__init__.py:1262
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr ""

#: builders/_epub_base.py:220
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr ""

#: builders/_epub_base.py:432
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr ""

#: builders/_epub_base.py:463
#, python-format
msgid "cannot write image file %r: %s"
msgstr ""

#: builders/_epub_base.py:475
msgid "Pillow not found - copying image files"
msgstr ""

#: builders/_epub_base.py:507
msgid "writing mimetype file..."
msgstr ""

#: builders/_epub_base.py:516
msgid "writing META-INF/container.xml file..."
msgstr ""

#: builders/_epub_base.py:553
msgid "writing content.opf file..."
msgstr ""

#: builders/_epub_base.py:585
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr ""

#: builders/_epub_base.py:756
msgid "writing toc.ncx file..."
msgstr ""

#: builders/_epub_base.py:785
#, python-format
msgid "writing %s file..."
msgstr ""

#: builders/changes.py:33
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr ""

#: builders/changes.py:60
#, python-format
msgid "no changes in version %s."
msgstr ""

#: builders/changes.py:62
msgid "writing summary file..."
msgstr ""

#: builders/changes.py:77
msgid "Builtins"
msgstr ""

#: builders/changes.py:79
msgid "Module level"
msgstr ""

#: builders/changes.py:131
msgid "copying source files..."
msgstr ""

#: builders/changes.py:140
#, python-format
msgid "could not read %r for changelog creation"
msgstr ""

#: util/rst.py:72
#, python-format
msgid "default role %s not found"
msgstr ""

#: util/docfields.py:95
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr ""

#: util/osutil.py:130
#, python-format
msgid ""
"Aborted attempted copy from %s to %s (the destination path has existing "
"data)."
msgstr ""

#: util/nodes.py:419
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr ""

#: util/nodes.py:487
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr ""

#: util/nodes.py:701
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr ""

#: util/fileutil.py:76
#, python-format
msgid ""
"Aborted attempted copy from rendered template %s to %s (the destination path"
" has existing data)."
msgstr ""

#: util/fileutil.py:91
#, python-format
msgid "Writing evaluated template result to %s"
msgstr ""

#: util/inventory.py:170
#, python-format
msgid "inventory <%s> contains duplicate definitions of %s"
msgstr ""

#: util/inventory.py:185
#, python-format
msgid "inventory <%s> contains multiple definitions for %s"
msgstr ""

#: util/docutils.py:283
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr ""

#: util/docutils.py:746
#, python-format
msgid "unknown node type: %r"
msgstr ""

#: util/display.py:83
msgid "skipped"
msgstr ""

#: util/display.py:88
msgid "failed"
msgstr "فشل"

#: util/i18n.py:105
#, python-format
msgid "reading error: %s, %s"
msgstr ""

#: util/i18n.py:112
#, python-format
msgid "writing error: %s, %s"
msgstr ""

#: util/i18n.py:141
#, python-format
msgid "locale_dir %s does not exist"
msgstr ""

#: util/i18n.py:236
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr ""

#: directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr ""

#: directives/code.py:63
msgid "non-whitespace stripped by dedent"
msgstr ""

#: directives/code.py:84
#, python-format
msgid "Invalid caption: %s"
msgstr ""

#: directives/code.py:129 directives/code.py:291 directives/code.py:478
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr ""

#: directives/code.py:211
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr ""

#: directives/code.py:225
#, python-format
msgid "Include file %r not found or reading it failed"
msgstr ""

#: directives/code.py:228
#, python-format
msgid ""
"Encoding %r used for reading included file %r seems to be wrong, try giving "
"an :encoding: option"
msgstr ""

#: directives/code.py:270
#, python-format
msgid "Object named %r not found in include file %r"
msgstr ""

#: directives/code.py:303
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr ""

#: directives/code.py:308
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr ""

#: directives/other.py:122
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr ""

#: directives/other.py:155 environment/adapters/toctree.py:355
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr ""

#: directives/other.py:158 environment/adapters/toctree.py:359
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr ""

#: directives/other.py:171
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr ""

#: directives/other.py:204
msgid "Section author: "
msgstr "مؤلف القسم:"

#: directives/other.py:206
msgid "Module author: "
msgstr ""

#: directives/other.py:208
msgid "Code author: "
msgstr "كاتب الكود:"

#: directives/other.py:210
msgid "Author: "
msgstr "المؤلف"

#: directives/other.py:284
msgid ".. acks content is not a list"
msgstr ""

#: directives/other.py:309
msgid ".. hlist content is not a list"
msgstr ""

#: _cli/__init__.py:73
msgid "Usage:"
msgstr ""

#: _cli/__init__.py:75
msgid "{0} [OPTIONS] <COMMAND> [<ARGS>]"
msgstr ""

#: _cli/__init__.py:78
msgid "  The Sphinx documentation generator."
msgstr ""

#: _cli/__init__.py:87
msgid "Commands:"
msgstr ""

#: _cli/__init__.py:98
msgid "Options"
msgstr ""

#: _cli/__init__.py:112 _cli/__init__.py:183
msgid "For more information, visit https://www.sphinx-doc.org/en/master/man/."
msgstr ""

#: _cli/__init__.py:172
msgid ""
"{0}: error: {1}\n"
"Run '{0} --help' for information"
msgstr ""

#: _cli/__init__.py:182
msgid "   Manage documentation with Sphinx."
msgstr ""

#: _cli/__init__.py:194
msgid "Show the version and exit."
msgstr ""

#: _cli/__init__.py:202
msgid "Show this message and exit."
msgstr ""

#: _cli/__init__.py:206
msgid "Logging"
msgstr ""

#: _cli/__init__.py:213
msgid "Increase verbosity (can be repeated)"
msgstr ""

#: _cli/__init__.py:221
msgid "Only print errors and warnings."
msgstr ""

#: _cli/__init__.py:228
msgid "No output at all"
msgstr ""

#: _cli/__init__.py:234
msgid "<command>"
msgstr ""

#: _cli/__init__.py:265
msgid "See 'sphinx --help'.\n"
msgstr ""

#: builders/html/__init__.py:478 builders/latex/__init__.py:201
#: transforms/__init__.py:133 writers/manpage.py:101 writers/texinfo.py:218
#, python-format
msgid "%b %d, %Y"
msgstr ""

#: transforms/__init__.py:143
msgid "could not calculate translation progress!"
msgstr ""

#: transforms/__init__.py:148
msgid "no translated elements!"
msgstr ""

#: transforms/__init__.py:267
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr ""

#: transforms/__init__.py:313
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr ""

#: transforms/__init__.py:322
msgid "Footnote [*] is not referenced."
msgstr ""

#: transforms/__init__.py:333
msgid "Footnote [#] is not referenced."
msgstr ""

#: transforms/i18n.py:229 transforms/i18n.py:304
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: transforms/i18n.py:274
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr ""

#: transforms/i18n.py:324
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: transforms/i18n.py:346
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: ext/linkcode.py:75 ext/viewcode.py:200
msgid "[source]"
msgstr "[المصدر]"

#: ext/imgconverter.py:40
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr ""

#: ext/imgconverter.py:49 ext/imgconverter.py:73
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: ext/imgconverter.py:68
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr ""

#: ext/viewcode.py:257
msgid "highlighting module code... "
msgstr ""

#: ext/viewcode.py:285
msgid "[docs]"
msgstr "[المستندات]"

#: ext/viewcode.py:305
msgid "Module code"
msgstr ""

#: ext/viewcode.py:311
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr ""

#: ext/viewcode.py:337
msgid "Overview: module code"
msgstr ""

#: ext/viewcode.py:338
msgid "<h1>All modules for which code is available</h1>"
msgstr ""

#: ext/coverage.py:47
#, python-format
msgid "invalid regex %r in %s"
msgstr ""

#: ext/coverage.py:134 ext/coverage.py:280
#, python-format
msgid "module %s could not be imported: %s"
msgstr ""

#: ext/coverage.py:141
#, python-format
msgid ""
"the following modules are documented but were not specified in "
"coverage_modules: %s"
msgstr ""

#: ext/coverage.py:149
msgid ""
"the following modules are specified in coverage_modules but were not "
"documented"
msgstr ""

#: ext/coverage.py:163
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr ""

#: ext/coverage.py:177
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr ""

#: ext/coverage.py:245
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr ""

#: ext/coverage.py:429
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr ""

#: ext/coverage.py:445
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr ""

#: ext/coverage.py:458
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr ""

#: ext/todo.py:71
msgid "Todo"
msgstr ""

#: ext/todo.py:104
#, python-format
msgid "TODO entry found: %s"
msgstr ""

#: ext/todo.py:163
msgid "<<original entry>>"
msgstr ""

#: ext/todo.py:165
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr ""

#: ext/todo.py:175
msgid "original entry"
msgstr ""

#: ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr ""

#: ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr ""

#: ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr ""

#: ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr ""

#: ext/doctest.py:220
msgid "invalid TestCode type"
msgstr ""

#: ext/doctest.py:281
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr ""

#: ext/doctest.py:434
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr ""

#: ext/doctest.py:522
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr ""

#: ext/graphviz.py:135
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr ""

#: ext/graphviz.py:145
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr ""

#: ext/graphviz.py:152
msgid "Ignoring \"graphviz\" directive without content."
msgstr ""

#: ext/graphviz.py:268
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr ""

#: ext/graphviz.py:303
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr ""

#: ext/graphviz.py:310
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: ext/graphviz.py:313
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: ext/graphviz.py:329
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr ""

#: ext/graphviz.py:333 ext/graphviz.py:386 ext/graphviz.py:423
#, python-format
msgid "dot code %r: %s"
msgstr ""

#: ext/graphviz.py:436 ext/graphviz.py:444
#, python-format
msgid "[graph: %s]"
msgstr ""

#: ext/graphviz.py:438 ext/graphviz.py:446
msgid "[graph]"
msgstr ""

#: ext/imgmath.py:369 ext/mathjax.py:52
msgid "Link to this equation"
msgstr ""

#: ext/apidoc.py:85
#, python-format
msgid "Would create file %s."
msgstr ""

#: ext/apidoc.py:375
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr ""

#: ext/apidoc.py:392
msgid "path to module to document"
msgstr ""

#: ext/apidoc.py:396
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr ""

#: ext/apidoc.py:407
msgid "directory to place all output"
msgstr ""

#: ext/apidoc.py:422
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr ""

#: ext/apidoc.py:429
msgid "overwrite existing files"
msgstr ""

#: ext/apidoc.py:437
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr ""

#: ext/apidoc.py:446
msgid "run the script without creating files"
msgstr ""

#: ext/apidoc.py:453
msgid "put documentation for each module on its own page"
msgstr ""

#: ext/apidoc.py:460
msgid "include \"_private\" modules"
msgstr ""

#: ext/apidoc.py:467
msgid "filename of table of contents (default: modules)"
msgstr ""

#: ext/apidoc.py:474
msgid "don't create a table of contents file"
msgstr ""

#: ext/apidoc.py:481
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr ""

#: ext/apidoc.py:492
msgid "put module documentation before submodule documentation"
msgstr ""

#: ext/apidoc.py:498
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr ""

#: ext/apidoc.py:508
msgid "file suffix (default: rst)"
msgstr ""

#: ext/apidoc.py:515 ext/autosummary/generate.py:838
msgid "Remove existing files in the output directory that were not generated"
msgstr ""

#: ext/apidoc.py:524
msgid "generate a full project with sphinx-quickstart"
msgstr ""

#: ext/apidoc.py:531
msgid "append module_path to sys.path, used when --full is given"
msgstr ""

#: ext/apidoc.py:538
msgid "project name (default: root module name)"
msgstr ""

#: ext/apidoc.py:545
msgid "project author(s), used when --full is given"
msgstr ""

#: ext/apidoc.py:552
msgid "project version, used when --full is given"
msgstr ""

#: ext/apidoc.py:559
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr ""

#: ext/apidoc.py:564
msgid "extension options"
msgstr ""

#: ext/apidoc.py:638
#, python-format
msgid "%s is not a directory."
msgstr "%s ليس مجلد."

#: ext/apidoc.py:710 ext/autosummary/generate.py:874
#, python-format
msgid "Failed to remove %s: %s"
msgstr ""

#: ext/autosectionlabel.py:48
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr ""

#: domains/std/__init__.py:702 domains/std/__init__.py:808
#: ext/autosectionlabel.py:52
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr ""

#: ext/duration.py:85
msgid ""
"====================== slowest reading durations ======================="
msgstr ""

#: ext/imgmath.py:159
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr ""

#: ext/imgmath.py:174
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr ""

#: ext/imgmath.py:328
#, python-format
msgid "display latex %r: %s"
msgstr ""

#: ext/imgmath.py:362
#, python-format
msgid "inline latex %r: %s"
msgstr ""

#: writers/latex.py:1093 writers/manpage.py:262 writers/texinfo.py:660
msgid "Footnotes"
msgstr ""

#: writers/manpage.py:308 writers/text.py:935
#, python-format
msgid "[image: %s]"
msgstr ""

#: writers/manpage.py:309 writers/text.py:936
msgid "[image]"
msgstr ""

#: writers/html5.py:99 writers/html5.py:108
msgid "Link to this definition"
msgstr ""

#: writers/html5.py:415
#, python-format
msgid "numfig_format is not defined for %s"
msgstr ""

#: writers/html5.py:427
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr ""

#: writers/html5.py:482
msgid "Link to this term"
msgstr ""

#: writers/html5.py:525 writers/html5.py:530
msgid "Link to this heading"
msgstr ""

#: writers/html5.py:535
msgid "Link to this table"
msgstr ""

#: writers/html5.py:549 writers/latex.py:1102
#, python-format
msgid "unsupported rubric heading level: %s"
msgstr ""

#: writers/html5.py:613
msgid "Link to this code"
msgstr ""

#: writers/html5.py:615
msgid "Link to this image"
msgstr ""

#: writers/html5.py:617
msgid "Link to this toctree"
msgstr ""

#: writers/html5.py:759
msgid "Could not obtain image size. :scale: option is ignored."
msgstr ""

#: builders/latex/__init__.py:208 domains/std/__init__.py:645
#: domains/std/__init__.py:657 templates/latex/latex.tex.jinja:106
#: themes/basic/genindex-single.html:22 themes/basic/genindex-single.html:48
#: themes/basic/genindex-split.html:3 themes/basic/genindex-split.html:6
#: themes/basic/genindex.html:3 themes/basic/genindex.html:26
#: themes/basic/genindex.html:59 themes/basic/layout.html:127
#: writers/texinfo.py:511
msgid "Index"
msgstr ""

#: writers/latex.py:746 writers/texinfo.py:642
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr ""

#: writers/texinfo.py:1214
msgid "caption not inside a figure."
msgstr ""

#: writers/texinfo.py:1300
#, python-format
msgid "unimplemented node type: %r"
msgstr ""

#: writers/latex.py:364
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr ""

#: builders/latex/__init__.py:226 writers/latex.py:414
#, python-format
msgid "no Babel option known for language %r"
msgstr ""

#: writers/latex.py:432
msgid "too large :maxdepth:, ignored."
msgstr ""

#: writers/latex.py:593
#, python-format
msgid "template %s not found; loading from legacy %s instead"
msgstr ""

#: writers/latex.py:711
msgid "document title is not a single Text node"
msgstr ""

#: writers/latex.py:1178
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr ""

#: writers/latex.py:1575
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr ""

#: writers/latex.py:1931
#, python-format
msgid "unknown index entry type %s found"
msgstr ""

#: domains/std/__init__.py:86 domains/std/__init__.py:103
#, python-format
msgid "environment variable; %s"
msgstr ""

#: domains/std/__init__.py:111
#, python-format
msgid "%s; configuration value"
msgstr ""

#: domains/std/__init__.py:165
msgid "Type"
msgstr ""

#: domains/std/__init__.py:175
msgid "Default"
msgstr ""

#: domains/std/__init__.py:234
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr ""

#: domains/std/__init__.py:305
#, python-format
msgid "%s command line option"
msgstr ""

#: domains/std/__init__.py:307
msgid "command line option"
msgstr ""

#: domains/std/__init__.py:429
msgid "glossary term must be preceded by empty line"
msgstr ""

#: domains/std/__init__.py:437
msgid "glossary terms must not be separated by empty lines"
msgstr ""

#: domains/std/__init__.py:443 domains/std/__init__.py:456
msgid "glossary seems to be misformatted, check indentation"
msgstr ""

#: domains/std/__init__.py:601
msgid "glossary term"
msgstr ""

#: domains/std/__init__.py:602
msgid "grammar token"
msgstr ""

#: domains/std/__init__.py:603
msgid "reference label"
msgstr ""

#: domains/std/__init__.py:606
msgid "environment variable"
msgstr ""

#: domains/std/__init__.py:607
msgid "program option"
msgstr ""

#: domains/std/__init__.py:608
msgid "document"
msgstr ""

#: domains/std/__init__.py:646 domains/std/__init__.py:658
msgid "Module Index"
msgstr ""

#: domains/std/__init__.py:647 domains/std/__init__.py:659
#: themes/basic/defindex.html:18
msgid "Search Page"
msgstr "صفحة البحث"

#: domains/std/__init__.py:721
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr ""

#: domains/std/__init__.py:926
msgid "numfig is disabled. :numref: is ignored."
msgstr ""

#: domains/std/__init__.py:934
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr ""

#: domains/std/__init__.py:946
#, python-format
msgid "the link has no caption: %s"
msgstr ""

#: domains/std/__init__.py:960
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr ""

#: domains/std/__init__.py:963
#, python-format
msgid "invalid numfig_format: %s"
msgstr ""

#: domains/std/__init__.py:1194
#, python-format
msgid "undefined label: %r"
msgstr ""

#: domains/std/__init__.py:1196
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr ""

#: domains/python/__init__.py:107 domains/python/__init__.py:244
#, python-format
msgid "%s() (in module %s)"
msgstr ""

#: domains/python/__init__.py:167 domains/python/__init__.py:334
#: domains/python/__init__.py:385 domains/python/__init__.py:424
#, python-format
msgid "%s (in module %s)"
msgstr ""

#: domains/python/__init__.py:169
#, python-format
msgid "%s (built-in variable)"
msgstr ""

#: domains/python/__init__.py:194
#, python-format
msgid "%s (built-in class)"
msgstr ""

#: domains/python/__init__.py:195
#, python-format
msgid "%s (class in %s)"
msgstr ""

#: domains/python/__init__.py:249
#, python-format
msgid "%s() (%s class method)"
msgstr ""

#: domains/python/__init__.py:251
#, python-format
msgid "%s() (%s static method)"
msgstr ""

#: domains/python/__init__.py:389
#, python-format
msgid "%s (%s property)"
msgstr ""

#: domains/python/__init__.py:428
#, python-format
msgid "%s (type alias in %s)"
msgstr ""

#: domains/python/__init__.py:557
msgid "Python Module Index"
msgstr ""

#: domains/python/__init__.py:558
msgid "modules"
msgstr ""

#: domains/python/__init__.py:607
msgid "Deprecated"
msgstr ""

#: domains/python/__init__.py:632
msgid "exception"
msgstr ""

#: domains/python/__init__.py:634
msgid "class method"
msgstr ""

#: domains/python/__init__.py:635
msgid "static method"
msgstr ""

#: domains/python/__init__.py:637
msgid "property"
msgstr ""

#: domains/python/__init__.py:638
msgid "type alias"
msgstr ""

#: domains/python/__init__.py:698
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr ""

#: domains/python/__init__.py:817
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr ""

#: domains/python/__init__.py:878
msgid " (deprecated)"
msgstr ""

#: domains/c/__init__.py:298 domains/cpp/__init__.py:436
#: domains/python/_object.py:164 ext/napoleon/docstring.py:786
msgid "Parameters"
msgstr ""

#: domains/python/_object.py:169
msgid "Variables"
msgstr "متغيرات"

#: domains/python/_object.py:173
msgid "Raises"
msgstr ""

#: domains/c/__init__.py:199
#, python-format
msgid "%s (C %s)"
msgstr ""

#: domains/c/__init__.py:260 domains/c/_symbol.py:510
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr ""

#: domains/c/__init__.py:301 domains/cpp/__init__.py:449
msgid "Return values"
msgstr ""

#: domains/c/__init__.py:673 domains/cpp/__init__.py:855
msgid "member"
msgstr ""

#: domains/c/__init__.py:674
msgid "variable"
msgstr "متغير"

#: domains/c/__init__.py:676
msgid "macro"
msgstr ""

#: domains/c/__init__.py:677
msgid "struct"
msgstr ""

#: domains/c/__init__.py:678 domains/cpp/__init__.py:853
msgid "union"
msgstr ""

#: domains/c/__init__.py:679 domains/cpp/__init__.py:858
msgid "enum"
msgstr ""

#: domains/c/__init__.py:680 domains/cpp/__init__.py:859
msgid "enumerator"
msgstr ""

#: domains/c/__init__.py:681 domains/cpp/__init__.py:856
msgid "type"
msgstr "نوع"

#: domains/c/__init__.py:683 domains/cpp/__init__.py:861
msgid "function parameter"
msgstr ""

#: domains/cpp/__init__.py:155
msgid "Template Parameters"
msgstr ""

#: domains/cpp/__init__.py:277
#, python-format
msgid "%s (C++ %s)"
msgstr ""

#: domains/cpp/__init__.py:360 domains/cpp/_symbol.py:793
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr ""

#: domains/cpp/__init__.py:857
msgid "concept"
msgstr ""

#: domains/cpp/__init__.py:862
msgid "template parameter"
msgstr ""

#: themes/haiku/layout.html:16
msgid "Contents"
msgstr "المحتوى"

#: themes/agogo/layout.html:29 themes/basic/globaltoc.html:2
#: themes/basic/localtoc.html:4 themes/scrolls/layout.html:32
msgid "Table of Contents"
msgstr "قائمة المحتويات"

#: themes/agogo/layout.html:34 themes/basic/layout.html:130
#: themes/basic/search.html:3 themes/basic/search.html:15
msgid "Search"
msgstr "بحث"

#: themes/agogo/layout.html:37 themes/basic/searchbox.html:8
#: themes/basic/searchfield.html:12
msgid "Go"
msgstr ""

#: themes/agogo/layout.html:81 themes/basic/sourcelink.html:7
msgid "Show Source"
msgstr "إظهار المصدر"

#: themes/classic/layout.html:12 themes/classic/static/sidebar.js.jinja:51
msgid "Collapse sidebar"
msgstr ""

#: themes/basic/layout.html:18
msgid "Navigation"
msgstr ""

#: themes/basic/layout.html:115
#, python-format
msgid "Search within %(docstitle)s"
msgstr "البحث ضمن %(docstitle)s"

#: themes/basic/layout.html:124
msgid "About these documents"
msgstr ""

#: themes/basic/layout.html:133 themes/basic/layout.html:177
#: themes/basic/layout.html:179
msgid "Copyright"
msgstr "الحقوق"

#: themes/basic/layout.html:183 themes/basic/layout.html:189
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr ""

#: themes/basic/layout.html:201
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr ""

#: themes/basic/layout.html:204
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr ""

#: themes/basic/genindex-single.html:26
#, python-format
msgid "Index &#x2013; %(key)s"
msgstr ""

#: themes/basic/genindex-single.html:54 themes/basic/genindex-split.html:16
#: themes/basic/genindex-split.html:30 themes/basic/genindex.html:65
msgid "Full index on one page"
msgstr ""

#: themes/basic/genindex-split.html:8
msgid "Index pages by letter"
msgstr ""

#: themes/basic/genindex-split.html:17
msgid "can be huge"
msgstr ""

#: themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "البحث %(docstitle)s"

#: themes/basic/sourcelink.html:4
msgid "This Page"
msgstr "هذه الصفحة"

#: themes/basic/defindex.html:4
msgid "Overview"
msgstr ""

#: themes/basic/defindex.html:8
msgid "Welcome! This is"
msgstr "أهلا وسهلا  هذا"

#: themes/basic/defindex.html:9
msgid "the documentation for"
msgstr "التوثيق ل"

#: themes/basic/defindex.html:10
msgid "last updated"
msgstr ""

#: themes/basic/defindex.html:13
msgid "Indices and tables:"
msgstr ""

#: themes/basic/defindex.html:16
msgid "Complete Table of Contents"
msgstr ""

#: themes/basic/defindex.html:17
msgid "lists all sections and subsections"
msgstr ""

#: themes/basic/defindex.html:19
msgid "search this documentation"
msgstr ""

#: themes/basic/defindex.html:21
msgid "Global Module Index"
msgstr ""

#: themes/basic/defindex.html:22
msgid "quick access to all modules"
msgstr ""

#: builders/html/__init__.py:499 themes/basic/defindex.html:23
msgid "General Index"
msgstr "الفهرس العام"

#: themes/basic/defindex.html:24
msgid "all functions, classes, terms"
msgstr ""

#: themes/basic/searchbox.html:4
msgid "Quick search"
msgstr "البحث السريع"

#: themes/basic/search.html:20
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr ""

#: themes/basic/search.html:28
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr ""

#: themes/basic/search.html:35
msgid "search"
msgstr "بحث"

#: themes/basic/relations.html:4
msgid "Previous topic"
msgstr "الموضوع السابق"

#: themes/basic/relations.html:6
msgid "previous chapter"
msgstr "القسم السابق"

#: themes/basic/relations.html:11
msgid "Next topic"
msgstr "الموضوع التالي"

#: themes/basic/relations.html:13
msgid "next chapter"
msgstr "الفصل التالي"

#: themes/classic/static/sidebar.js.jinja:42
msgid "Expand sidebar"
msgstr ""

#: themes/basic/changes/frameset.html:5
#: themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr ""

#: themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr ""

#: themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "تجهيز البحث"

#: themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr ""

#: themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr ""

#: themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr ""

#: themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr ""

#: themes/basic/static/searchtools.js:117
msgid "Search Results"
msgstr "نتائج البحث"

#: themes/basic/static/searchtools.js:119
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr ""

#: themes/basic/static/searchtools.js:123
msgid "Search finished, found one page matching the search query."
msgid_plural ""
"Search finished, found ${resultCount} pages matching the search query."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""
msgstr[5] ""

#: themes/basic/static/searchtools.js:253
msgid "Searching"
msgstr ""

#: themes/basic/static/searchtools.js:270
msgid "Preparing search..."
msgstr ""

#: themes/basic/static/searchtools.js:474
msgid ", in "
msgstr ""

#: environment/collectors/asset.py:95
#, python-format
msgid "image file not readable: %s"
msgstr ""

#: environment/collectors/asset.py:123
#, python-format
msgid "image file %s not readable: %s"
msgstr ""

#: environment/collectors/asset.py:160
#, python-format
msgid "download file not readable: %s"
msgstr ""

#: environment/collectors/toctree.py:258
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr ""

#: environment/adapters/toctree.py:318
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr ""

#: environment/adapters/toctree.py:342
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr ""

#: environment/adapters/toctree.py:357
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr ""

#: environment/adapters/indexentries.py:126
#, python-format
msgid "see %s"
msgstr ""

#: environment/adapters/indexentries.py:136
#, python-format
msgid "see also %s"
msgstr ""

#: environment/adapters/indexentries.py:144
#, python-format
msgid "unknown index entry type %r"
msgstr ""

#: environment/adapters/indexentries.py:273
#: templates/latex/sphinxmessages.sty.jinja:11
msgid "Symbols"
msgstr ""

#: builders/html/_build_info.py:32
msgid "failed to read broken build info file (unknown version)"
msgstr ""

#: builders/html/_build_info.py:36
msgid "failed to read broken build info file (missing config entry)"
msgstr ""

#: builders/html/_build_info.py:39
msgid "failed to read broken build info file (missing tags entry)"
msgstr ""

#: builders/html/__init__.py:113
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "صفحة الHTML موجودة في  %(outdir)s"

#: builders/html/__init__.py:340
#, python-format
msgid "Failed to read build info file: %r"
msgstr ""

#: builders/html/__init__.py:355
msgid "build_info mismatch, copying .buildinfo to .buildinfo.bak"
msgstr ""

#: builders/html/__init__.py:358
msgid "building [html]: "
msgstr ""

#: builders/html/__init__.py:374
#, python-format
msgid ""
"template %s has been changed since the previous build, all docs will be "
"rebuilt"
msgstr ""

#: builders/html/__init__.py:499
msgid "index"
msgstr "الفهرس"

#: builders/html/__init__.py:547
#, python-format
msgid "Logo of %s"
msgstr ""

#: builders/html/__init__.py:572
msgid "next"
msgstr "التالي"

#: builders/html/__init__.py:581
msgid "previous"
msgstr "السابق"

#: builders/html/__init__.py:678
msgid "generating indices"
msgstr "إنشاء الفهرس"

#: builders/html/__init__.py:693
msgid "writing additional pages"
msgstr "كتابة صفحات إضافية "

#: builders/html/__init__.py:772
#, python-format
msgid "cannot copy image file '%s': %s"
msgstr ""

#: builders/html/__init__.py:784
msgid "copying downloadable files... "
msgstr "نسخ الملفات القابلة للتحميل للنسخ..."

#: builders/html/__init__.py:796
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "غير قادر على نسخ الملفات القابلة للتحميل %r : %s"

#: builders/html/__init__.py:843
#, python-format
msgid "Failed to copy a file in the theme's 'static' directory: %s: %r"
msgstr ""

#: builders/html/__init__.py:861
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr ""

#: builders/html/__init__.py:896
msgid "copying static files"
msgstr ""

#: builders/html/__init__.py:912
#, python-format
msgid "cannot copy static file %r"
msgstr "غير قادر على نسخ الملف الثابت %r"

#: builders/html/__init__.py:917
msgid "copying extra files"
msgstr "نسخ ملفات إضافية"

#: builders/html/__init__.py:927
#, python-format
msgid "cannot copy extra file %r"
msgstr "غير قادر على نسخ المف الإضافي %r"

#: builders/html/__init__.py:933
#, python-format
msgid "Failed to write build info file: %r"
msgstr ""

#: builders/html/__init__.py:982
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr ""

#: builders/html/__init__.py:1027
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr ""

#: builders/html/__init__.py:1188
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr ""

#: builders/html/__init__.py:1197
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr ""

#: builders/html/__init__.py:1229
msgid "dumping object inventory"
msgstr ""

#: builders/html/__init__.py:1237
#, python-format
msgid "dumping search index in %s"
msgstr ""

#: builders/html/__init__.py:1279
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr ""

#: builders/html/__init__.py:1312
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr ""

#: builders/html/__init__.py:1317
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr ""

#: builders/html/__init__.py:1325
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr ""

#: builders/html/__init__.py:1332
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr ""

#: builders/html/__init__.py:1342
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr ""

#: builders/html/__init__.py:1349
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr ""

#: builders/html/__init__.py:1361 builders/latex/__init__.py:507
#, python-format
msgid "logo file %r does not exist"
msgstr "ملف الشعار %r غير موجود"

#: builders/html/__init__.py:1372
#, python-format
msgid "favicon file %r does not exist"
msgstr "ملف الايقونة %r غير موجود"

#: builders/html/__init__.py:1384
#, python-format
msgid ""
"Values in 'html_sidebars' must be a list of strings. At least one pattern "
"has a string value: %s. Change to `html_sidebars = %r`."
msgstr ""

#: builders/html/__init__.py:1397
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr ""

#: builders/html/__init__.py:1414
#, python-format
msgid "%s %s documentation"
msgstr ""

#: builders/latex/transforms.py:118
msgid "Failed to get a docname!"
msgstr ""

#: builders/latex/transforms.py:119
#, python-format
msgid "Failed to get a docname for source %r!"
msgstr ""

#: builders/latex/transforms.py:485
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr ""

#: builders/latex/__init__.py:117
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr ""

#: builders/latex/__init__.py:119
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr ""

#: builders/latex/__init__.py:157
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr ""

#: builders/latex/__init__.py:169
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr ""

#: builders/latex/__init__.py:211 templates/latex/latex.tex.jinja:91
msgid "Release"
msgstr ""

#: builders/latex/__init__.py:432
msgid "copying TeX support files"
msgstr ""

#: builders/latex/__init__.py:469
msgid "copying additional files"
msgstr ""

#: builders/latex/__init__.py:543
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr ""

#: builders/latex/__init__.py:551
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr ""

#: builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr ""

#: builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr ""

#: _cli/util/errors.py:124
msgid "Exception occurred, starting debugger:"
msgstr ""

#: _cli/util/errors.py:133
msgid "reStructuredText markup error:"
msgstr ""

#: _cli/util/errors.py:168
msgid "The full traceback has been saved in:"
msgstr ""

#: _cli/util/errors.py:172
msgid ""
"To report this error to the developers, please open an issue at "
"<https://github.com/sphinx-doc/sphinx/issues/>. Thanks!"
msgstr ""

#: transforms/post_transforms/__init__.py:124
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr ""

#: transforms/post_transforms/__init__.py:184
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr ""

#: transforms/post_transforms/__init__.py:250
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr ""

#: transforms/post_transforms/__init__.py:256
#, python-format
msgid "%r reference target not found: %s"
msgstr ""

#: transforms/post_transforms/images.py:77
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr ""

#: transforms/post_transforms/images.py:94
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr ""

#: transforms/post_transforms/images.py:141
#, python-format
msgid "Unknown image format: %s..."
msgstr ""

#: ext/napoleon/docstring.py:707
msgid "Example"
msgstr "مثال"

#: ext/napoleon/docstring.py:708
msgid "Examples"
msgstr "أمثلة"

#: ext/napoleon/__init__.py:344 ext/napoleon/docstring.py:752
msgid "Keyword Arguments"
msgstr ""

#: ext/napoleon/docstring.py:768
msgid "Notes"
msgstr "ملاحظات"

#: ext/napoleon/docstring.py:777
msgid "Other Parameters"
msgstr ""

#: ext/napoleon/docstring.py:813
msgid "Receives"
msgstr ""

#: ext/napoleon/docstring.py:817
msgid "References"
msgstr "مراجع"

#: ext/napoleon/docstring.py:849
msgid "Warns"
msgstr ""

#: ext/napoleon/docstring.py:853
msgid "Yields"
msgstr ""

#: ext/napoleon/docstring.py:1015
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr ""

#: ext/napoleon/docstring.py:1022
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr ""

#: ext/napoleon/docstring.py:1029
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr ""

#: ext/napoleon/docstring.py:1036
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr ""

#: ext/autosummary/__init__.py:255
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr ""

#: ext/autosummary/__init__.py:257
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr ""

#: ext/autosummary/__init__.py:276
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr ""

#: ext/autosummary/__init__.py:329
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: ext/autosummary/__init__.py:343
#, python-format
msgid "failed to parse name %s"
msgstr ""

#: ext/autosummary/__init__.py:348
#, python-format
msgid "failed to import object %s"
msgstr ""

#: ext/autosummary/__init__.py:647
#, python-format
msgid ""
"Summarised items should not include the current module. Replace %r with %r."
msgstr ""

#: ext/autosummary/__init__.py:818
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr ""

#: ext/autosummary/__init__.py:826
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr ""

#: ext/autosummary/generate.py:214 ext/autosummary/generate.py:390
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: ext/autosummary/generate.py:525
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr ""

#: ext/autosummary/generate.py:529
#, python-format
msgid "[autosummary] writing to %s"
msgstr ""

#: ext/autosummary/generate.py:571
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: ext/autosummary/generate.py:766
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr ""

#: ext/autosummary/generate.py:788
msgid "source files to generate rST files for"
msgstr ""

#: ext/autosummary/generate.py:796
msgid "directory to place all output in"
msgstr ""

#: ext/autosummary/generate.py:804
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr ""

#: ext/autosummary/generate.py:812
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr ""

#: ext/autosummary/generate.py:820
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr ""

#: ext/autosummary/generate.py:828
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr ""

#: ext/intersphinx/_resolve.py:47
#, python-format
msgid "(in %s v%s)"
msgstr ""

#: ext/intersphinx/_resolve.py:49
#, python-format
msgid "(in %s)"
msgstr ""

#: ext/intersphinx/_resolve.py:103
#, python-format
msgid "inventory '%s': duplicate matches found for %s:%s"
msgstr ""

#: ext/intersphinx/_resolve.py:113
#, python-format
msgid "inventory '%s': multiple matches found for %s:%s"
msgstr ""

#: ext/intersphinx/_resolve.py:359
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:367
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:378
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:585
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr ""

#: ext/intersphinx/_load.py:59
#, python-format
msgid ""
"Invalid intersphinx project identifier `%r` in intersphinx_mapping. Project "
"identifiers must be non-empty strings."
msgstr ""

#: ext/intersphinx/_load.py:70
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Expected a two-element tuple "
"or list."
msgstr ""

#: ext/intersphinx/_load.py:81
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Values must be a (target URI,"
" inventory locations) pair."
msgstr ""

#: ext/intersphinx/_load.py:92
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique non-empty strings."
msgstr ""

#: ext/intersphinx/_load.py:101
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique (other instance in intersphinx_mapping[%r])."
msgstr ""

#: ext/intersphinx/_load.py:120
#, python-format
msgid ""
"Invalid inventory location value `%r` in intersphinx_mapping[%r][1]. "
"Inventory locations must be non-empty strings or None."
msgstr ""

#: ext/intersphinx/_load.py:131
msgid "Invalid `intersphinx_mapping` configuration (1 error)."
msgstr ""

#: ext/intersphinx/_load.py:134
#, python-format
msgid "Invalid `intersphinx_mapping` configuration (%s errors)."
msgstr ""

#: ext/intersphinx/_load.py:155
msgid "An invalid intersphinx_mapping entry was added after normalisation."
msgstr ""

#: ext/intersphinx/_load.py:240
#, python-format
msgid "loading intersphinx inventory '%s' from %s ..."
msgstr ""

#: ext/intersphinx/_load.py:265
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr ""

#: ext/intersphinx/_load.py:275
msgid "failed to reach any of the inventories with the following issues:"
msgstr ""

#: ext/intersphinx/_load.py:319
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr ""

#: ext/autodoc/type_comment.py:132
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr ""

#: ext/autodoc/type_comment.py:135
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr ""

#: ext/autodoc/__init__.py:141
#, python-format
msgid "invalid value for member-order option: %s"
msgstr ""

#: ext/autodoc/__init__.py:149
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr ""

#: ext/autodoc/__init__.py:408
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr ""

#: ext/autodoc/__init__.py:525
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:795
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: ext/autodoc/__init__.py:890
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr ""

#: ext/autodoc/__init__.py:934
#, python-format
msgid "A mocked object is detected: %r"
msgstr ""

#: ext/autodoc/__init__.py:953
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:1016
msgid "\"::\" in automodule name doesn't make sense"
msgstr ""

#: ext/autodoc/__init__.py:1023
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr ""

#: ext/autodoc/__init__.py:1036
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr ""

#: ext/autodoc/__init__.py:1102
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr ""

#: ext/autodoc/__init__.py:1325 ext/autodoc/__init__.py:1402
#: ext/autodoc/__init__.py:2810
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:1616
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:1743
#, python-format
msgid "Bases: %s"
msgstr ""

#: ext/autodoc/__init__.py:1757
#, python-format
msgid "missing attribute %s in object %s"
msgstr ""

#: ext/autodoc/__init__.py:1838 ext/autodoc/__init__.py:1875
#: ext/autodoc/__init__.py:1970
#, python-format
msgid "alias of %s"
msgstr ""

#: ext/autodoc/__init__.py:1858
#, python-format
msgid "alias of TypeVar(%s)"
msgstr ""

#: ext/autodoc/__init__.py:2198 ext/autodoc/__init__.py:2298
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr ""

#: ext/autodoc/__init__.py:2429
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr ""

#: ext/autodoc/preserve_defaults.py:190
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr ""

#: templates/latex/longtable.tex.jinja:52
#: templates/latex/sphinxmessages.sty.jinja:8
msgid "continued from previous page"
msgstr ""

#: templates/latex/longtable.tex.jinja:63
#: templates/latex/sphinxmessages.sty.jinja:9
msgid "continues on next page"
msgstr "استمرار في الصفحة التالية"

#: templates/latex/sphinxmessages.sty.jinja:10
msgid "Non-alphabetical"
msgstr ""

#: templates/latex/sphinxmessages.sty.jinja:12
msgid "Numbers"
msgstr "أرقام"

#: templates/latex/sphinxmessages.sty.jinja:13
msgid "page"
msgstr "صفحة"
